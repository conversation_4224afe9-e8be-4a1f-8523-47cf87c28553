"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bb418ee37d87\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImJiNDE4ZWUzN2Q4N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // For now, we'll use the default categories\n            // Later we'll load from Appwrite database\n            setCategories(defaultCategories);\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-white mb-8 text-center\",\n                    children: \"SXE Vision Board\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"Ku/3fYTZ4p+HhLbl/Ex0fsiHh1U=\");\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/database.js":
/*!*****************************!*\
  !*** ./src/lib/database.js ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   DATABASE_ID: () => (/* binding */ DATABASE_ID),\n/* harmony export */   categoryService: () => (/* binding */ categoryService),\n/* harmony export */   goalService: () => (/* binding */ goalService),\n/* harmony export */   setupDatabase: () => (/* binding */ setupDatabase),\n/* harmony export */   userPreferencesService: () => (/* binding */ userPreferencesService)\n/* harmony export */ });\n/* harmony import */ var _appwrite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./appwrite */ \"(app-pages-browser)/./src/lib/appwrite.js\");\n/* harmony import */ var appwrite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! appwrite */ \"(app-pages-browser)/./node_modules/appwrite/dist/esm/sdk.js\");\n\n\n// Database and Collection IDs\nconst DATABASE_ID = \"sxe_vision_board\";\nconst COLLECTIONS = {\n    CATEGORIES: \"categories\",\n    GOALS: \"goals\",\n    USER_PREFERENCES: \"user_preferences\"\n};\n// Database schema setup\nconst setupDatabase = async ()=>{\n    try {\n        // Create database\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.create(DATABASE_ID, \"SXE Vision Board\");\n        console.log(\"Database created successfully\");\n        // Create Categories collection\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createCollection(DATABASE_ID, COLLECTIONS.CATEGORIES, \"Categories\", [\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.read(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.create(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.update(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.delete(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\"))\n        ]);\n        // Create attributes for Categories collection\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, \"name\", 100, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, \"color\", 50, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, \"userId\", 50, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, \"order\", true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, \"createdAt\", true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, \"updatedAt\", true);\n        // Create Goals collection\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createCollection(DATABASE_ID, COLLECTIONS.GOALS, \"Goals\", [\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.read(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.create(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.update(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.delete(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\"))\n        ]);\n        // Create attributes for Goals collection\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"title\", 200, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"description\", 1000, false);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"icon\", 10, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createEnumAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"status\", [\n            \"not-started\",\n            \"in-progress\",\n            \"done\",\n            \"not-good\"\n        ], true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"categoryId\", 50, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"userId\", 50, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"order\", true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"createdAt\", true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"updatedAt\", true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, \"targetDate\", false);\n        // Create User Preferences collection\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createCollection(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"User Preferences\", [\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.read(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.create(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.update(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\")),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Permission.delete(appwrite__WEBPACK_IMPORTED_MODULE_1__.Role.user(\"user\"))\n        ]);\n        // Create attributes for User Preferences collection\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"userId\", 50, true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"theme\", 50, false);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"aiSuggestions\", false);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"preferences\", 2000, false);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"createdAt\", true);\n        await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, \"updatedAt\", true);\n        console.log(\"Database schema created successfully\");\n    } catch (error) {\n        console.error(\"Error setting up database:\", error);\n        throw error;\n    }\n};\n// CRUD operations for Categories\nconst categoryService = {\n    async create (userId, categoryData) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDocument(DATABASE_ID, COLLECTIONS.CATEGORIES, appwrite__WEBPACK_IMPORTED_MODULE_1__.ID.unique(), {\n            ...categoryData,\n            userId,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        });\n    },\n    async list (userId) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.listDocuments(DATABASE_ID, COLLECTIONS.CATEGORIES, [\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Query.equal(\"userId\", userId),\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Query.orderAsc(\"order\")\n        ]);\n    },\n    async update (categoryId, updates) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.updateDocument(DATABASE_ID, COLLECTIONS.CATEGORIES, categoryId, {\n            ...updates,\n            updatedAt: new Date().toISOString()\n        });\n    },\n    async delete (categoryId) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.deleteDocument(DATABASE_ID, COLLECTIONS.CATEGORIES, categoryId);\n    }\n};\n// CRUD operations for Goals\nconst goalService = {\n    async create (userId, goalData) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDocument(DATABASE_ID, COLLECTIONS.GOALS, appwrite__WEBPACK_IMPORTED_MODULE_1__.ID.unique(), {\n            ...goalData,\n            userId,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        });\n    },\n    async list (userId) {\n        let categoryId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        const queries = [\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Query.equal(\"userId\", userId)\n        ];\n        if (categoryId) {\n            queries.push(appwrite__WEBPACK_IMPORTED_MODULE_1__.Query.equal(\"categoryId\", categoryId));\n        }\n        queries.push(appwrite__WEBPACK_IMPORTED_MODULE_1__.Query.orderAsc(\"order\"));\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.listDocuments(DATABASE_ID, COLLECTIONS.GOALS, queries);\n    },\n    async update (goalId, updates) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.updateDocument(DATABASE_ID, COLLECTIONS.GOALS, goalId, {\n            ...updates,\n            updatedAt: new Date().toISOString()\n        });\n    },\n    async delete (goalId) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.deleteDocument(DATABASE_ID, COLLECTIONS.GOALS, goalId);\n    }\n};\n// CRUD operations for User Preferences\nconst userPreferencesService = {\n    async create (userId, preferences) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.createDocument(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, appwrite__WEBPACK_IMPORTED_MODULE_1__.ID.unique(), {\n            userId,\n            ...preferences,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        });\n    },\n    async get (userId) {\n        const result = await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.listDocuments(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, [\n            appwrite__WEBPACK_IMPORTED_MODULE_1__.Query.equal(\"userId\", userId)\n        ]);\n        return result.documents[0] || null;\n    },\n    async update (preferencesId, updates) {\n        return await _appwrite__WEBPACK_IMPORTED_MODULE_0__.databases.updateDocument(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, preferencesId, {\n            ...updates,\n            updatedAt: new Date().toISOString()\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZGF0YWJhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBdUM7QUFDZ0I7QUFFdkQsOEJBQThCO0FBQ3ZCLE1BQU1LLGNBQWMsbUJBQW1CO0FBQ3ZDLE1BQU1DLGNBQWM7SUFDekJDLFlBQVk7SUFDWkMsT0FBTztJQUNQQyxrQkFBa0I7QUFDcEIsRUFBRTtBQUVGLHdCQUF3QjtBQUNqQixNQUFNQyxnQkFBZ0I7SUFDM0IsSUFBSTtRQUNGLGtCQUFrQjtRQUNsQixNQUFNVixnREFBU0EsQ0FBQ1csTUFBTSxDQUFDTixhQUFhO1FBQ3BDTyxRQUFRQyxHQUFHLENBQUM7UUFFWiwrQkFBK0I7UUFDL0IsTUFBTWIsZ0RBQVNBLENBQUNjLGdCQUFnQixDQUM5QlQsYUFDQUMsWUFBWUMsVUFBVSxFQUN0QixjQUNBO1lBQ0VMLGdEQUFVQSxDQUFDYSxJQUFJLENBQUNaLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7WUFDMUJkLGdEQUFVQSxDQUFDUyxNQUFNLENBQUNSLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7WUFDNUJkLGdEQUFVQSxDQUFDZSxNQUFNLENBQUNkLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7WUFDNUJkLGdEQUFVQSxDQUFDZ0IsTUFBTSxDQUFDZiwwQ0FBSUEsQ0FBQ2EsSUFBSSxDQUFDO1NBQzdCO1FBR0gsOENBQThDO1FBQzlDLE1BQU1oQixnREFBU0EsQ0FBQ21CLHFCQUFxQixDQUFDZCxhQUFhQyxZQUFZQyxVQUFVLEVBQUUsUUFBUSxLQUFLO1FBQ3hGLE1BQU1QLGdEQUFTQSxDQUFDbUIscUJBQXFCLENBQUNkLGFBQWFDLFlBQVlDLFVBQVUsRUFBRSxTQUFTLElBQUk7UUFDeEYsTUFBTVAsZ0RBQVNBLENBQUNtQixxQkFBcUIsQ0FBQ2QsYUFBYUMsWUFBWUMsVUFBVSxFQUFFLFVBQVUsSUFBSTtRQUN6RixNQUFNUCxnREFBU0EsQ0FBQ29CLHNCQUFzQixDQUFDZixhQUFhQyxZQUFZQyxVQUFVLEVBQUUsU0FBUztRQUNyRixNQUFNUCxnREFBU0EsQ0FBQ3FCLHVCQUF1QixDQUFDaEIsYUFBYUMsWUFBWUMsVUFBVSxFQUFFLGFBQWE7UUFDMUYsTUFBTVAsZ0RBQVNBLENBQUNxQix1QkFBdUIsQ0FBQ2hCLGFBQWFDLFlBQVlDLFVBQVUsRUFBRSxhQUFhO1FBRTFGLDBCQUEwQjtRQUMxQixNQUFNUCxnREFBU0EsQ0FBQ2MsZ0JBQWdCLENBQzlCVCxhQUNBQyxZQUFZRSxLQUFLLEVBQ2pCLFNBQ0E7WUFDRU4sZ0RBQVVBLENBQUNhLElBQUksQ0FBQ1osMENBQUlBLENBQUNhLElBQUksQ0FBQztZQUMxQmQsZ0RBQVVBLENBQUNTLE1BQU0sQ0FBQ1IsMENBQUlBLENBQUNhLElBQUksQ0FBQztZQUM1QmQsZ0RBQVVBLENBQUNlLE1BQU0sQ0FBQ2QsMENBQUlBLENBQUNhLElBQUksQ0FBQztZQUM1QmQsZ0RBQVVBLENBQUNnQixNQUFNLENBQUNmLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7U0FDN0I7UUFHSCx5Q0FBeUM7UUFDekMsTUFBTWhCLGdEQUFTQSxDQUFDbUIscUJBQXFCLENBQUNkLGFBQWFDLFlBQVlFLEtBQUssRUFBRSxTQUFTLEtBQUs7UUFDcEYsTUFBTVIsZ0RBQVNBLENBQUNtQixxQkFBcUIsQ0FBQ2QsYUFBYUMsWUFBWUUsS0FBSyxFQUFFLGVBQWUsTUFBTTtRQUMzRixNQUFNUixnREFBU0EsQ0FBQ21CLHFCQUFxQixDQUFDZCxhQUFhQyxZQUFZRSxLQUFLLEVBQUUsUUFBUSxJQUFJO1FBQ2xGLE1BQU1SLGdEQUFTQSxDQUFDc0IsbUJBQW1CLENBQ2pDakIsYUFDQUMsWUFBWUUsS0FBSyxFQUNqQixVQUNBO1lBQUM7WUFBZTtZQUFlO1lBQVE7U0FBVyxFQUNsRDtRQUVGLE1BQU1SLGdEQUFTQSxDQUFDbUIscUJBQXFCLENBQUNkLGFBQWFDLFlBQVlFLEtBQUssRUFBRSxjQUFjLElBQUk7UUFDeEYsTUFBTVIsZ0RBQVNBLENBQUNtQixxQkFBcUIsQ0FBQ2QsYUFBYUMsWUFBWUUsS0FBSyxFQUFFLFVBQVUsSUFBSTtRQUNwRixNQUFNUixnREFBU0EsQ0FBQ29CLHNCQUFzQixDQUFDZixhQUFhQyxZQUFZRSxLQUFLLEVBQUUsU0FBUztRQUNoRixNQUFNUixnREFBU0EsQ0FBQ3FCLHVCQUF1QixDQUFDaEIsYUFBYUMsWUFBWUUsS0FBSyxFQUFFLGFBQWE7UUFDckYsTUFBTVIsZ0RBQVNBLENBQUNxQix1QkFBdUIsQ0FBQ2hCLGFBQWFDLFlBQVlFLEtBQUssRUFBRSxhQUFhO1FBQ3JGLE1BQU1SLGdEQUFTQSxDQUFDcUIsdUJBQXVCLENBQUNoQixhQUFhQyxZQUFZRSxLQUFLLEVBQUUsY0FBYztRQUV0RixxQ0FBcUM7UUFDckMsTUFBTVIsZ0RBQVNBLENBQUNjLGdCQUFnQixDQUM5QlQsYUFDQUMsWUFBWUcsZ0JBQWdCLEVBQzVCLG9CQUNBO1lBQ0VQLGdEQUFVQSxDQUFDYSxJQUFJLENBQUNaLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7WUFDMUJkLGdEQUFVQSxDQUFDUyxNQUFNLENBQUNSLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7WUFDNUJkLGdEQUFVQSxDQUFDZSxNQUFNLENBQUNkLDBDQUFJQSxDQUFDYSxJQUFJLENBQUM7WUFDNUJkLGdEQUFVQSxDQUFDZ0IsTUFBTSxDQUFDZiwwQ0FBSUEsQ0FBQ2EsSUFBSSxDQUFDO1NBQzdCO1FBR0gsb0RBQW9EO1FBQ3BELE1BQU1oQixnREFBU0EsQ0FBQ21CLHFCQUFxQixDQUFDZCxhQUFhQyxZQUFZRyxnQkFBZ0IsRUFBRSxVQUFVLElBQUk7UUFDL0YsTUFBTVQsZ0RBQVNBLENBQUNtQixxQkFBcUIsQ0FBQ2QsYUFBYUMsWUFBWUcsZ0JBQWdCLEVBQUUsU0FBUyxJQUFJO1FBQzlGLE1BQU1ULGdEQUFTQSxDQUFDdUIsc0JBQXNCLENBQUNsQixhQUFhQyxZQUFZRyxnQkFBZ0IsRUFBRSxpQkFBaUI7UUFDbkcsTUFBTVQsZ0RBQVNBLENBQUNtQixxQkFBcUIsQ0FBQ2QsYUFBYUMsWUFBWUcsZ0JBQWdCLEVBQUUsZUFBZSxNQUFNO1FBQ3RHLE1BQU1ULGdEQUFTQSxDQUFDcUIsdUJBQXVCLENBQUNoQixhQUFhQyxZQUFZRyxnQkFBZ0IsRUFBRSxhQUFhO1FBQ2hHLE1BQU1ULGdEQUFTQSxDQUFDcUIsdUJBQXVCLENBQUNoQixhQUFhQyxZQUFZRyxnQkFBZ0IsRUFBRSxhQUFhO1FBRWhHRyxRQUFRQyxHQUFHLENBQUM7SUFDZCxFQUFFLE9BQU9XLE9BQU87UUFDZFosUUFBUVksS0FBSyxDQUFDLDhCQUE4QkE7UUFDNUMsTUFBTUE7SUFDUjtBQUNGLEVBQUU7QUFFRixpQ0FBaUM7QUFDMUIsTUFBTUMsa0JBQWtCO0lBQzdCLE1BQU1kLFFBQU9lLE1BQU0sRUFBRUMsWUFBWTtRQUMvQixPQUFPLE1BQU0zQixnREFBU0EsQ0FBQzRCLGNBQWMsQ0FDbkN2QixhQUNBQyxZQUFZQyxVQUFVLEVBQ3RCTix3Q0FBRUEsQ0FBQzRCLE1BQU0sSUFDVDtZQUNFLEdBQUdGLFlBQVk7WUFDZkQ7WUFDQUksV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ2pDQyxXQUFXLElBQUlGLE9BQU9DLFdBQVc7UUFDbkM7SUFFSjtJQUVBLE1BQU1FLE1BQUtSLE1BQU07UUFDZixPQUFPLE1BQU0xQixnREFBU0EsQ0FBQ21DLGFBQWEsQ0FDbEM5QixhQUNBQyxZQUFZQyxVQUFVLEVBQ3RCO1lBQ0VILDJDQUFLQSxDQUFDZ0MsS0FBSyxDQUFDLFVBQVVWO1lBQ3RCdEIsMkNBQUtBLENBQUNpQyxRQUFRLENBQUM7U0FDaEI7SUFFTDtJQUVBLE1BQU1wQixRQUFPcUIsVUFBVSxFQUFFQyxPQUFPO1FBQzlCLE9BQU8sTUFBTXZDLGdEQUFTQSxDQUFDd0MsY0FBYyxDQUNuQ25DLGFBQ0FDLFlBQVlDLFVBQVUsRUFDdEIrQixZQUNBO1lBQ0UsR0FBR0MsT0FBTztZQUNWTixXQUFXLElBQUlGLE9BQU9DLFdBQVc7UUFDbkM7SUFFSjtJQUVBLE1BQU1kLFFBQU9vQixVQUFVO1FBQ3JCLE9BQU8sTUFBTXRDLGdEQUFTQSxDQUFDeUMsY0FBYyxDQUNuQ3BDLGFBQ0FDLFlBQVlDLFVBQVUsRUFDdEIrQjtJQUVKO0FBQ0YsRUFBRTtBQUVGLDRCQUE0QjtBQUNyQixNQUFNSSxjQUFjO0lBQ3pCLE1BQU0vQixRQUFPZSxNQUFNLEVBQUVpQixRQUFRO1FBQzNCLE9BQU8sTUFBTTNDLGdEQUFTQSxDQUFDNEIsY0FBYyxDQUNuQ3ZCLGFBQ0FDLFlBQVlFLEtBQUssRUFDakJQLHdDQUFFQSxDQUFDNEIsTUFBTSxJQUNUO1lBQ0UsR0FBR2MsUUFBUTtZQUNYakI7WUFDQUksV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ2pDQyxXQUFXLElBQUlGLE9BQU9DLFdBQVc7UUFDbkM7SUFFSjtJQUVBLE1BQU1FLE1BQUtSLE1BQU07WUFBRVksYUFBQUEsaUVBQWE7UUFDOUIsTUFBTU0sVUFBVTtZQUFDeEMsMkNBQUtBLENBQUNnQyxLQUFLLENBQUMsVUFBVVY7U0FBUTtRQUMvQyxJQUFJWSxZQUFZO1lBQ2RNLFFBQVFDLElBQUksQ0FBQ3pDLDJDQUFLQSxDQUFDZ0MsS0FBSyxDQUFDLGNBQWNFO1FBQ3pDO1FBQ0FNLFFBQVFDLElBQUksQ0FBQ3pDLDJDQUFLQSxDQUFDaUMsUUFBUSxDQUFDO1FBRTVCLE9BQU8sTUFBTXJDLGdEQUFTQSxDQUFDbUMsYUFBYSxDQUNsQzlCLGFBQ0FDLFlBQVlFLEtBQUssRUFDakJvQztJQUVKO0lBRUEsTUFBTTNCLFFBQU82QixNQUFNLEVBQUVQLE9BQU87UUFDMUIsT0FBTyxNQUFNdkMsZ0RBQVNBLENBQUN3QyxjQUFjLENBQ25DbkMsYUFDQUMsWUFBWUUsS0FBSyxFQUNqQnNDLFFBQ0E7WUFDRSxHQUFHUCxPQUFPO1lBQ1ZOLFdBQVcsSUFBSUYsT0FBT0MsV0FBVztRQUNuQztJQUVKO0lBRUEsTUFBTWQsUUFBTzRCLE1BQU07UUFDakIsT0FBTyxNQUFNOUMsZ0RBQVNBLENBQUN5QyxjQUFjLENBQ25DcEMsYUFDQUMsWUFBWUUsS0FBSyxFQUNqQnNDO0lBRUo7QUFDRixFQUFFO0FBRUYsdUNBQXVDO0FBQ2hDLE1BQU1DLHlCQUF5QjtJQUNwQyxNQUFNcEMsUUFBT2UsTUFBTSxFQUFFc0IsV0FBVztRQUM5QixPQUFPLE1BQU1oRCxnREFBU0EsQ0FBQzRCLGNBQWMsQ0FDbkN2QixhQUNBQyxZQUFZRyxnQkFBZ0IsRUFDNUJSLHdDQUFFQSxDQUFDNEIsTUFBTSxJQUNUO1lBQ0VIO1lBQ0EsR0FBR3NCLFdBQVc7WUFDZGxCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNqQ0MsV0FBVyxJQUFJRixPQUFPQyxXQUFXO1FBQ25DO0lBRUo7SUFFQSxNQUFNaUIsS0FBSXZCLE1BQU07UUFDZCxNQUFNd0IsU0FBUyxNQUFNbEQsZ0RBQVNBLENBQUNtQyxhQUFhLENBQzFDOUIsYUFDQUMsWUFBWUcsZ0JBQWdCLEVBQzVCO1lBQUNMLDJDQUFLQSxDQUFDZ0MsS0FBSyxDQUFDLFVBQVVWO1NBQVE7UUFFakMsT0FBT3dCLE9BQU9DLFNBQVMsQ0FBQyxFQUFFLElBQUk7SUFDaEM7SUFFQSxNQUFNbEMsUUFBT21DLGFBQWEsRUFBRWIsT0FBTztRQUNqQyxPQUFPLE1BQU12QyxnREFBU0EsQ0FBQ3dDLGNBQWMsQ0FDbkNuQyxhQUNBQyxZQUFZRyxnQkFBZ0IsRUFDNUIyQyxlQUNBO1lBQ0UsR0FBR2IsT0FBTztZQUNWTixXQUFXLElBQUlGLE9BQU9DLFdBQVc7UUFDbkM7SUFFSjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9saWIvZGF0YWJhc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGF0YWJhc2VzIH0gZnJvbSBcIi4vYXBwd3JpdGVcIjtcbmltcG9ydCB7IElELCBQZXJtaXNzaW9uLCBSb2xlLCBRdWVyeSB9IGZyb20gXCJhcHB3cml0ZVwiO1xuXG4vLyBEYXRhYmFzZSBhbmQgQ29sbGVjdGlvbiBJRHNcbmV4cG9ydCBjb25zdCBEQVRBQkFTRV9JRCA9IFwic3hlX3Zpc2lvbl9ib2FyZFwiO1xuZXhwb3J0IGNvbnN0IENPTExFQ1RJT05TID0ge1xuICBDQVRFR09SSUVTOiBcImNhdGVnb3JpZXNcIixcbiAgR09BTFM6IFwiZ29hbHNcIixcbiAgVVNFUl9QUkVGRVJFTkNFUzogXCJ1c2VyX3ByZWZlcmVuY2VzXCJcbn07XG5cbi8vIERhdGFiYXNlIHNjaGVtYSBzZXR1cFxuZXhwb3J0IGNvbnN0IHNldHVwRGF0YWJhc2UgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8gQ3JlYXRlIGRhdGFiYXNlXG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZShEQVRBQkFTRV9JRCwgXCJTWEUgVmlzaW9uIEJvYXJkXCIpO1xuICAgIGNvbnNvbGUubG9nKFwiRGF0YWJhc2UgY3JlYXRlZCBzdWNjZXNzZnVsbHlcIik7XG5cbiAgICAvLyBDcmVhdGUgQ2F0ZWdvcmllcyBjb2xsZWN0aW9uXG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZUNvbGxlY3Rpb24oXG4gICAgICBEQVRBQkFTRV9JRCxcbiAgICAgIENPTExFQ1RJT05TLkNBVEVHT1JJRVMsXG4gICAgICBcIkNhdGVnb3JpZXNcIixcbiAgICAgIFtcbiAgICAgICAgUGVybWlzc2lvbi5yZWFkKFJvbGUudXNlcihcInVzZXJcIikpLFxuICAgICAgICBQZXJtaXNzaW9uLmNyZWF0ZShSb2xlLnVzZXIoXCJ1c2VyXCIpKSxcbiAgICAgICAgUGVybWlzc2lvbi51cGRhdGUoUm9sZS51c2VyKFwidXNlclwiKSksXG4gICAgICAgIFBlcm1pc3Npb24uZGVsZXRlKFJvbGUudXNlcihcInVzZXJcIikpXG4gICAgICBdXG4gICAgKTtcblxuICAgIC8vIENyZWF0ZSBhdHRyaWJ1dGVzIGZvciBDYXRlZ29yaWVzIGNvbGxlY3Rpb25cbiAgICBhd2FpdCBkYXRhYmFzZXMuY3JlYXRlU3RyaW5nQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5DQVRFR09SSUVTLCBcIm5hbWVcIiwgMTAwLCB0cnVlKTtcbiAgICBhd2FpdCBkYXRhYmFzZXMuY3JlYXRlU3RyaW5nQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5DQVRFR09SSUVTLCBcImNvbG9yXCIsIDUwLCB0cnVlKTtcbiAgICBhd2FpdCBkYXRhYmFzZXMuY3JlYXRlU3RyaW5nQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5DQVRFR09SSUVTLCBcInVzZXJJZFwiLCA1MCwgdHJ1ZSk7XG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZUludGVnZXJBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkNBVEVHT1JJRVMsIFwib3JkZXJcIiwgdHJ1ZSk7XG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZURhdGV0aW1lQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5DQVRFR09SSUVTLCBcImNyZWF0ZWRBdFwiLCB0cnVlKTtcbiAgICBhd2FpdCBkYXRhYmFzZXMuY3JlYXRlRGF0ZXRpbWVBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkNBVEVHT1JJRVMsIFwidXBkYXRlZEF0XCIsIHRydWUpO1xuXG4gICAgLy8gQ3JlYXRlIEdvYWxzIGNvbGxlY3Rpb25cbiAgICBhd2FpdCBkYXRhYmFzZXMuY3JlYXRlQ29sbGVjdGlvbihcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuR09BTFMsXG4gICAgICBcIkdvYWxzXCIsXG4gICAgICBbXG4gICAgICAgIFBlcm1pc3Npb24ucmVhZChSb2xlLnVzZXIoXCJ1c2VyXCIpKSxcbiAgICAgICAgUGVybWlzc2lvbi5jcmVhdGUoUm9sZS51c2VyKFwidXNlclwiKSksXG4gICAgICAgIFBlcm1pc3Npb24udXBkYXRlKFJvbGUudXNlcihcInVzZXJcIikpLFxuICAgICAgICBQZXJtaXNzaW9uLmRlbGV0ZShSb2xlLnVzZXIoXCJ1c2VyXCIpKVxuICAgICAgXVxuICAgICk7XG5cbiAgICAvLyBDcmVhdGUgYXR0cmlidXRlcyBmb3IgR29hbHMgY29sbGVjdGlvblxuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVTdHJpbmdBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkdPQUxTLCBcInRpdGxlXCIsIDIwMCwgdHJ1ZSk7XG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZVN0cmluZ0F0dHJpYnV0ZShEQVRBQkFTRV9JRCwgQ09MTEVDVElPTlMuR09BTFMsIFwiZGVzY3JpcHRpb25cIiwgMTAwMCwgZmFsc2UpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVTdHJpbmdBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkdPQUxTLCBcImljb25cIiwgMTAsIHRydWUpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVFbnVtQXR0cmlidXRlKFxuICAgICAgREFUQUJBU0VfSUQsIFxuICAgICAgQ09MTEVDVElPTlMuR09BTFMsIFxuICAgICAgXCJzdGF0dXNcIiwgXG4gICAgICBbXCJub3Qtc3RhcnRlZFwiLCBcImluLXByb2dyZXNzXCIsIFwiZG9uZVwiLCBcIm5vdC1nb29kXCJdLCBcbiAgICAgIHRydWVcbiAgICApO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVTdHJpbmdBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkdPQUxTLCBcImNhdGVnb3J5SWRcIiwgNTAsIHRydWUpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVTdHJpbmdBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkdPQUxTLCBcInVzZXJJZFwiLCA1MCwgdHJ1ZSk7XG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZUludGVnZXJBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLkdPQUxTLCBcIm9yZGVyXCIsIHRydWUpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEYXRldGltZUF0dHJpYnV0ZShEQVRBQkFTRV9JRCwgQ09MTEVDVElPTlMuR09BTFMsIFwiY3JlYXRlZEF0XCIsIHRydWUpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEYXRldGltZUF0dHJpYnV0ZShEQVRBQkFTRV9JRCwgQ09MTEVDVElPTlMuR09BTFMsIFwidXBkYXRlZEF0XCIsIHRydWUpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEYXRldGltZUF0dHJpYnV0ZShEQVRBQkFTRV9JRCwgQ09MTEVDVElPTlMuR09BTFMsIFwidGFyZ2V0RGF0ZVwiLCBmYWxzZSk7XG5cbiAgICAvLyBDcmVhdGUgVXNlciBQcmVmZXJlbmNlcyBjb2xsZWN0aW9uXG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZUNvbGxlY3Rpb24oXG4gICAgICBEQVRBQkFTRV9JRCxcbiAgICAgIENPTExFQ1RJT05TLlVTRVJfUFJFRkVSRU5DRVMsXG4gICAgICBcIlVzZXIgUHJlZmVyZW5jZXNcIixcbiAgICAgIFtcbiAgICAgICAgUGVybWlzc2lvbi5yZWFkKFJvbGUudXNlcihcInVzZXJcIikpLFxuICAgICAgICBQZXJtaXNzaW9uLmNyZWF0ZShSb2xlLnVzZXIoXCJ1c2VyXCIpKSxcbiAgICAgICAgUGVybWlzc2lvbi51cGRhdGUoUm9sZS51c2VyKFwidXNlclwiKSksXG4gICAgICAgIFBlcm1pc3Npb24uZGVsZXRlKFJvbGUudXNlcihcInVzZXJcIikpXG4gICAgICBdXG4gICAgKTtcblxuICAgIC8vIENyZWF0ZSBhdHRyaWJ1dGVzIGZvciBVc2VyIFByZWZlcmVuY2VzIGNvbGxlY3Rpb25cbiAgICBhd2FpdCBkYXRhYmFzZXMuY3JlYXRlU3RyaW5nQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5VU0VSX1BSRUZFUkVOQ0VTLCBcInVzZXJJZFwiLCA1MCwgdHJ1ZSk7XG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZVN0cmluZ0F0dHJpYnV0ZShEQVRBQkFTRV9JRCwgQ09MTEVDVElPTlMuVVNFUl9QUkVGRVJFTkNFUywgXCJ0aGVtZVwiLCA1MCwgZmFsc2UpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVCb29sZWFuQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5VU0VSX1BSRUZFUkVOQ0VTLCBcImFpU3VnZ2VzdGlvbnNcIiwgZmFsc2UpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVTdHJpbmdBdHRyaWJ1dGUoREFUQUJBU0VfSUQsIENPTExFQ1RJT05TLlVTRVJfUFJFRkVSRU5DRVMsIFwicHJlZmVyZW5jZXNcIiwgMjAwMCwgZmFsc2UpO1xuICAgIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEYXRldGltZUF0dHJpYnV0ZShEQVRBQkFTRV9JRCwgQ09MTEVDVElPTlMuVVNFUl9QUkVGRVJFTkNFUywgXCJjcmVhdGVkQXRcIiwgdHJ1ZSk7XG4gICAgYXdhaXQgZGF0YWJhc2VzLmNyZWF0ZURhdGV0aW1lQXR0cmlidXRlKERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUy5VU0VSX1BSRUZFUkVOQ0VTLCBcInVwZGF0ZWRBdFwiLCB0cnVlKTtcblxuICAgIGNvbnNvbGUubG9nKFwiRGF0YWJhc2Ugc2NoZW1hIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzZXR0aW5nIHVwIGRhdGFiYXNlOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn07XG5cbi8vIENSVUQgb3BlcmF0aW9ucyBmb3IgQ2F0ZWdvcmllc1xuZXhwb3J0IGNvbnN0IGNhdGVnb3J5U2VydmljZSA9IHtcbiAgYXN5bmMgY3JlYXRlKHVzZXJJZCwgY2F0ZWdvcnlEYXRhKSB7XG4gICAgcmV0dXJuIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEb2N1bWVudChcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuQ0FURUdPUklFUyxcbiAgICAgIElELnVuaXF1ZSgpLFxuICAgICAge1xuICAgICAgICAuLi5jYXRlZ29yeURhdGEsXG4gICAgICAgIHVzZXJJZCxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9XG4gICAgKTtcbiAgfSxcblxuICBhc3luYyBsaXN0KHVzZXJJZCkge1xuICAgIHJldHVybiBhd2FpdCBkYXRhYmFzZXMubGlzdERvY3VtZW50cyhcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuQ0FURUdPUklFUyxcbiAgICAgIFtcbiAgICAgICAgUXVlcnkuZXF1YWwoXCJ1c2VySWRcIiwgdXNlcklkKSxcbiAgICAgICAgUXVlcnkub3JkZXJBc2MoXCJvcmRlclwiKVxuICAgICAgXVxuICAgICk7XG4gIH0sXG5cbiAgYXN5bmMgdXBkYXRlKGNhdGVnb3J5SWQsIHVwZGF0ZXMpIHtcbiAgICByZXR1cm4gYXdhaXQgZGF0YWJhc2VzLnVwZGF0ZURvY3VtZW50KFxuICAgICAgREFUQUJBU0VfSUQsXG4gICAgICBDT0xMRUNUSU9OUy5DQVRFR09SSUVTLFxuICAgICAgY2F0ZWdvcnlJZCxcbiAgICAgIHtcbiAgICAgICAgLi4udXBkYXRlcyxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH1cbiAgICApO1xuICB9LFxuXG4gIGFzeW5jIGRlbGV0ZShjYXRlZ29yeUlkKSB7XG4gICAgcmV0dXJuIGF3YWl0IGRhdGFiYXNlcy5kZWxldGVEb2N1bWVudChcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuQ0FURUdPUklFUyxcbiAgICAgIGNhdGVnb3J5SWRcbiAgICApO1xuICB9XG59O1xuXG4vLyBDUlVEIG9wZXJhdGlvbnMgZm9yIEdvYWxzXG5leHBvcnQgY29uc3QgZ29hbFNlcnZpY2UgPSB7XG4gIGFzeW5jIGNyZWF0ZSh1c2VySWQsIGdvYWxEYXRhKSB7XG4gICAgcmV0dXJuIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEb2N1bWVudChcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuR09BTFMsXG4gICAgICBJRC51bmlxdWUoKSxcbiAgICAgIHtcbiAgICAgICAgLi4uZ29hbERhdGEsXG4gICAgICAgIHVzZXJJZCxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9XG4gICAgKTtcbiAgfSxcblxuICBhc3luYyBsaXN0KHVzZXJJZCwgY2F0ZWdvcnlJZCA9IG51bGwpIHtcbiAgICBjb25zdCBxdWVyaWVzID0gW1F1ZXJ5LmVxdWFsKFwidXNlcklkXCIsIHVzZXJJZCldO1xuICAgIGlmIChjYXRlZ29yeUlkKSB7XG4gICAgICBxdWVyaWVzLnB1c2goUXVlcnkuZXF1YWwoXCJjYXRlZ29yeUlkXCIsIGNhdGVnb3J5SWQpKTtcbiAgICB9XG4gICAgcXVlcmllcy5wdXNoKFF1ZXJ5Lm9yZGVyQXNjKFwib3JkZXJcIikpO1xuXG4gICAgcmV0dXJuIGF3YWl0IGRhdGFiYXNlcy5saXN0RG9jdW1lbnRzKFxuICAgICAgREFUQUJBU0VfSUQsXG4gICAgICBDT0xMRUNUSU9OUy5HT0FMUyxcbiAgICAgIHF1ZXJpZXNcbiAgICApO1xuICB9LFxuXG4gIGFzeW5jIHVwZGF0ZShnb2FsSWQsIHVwZGF0ZXMpIHtcbiAgICByZXR1cm4gYXdhaXQgZGF0YWJhc2VzLnVwZGF0ZURvY3VtZW50KFxuICAgICAgREFUQUJBU0VfSUQsXG4gICAgICBDT0xMRUNUSU9OUy5HT0FMUyxcbiAgICAgIGdvYWxJZCxcbiAgICAgIHtcbiAgICAgICAgLi4udXBkYXRlcyxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH1cbiAgICApO1xuICB9LFxuXG4gIGFzeW5jIGRlbGV0ZShnb2FsSWQpIHtcbiAgICByZXR1cm4gYXdhaXQgZGF0YWJhc2VzLmRlbGV0ZURvY3VtZW50KFxuICAgICAgREFUQUJBU0VfSUQsXG4gICAgICBDT0xMRUNUSU9OUy5HT0FMUyxcbiAgICAgIGdvYWxJZFxuICAgICk7XG4gIH1cbn07XG5cbi8vIENSVUQgb3BlcmF0aW9ucyBmb3IgVXNlciBQcmVmZXJlbmNlc1xuZXhwb3J0IGNvbnN0IHVzZXJQcmVmZXJlbmNlc1NlcnZpY2UgPSB7XG4gIGFzeW5jIGNyZWF0ZSh1c2VySWQsIHByZWZlcmVuY2VzKSB7XG4gICAgcmV0dXJuIGF3YWl0IGRhdGFiYXNlcy5jcmVhdGVEb2N1bWVudChcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuVVNFUl9QUkVGRVJFTkNFUyxcbiAgICAgIElELnVuaXF1ZSgpLFxuICAgICAge1xuICAgICAgICB1c2VySWQsXG4gICAgICAgIC4uLnByZWZlcmVuY2VzLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH1cbiAgICApO1xuICB9LFxuXG4gIGFzeW5jIGdldCh1c2VySWQpIHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBkYXRhYmFzZXMubGlzdERvY3VtZW50cyhcbiAgICAgIERBVEFCQVNFX0lELFxuICAgICAgQ09MTEVDVElPTlMuVVNFUl9QUkVGRVJFTkNFUyxcbiAgICAgIFtRdWVyeS5lcXVhbChcInVzZXJJZFwiLCB1c2VySWQpXVxuICAgICk7XG4gICAgcmV0dXJuIHJlc3VsdC5kb2N1bWVudHNbMF0gfHwgbnVsbDtcbiAgfSxcblxuICBhc3luYyB1cGRhdGUocHJlZmVyZW5jZXNJZCwgdXBkYXRlcykge1xuICAgIHJldHVybiBhd2FpdCBkYXRhYmFzZXMudXBkYXRlRG9jdW1lbnQoXG4gICAgICBEQVRBQkFTRV9JRCxcbiAgICAgIENPTExFQ1RJT05TLlVTRVJfUFJFRkVSRU5DRVMsXG4gICAgICBwcmVmZXJlbmNlc0lkLFxuICAgICAge1xuICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfVxuICAgICk7XG4gIH1cbn07XG4iXSwibmFtZXMiOlsiZGF0YWJhc2VzIiwiSUQiLCJQZXJtaXNzaW9uIiwiUm9sZSIsIlF1ZXJ5IiwiREFUQUJBU0VfSUQiLCJDT0xMRUNUSU9OUyIsIkNBVEVHT1JJRVMiLCJHT0FMUyIsIlVTRVJfUFJFRkVSRU5DRVMiLCJzZXR1cERhdGFiYXNlIiwiY3JlYXRlIiwiY29uc29sZSIsImxvZyIsImNyZWF0ZUNvbGxlY3Rpb24iLCJyZWFkIiwidXNlciIsInVwZGF0ZSIsImRlbGV0ZSIsImNyZWF0ZVN0cmluZ0F0dHJpYnV0ZSIsImNyZWF0ZUludGVnZXJBdHRyaWJ1dGUiLCJjcmVhdGVEYXRldGltZUF0dHJpYnV0ZSIsImNyZWF0ZUVudW1BdHRyaWJ1dGUiLCJjcmVhdGVCb29sZWFuQXR0cmlidXRlIiwiZXJyb3IiLCJjYXRlZ29yeVNlcnZpY2UiLCJ1c2VySWQiLCJjYXRlZ29yeURhdGEiLCJjcmVhdGVEb2N1bWVudCIsInVuaXF1ZSIsImNyZWF0ZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZWRBdCIsImxpc3QiLCJsaXN0RG9jdW1lbnRzIiwiZXF1YWwiLCJvcmRlckFzYyIsImNhdGVnb3J5SWQiLCJ1cGRhdGVzIiwidXBkYXRlRG9jdW1lbnQiLCJkZWxldGVEb2N1bWVudCIsImdvYWxTZXJ2aWNlIiwiZ29hbERhdGEiLCJxdWVyaWVzIiwicHVzaCIsImdvYWxJZCIsInVzZXJQcmVmZXJlbmNlc1NlcnZpY2UiLCJwcmVmZXJlbmNlcyIsImdldCIsInJlc3VsdCIsImRvY3VtZW50cyIsInByZWZlcmVuY2VzSWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.js\n"));

/***/ })

});