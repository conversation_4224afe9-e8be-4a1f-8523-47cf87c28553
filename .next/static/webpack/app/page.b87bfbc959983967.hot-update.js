"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17dfdd5ebdad\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE3ZGZkZDVlYmRhZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _ProgressOverview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressOverview */ \"(app-pages-browser)/./src/components/ProgressOverview.js\");\n/* harmony import */ var _AIInsights__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AIInsights */ \"(app-pages-browser)/./src/components/AIInsights.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGoals, setAllGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    // Real-time update handlers\n    const handleCategoryUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VisionBoard.useCallback[handleCategoryUpdate]\": (payload, events)=>{\n            const isCreate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isCreate\": (e)=>e.includes('.create')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isCreate\"]);\n            const isUpdate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\": (e)=>e.includes('.update')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\"]);\n            const isDelete = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isDelete\": (e)=>e.includes('.delete')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isDelete\"]);\n            if (isCreate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>[\n                            ...prev,\n                            payload\n                        ]\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isUpdate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.map({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id === payload.$id ? payload : cat\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isDelete) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.filter({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id !== payload.$id\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            }\n        }\n    }[\"VisionBoard.useCallback[handleCategoryUpdate]\"], []);\n    // Set up real-time subscriptions\n    (0,_hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(userId, handleCategoryUpdate, null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n                loadAllGoals();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadAllGoals = async ()=>{\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.list(userId);\n            setAllGoals(result.documents);\n        } catch (error) {\n            console.error(\"Error loading all goals:\", error);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressOverview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userId: userId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"sPZE3qNsc/NOweeUWYagq2a0U0c=\", false, function() {\n    return [\n        _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ })

});