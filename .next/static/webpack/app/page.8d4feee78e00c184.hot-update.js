"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b0f4f81e7795\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImIwZjRmODFlNzc5NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"(app-pages-browser)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _ProgressOverview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ProgressOverview */ \"(app-pages-browser)/./src/components/ProgressOverview.js\");\n/* harmony import */ var _AIInsights__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AIInsights */ \"(app-pages-browser)/./src/components/AIInsights.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGoals, setAllGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedGoal, setDraggedGoal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    // Real-time update handlers\n    const handleCategoryUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VisionBoard.useCallback[handleCategoryUpdate]\": (payload, events)=>{\n            const isCreate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isCreate\": (e)=>e.includes('.create')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isCreate\"]);\n            const isUpdate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\": (e)=>e.includes('.update')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\"]);\n            const isDelete = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isDelete\": (e)=>e.includes('.delete')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isDelete\"]);\n            if (isCreate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>[\n                            ...prev,\n                            payload\n                        ]\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isUpdate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.map({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id === payload.$id ? payload : cat\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isDelete) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.filter({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id !== payload.$id\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            }\n        }\n    }[\"VisionBoard.useCallback[handleCategoryUpdate]\"], []);\n    // Set up real-time subscriptions\n    (0,_hooks_useRealtime__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(userId, handleCategoryUpdate, null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n                loadAllGoals();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadAllGoals = async ()=>{\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.goalService.list(userId);\n            setAllGoals(result.documents);\n        } catch (error) {\n            console.error(\"Error loading all goals:\", error);\n        }\n    };\n    const handleDragStart = (event)=>{\n        const { active } = event;\n        setActiveId(active.id);\n        // Find the dragged goal\n        const goal = allGoals.find((g)=>(g.$id || g.id) === active.id);\n        setDraggedGoal(goal);\n    };\n    const handleDragEnd = async (event)=>{\n        const { active, over } = event;\n        if (!over) {\n            setActiveId(null);\n            setDraggedGoal(null);\n            return;\n        }\n        const activeGoalId = active.id;\n        const overId = over.id;\n        // Find the active goal\n        const activeGoal = allGoals.find((g)=>(g.$id || g.id) === activeGoalId);\n        if (!activeGoal) return;\n        // Check if we're dropping on a category\n        const targetCategory = categories.find((c)=>(c.$id || c.id) === overId);\n        if (targetCategory && activeGoal.categoryId !== (targetCategory.$id || targetCategory.id)) {\n            // Moving goal to different category\n            try {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_8__.goalService.update(activeGoal.$id || activeGoal.id, {\n                    categoryId: targetCategory.$id || targetCategory.id\n                });\n                // Update local state\n                setAllGoals((prev)=>prev.map((goal)=>(goal.$id || goal.id) === activeGoalId ? {\n                            ...goal,\n                            categoryId: targetCategory.$id || targetCategory.id\n                        } : goal));\n            } catch (error) {\n                console.error(\"Error moving goal:\", error);\n            }\n        }\n        setActiveId(null);\n        setDraggedGoal(null);\n    };\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_8__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressOverview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    userId: userId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIInsights__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    goals: allGoals,\n                    userId: userId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"82KrcyoV3CQOMbq67BxAPygR2M4=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors,\n        _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n});\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ })

});