"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/lightbulb.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Lightbulb)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5\",\n            key: \"1gvzjb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 18h6\",\n            key: \"x1upvd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 22h4\",\n            key: \"ceow96\"\n        }\n    ]\n];\nconst Lightbulb = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"lightbulb\", __iconNode);\n //# sourceMappingURL=lightbulb.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/sparkles.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Sparkles)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z\",\n            key: \"1s2grr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 2v4\",\n            key: \"1rf3ol\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 4h-4\",\n            key: \"gwowj6\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"4\",\n            cy: \"20\",\n            r: \"2\",\n            key: \"6kqj1y\"\n        }\n    ]\n];\nconst Sparkles = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sparkles\", __iconNode);\n //# sourceMappingURL=sparkles.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"22fe8e4e4521\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyZmU4ZTRlNDUyMVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AISuggestions.js":
/*!*****************************************!*\
  !*** ./src/components/AISuggestions.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Plus,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Plus,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Plus,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Plus,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _lib_aiService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aiService */ \"(app-pages-browser)/./src/lib/aiService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AISuggestions = (param)=>{\n    let { categoryName, existingGoals, onAddGoal, isOpen, onClose } = param;\n    _s();\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSuggestion, setSelectedSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AISuggestions.useEffect\": ()=>{\n            if (isOpen && categoryName) {\n                loadSuggestions();\n            }\n        }\n    }[\"AISuggestions.useEffect\"], [\n        isOpen,\n        categoryName,\n        existingGoals\n    ]);\n    const loadSuggestions = async ()=>{\n        setLoading(true);\n        try {\n            // Simulate AI processing delay\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            const aiSuggestions = _lib_aiService__WEBPACK_IMPORTED_MODULE_2__.aiService.getSuggestions(categoryName, existingGoals);\n            setSuggestions(aiSuggestions);\n        } catch (error) {\n            console.error(\"Error loading AI suggestions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddSuggestion = (suggestion)=>{\n        onAddGoal({\n            title: suggestion.title,\n            icon: suggestion.icon,\n            description: suggestion.description\n        });\n        onClose();\n    };\n    const handleCustomizeAndAdd = ()=>{\n        if (selectedSuggestion) {\n            handleAddSuggestion(selectedSuggestion);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n            onClick: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                exit: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                className: \"bg-white rounded-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto\",\n                onClick: (e)=>e.stopPropagation(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"text-purple-600\",\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-gray-800\",\n                                                children: \"AI Goal Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    \"Smart recommendations for your \",\n                                                    categoryName,\n                                                    \" category\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 90,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"AI is analyzing your goals...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 91,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                        lineNumber: 89,\n                        columnNumber: 15\n                    }, undefined) : suggestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"p-4 border-2 rounded-lg cursor-pointer transition-all \".concat(selectedSuggestion === suggestion ? \"border-purple-500 bg-purple-50\" : \"border-gray-200 hover:border-purple-300 hover:bg-gray-50\"),\n                                        onClick: ()=>setSelectedSuggestion(suggestion),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: suggestion.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-800 mb-1\",\n                                                            children: suggestion.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: suggestion.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleAddSuggestion(suggestion);\n                                                    },\n                                                    className: \"p-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors\",\n                                                    title: \"Add this goal\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 95,\n                                columnNumber: 17\n                            }, undefined),\n                            selectedSuggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: selectedSuggestion.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-purple-800\",\n                                                            children: selectedSuggestion.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-600 text-sm\",\n                                                            children: \"Selected for addition\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                            lineNumber: 141,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCustomizeAndAdd,\n                                            className: \"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors\",\n                                            children: \"Add Goal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                            lineNumber: 152,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 135,\n                                columnNumber: 19\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center pt-4 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    \"Powered by AI • \",\n                                                    suggestions.length,\n                                                    \" suggestions found\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadSuggestions,\n                                        className: \"text-purple-600 hover:text-purple-700 text-sm font-medium\",\n                                        children: \"Refresh Suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 162,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                        lineNumber: 94,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Plus_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"mx-auto text-gray-400 mb-4\",\n                                size: 48\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 179,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No new suggestions available for this category.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 180,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadSuggestions,\n                                className: \"mt-4 text-purple-600 hover:text-purple-700 font-medium\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                                lineNumber: 183,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                        lineNumber: 178,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n            lineNumber: 52,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AISuggestions.js\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AISuggestions, \"srjFdxY91/2cf5Jl2IdSflFe0mQ=\");\n_c = AISuggestions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AISuggestions);\nvar _c;\n$RefreshReg$(_c, \"AISuggestions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AISuggestions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Category.js":
/*!************************************!*\
  !*** ./src/components/Category.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _AddGoalModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddGoalModal */ \"(app-pages-browser)/./src/components/AddGoalModal.js\");\n/* harmony import */ var _AISuggestions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AISuggestions */ \"(app-pages-browser)/./src/components/AISuggestions.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Category = (param)=>{\n    let { category, userId } = param;\n    _s();\n    const [goals, setGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Sample goals based on the vision board image\n    const sampleGoals = {\n        personal: [\n            {\n                id: \"1\",\n                title: \"Documents\",\n                status: \"in-progress\",\n                icon: \"📄\"\n            },\n            {\n                id: \"2\",\n                title: \"Family\",\n                status: \"not-good\",\n                icon: \"👨‍👩‍👧‍👦\"\n            },\n            {\n                id: \"3\",\n                title: \"Exercise\",\n                status: \"not-good\",\n                icon: \"💪\"\n            },\n            {\n                id: \"4\",\n                title: \"Diet\",\n                status: \"not-good\",\n                icon: \"🥗\"\n            },\n            {\n                id: \"5\",\n                title: \"Prayers\",\n                status: \"not-good\",\n                icon: \"🙏\"\n            }\n        ],\n        learn: [\n            {\n                id: \"6\",\n                title: \"Coding\",\n                status: \"done\",\n                icon: \"💻\"\n            },\n            {\n                id: \"7\",\n                title: \"Design\",\n                status: \"done\",\n                icon: \"🎨\"\n            },\n            {\n                id: \"8\",\n                title: \"Business\",\n                status: \"done\",\n                icon: \"💼\"\n            },\n            {\n                id: \"9\",\n                title: \"Marketing\",\n                status: \"done\",\n                icon: \"📈\"\n            },\n            {\n                id: \"10\",\n                title: \"Finance\",\n                status: \"done\",\n                icon: \"💰\"\n            }\n        ],\n        work: [\n            {\n                id: \"11\",\n                title: \"Trading\",\n                status: \"in-progress\",\n                icon: \"📊\"\n            },\n            {\n                id: \"12\",\n                title: \"Real Estate\",\n                status: \"in-progress\",\n                icon: \"🏠\"\n            },\n            {\n                id: \"13\",\n                title: \"Digital Marketing\",\n                status: \"in-progress\",\n                icon: \"📱\"\n            },\n            {\n                id: \"14\",\n                title: \"Hubcv\",\n                status: \"not-good\",\n                icon: \"🔗\"\n            },\n            {\n                id: \"15\",\n                title: \"Codelude\",\n                status: \"not-good\",\n                icon: \"⚡\"\n            }\n        ],\n        finance: [\n            {\n                id: \"16\",\n                title: \"Net Worth\",\n                status: \"in-progress\",\n                icon: \"🏛️\"\n            },\n            {\n                id: \"17\",\n                title: \"Loans & Credits\",\n                status: \"not-good\",\n                icon: \"💳\"\n            },\n            {\n                id: \"18\",\n                title: \"Assets\",\n                status: \"not-started\",\n                icon: \"💎\"\n            },\n            {\n                id: \"19\",\n                title: \"Charity\",\n                status: \"not-started\",\n                icon: \"❤️\"\n            },\n            {\n                id: \"20\",\n                title: \"Investments\",\n                status: \"not-started\",\n                icon: \"💹\"\n            }\n        ],\n        weekend: [\n            {\n                id: \"21\",\n                title: \"Memories\",\n                status: \"in-progress\",\n                icon: \"📸\"\n            },\n            {\n                id: \"22\",\n                title: \"Travels\",\n                status: \"in-progress\",\n                icon: \"✈️\"\n            },\n            {\n                id: \"23\",\n                title: \"Shopping\",\n                status: \"not-good\",\n                icon: \"🛍️\"\n            },\n            {\n                id: \"24\",\n                title: \"Branding\",\n                status: \"not-started\",\n                icon: \"⭐\"\n            },\n            {\n                id: \"25\",\n                title: \"Events\",\n                status: \"not-started\",\n                icon: \"🎉\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Category.useEffect\": ()=>{\n            loadGoals();\n        }\n    }[\"Category.useEffect\"], [\n        category.id\n    ]);\n    const loadGoals = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load goals from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.list(userId, category.$id || category.id);\n                if (result.documents.length > 0) {\n                    setGoals(result.documents);\n                } else {\n                    // If no goals exist, create sample goals for this category\n                    await createSampleGoals();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using sample data:\", dbError);\n                const categoryGoals = sampleGoals[category.id] || [];\n                setGoals(categoryGoals);\n            }\n        } catch (error) {\n            console.error(\"Error loading goals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createSampleGoals = async ()=>{\n        try {\n            var _category_name;\n            const categoryGoals = sampleGoals[category.id] || sampleGoals[(_category_name = category.name) === null || _category_name === void 0 ? void 0 : _category_name.toLowerCase()] || [];\n            const createdGoals = [];\n            for(let i = 0; i < categoryGoals.length; i++){\n                const goal = categoryGoals[i];\n                try {\n                    const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.create(userId, {\n                        title: goal.title,\n                        icon: goal.icon,\n                        status: goal.status,\n                        categoryId: category.$id || category.id,\n                        order: i\n                    });\n                    createdGoals.push(created);\n                } catch (error) {\n                    console.error(\"Error creating sample goal:\", error);\n                    // If database creation fails, use sample data\n                    createdGoals.push(goal);\n                }\n            }\n            setGoals(createdGoals);\n        } catch (error) {\n            console.error(\"Error creating sample goals:\", error);\n            const categoryGoals = sampleGoals[category.id] || [];\n            setGoals(categoryGoals);\n        }\n    };\n    const addGoal = async (newGoal)=>{\n        try {\n            const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.create(userId, {\n                ...newGoal,\n                status: \"not-started\",\n                categoryId: category.$id || category.id,\n                order: goals.length\n            });\n            setGoals([\n                ...goals,\n                created\n            ]);\n        } catch (error) {\n            console.error(\"Error adding goal:\", error);\n            // Fallback to local state\n            const goal = {\n                id: Date.now().toString(),\n                ...newGoal,\n                status: \"not-started\"\n            };\n            setGoals([\n                ...goals,\n                goal\n            ]);\n        }\n    };\n    const updateGoal = async (goalId, updates)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.update(goalId, updates);\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        } catch (error) {\n            console.error(\"Error updating goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        }\n    };\n    const deleteGoal = async (goalId)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.delete(goalId);\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        } catch (error) {\n            console.error(\"Error deleting goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(category.color, \" rounded-lg p-4 min-h-[400px] shadow-lg hover:shadow-xl transition-shadow duration-300\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddModal(true),\n                        className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoalCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    goal: goal,\n                                    onUpdate: updateGoal,\n                                    onDelete: deleteGoal\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, goal.id, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    goals.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/70 text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No goals yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"mt-2 text-white/90 hover:text-white underline\",\n                                children: \"Add your first goal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-2 border-t border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddModal(true),\n                            className: \"w-full flex items-center justify-center space-x-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg py-2 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"New goal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddGoalModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onAdd: addGoal,\n                categoryName: category.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Category, \"UvPV4U8DXw8RWQ0vermlCsxiApQ=\");\n_c = Category;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\nvar _c;\n$RefreshReg$(_c, \"Category\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Category.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/aiService.js":
/*!******************************!*\
  !*** ./src/lib/aiService.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: () => (/* binding */ aiService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// AI Service for SXE Vision Board\n// This service provides AI-powered features like goal suggestions and progress analysis\nclass AIService {\n    // Get AI-powered goal suggestions based on category\n    getSuggestions(categoryName) {\n        let existingGoals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n        const categoryKey = categoryName.toLowerCase();\n        const suggestions = this.goalSuggestions[categoryKey] || [];\n        // Filter out goals that already exist\n        const existingTitles = existingGoals.map((goal)=>goal.title.toLowerCase());\n        const filteredSuggestions = suggestions.filter((suggestion)=>!existingTitles.includes(suggestion.title.toLowerCase()));\n        // Return random 3-5 suggestions\n        const shuffled = filteredSuggestions.sort(()=>0.5 - Math.random());\n        return shuffled.slice(0, Math.min(5, shuffled.length));\n    }\n    // Analyze progress and provide insights\n    analyzeProgress(goals) {\n        const total = goals.length;\n        if (total === 0) {\n            return {\n                message: \"Start by adding some goals to track your progress!\",\n                suggestion: \"Break down your big dreams into smaller, actionable goals.\",\n                motivation: this.getRandomQuote()\n            };\n        }\n        const completed = goals.filter((g)=>g.status === 'done').length;\n        const inProgress = goals.filter((g)=>g.status === 'in-progress').length;\n        const notGood = goals.filter((g)=>g.status === 'not-good').length;\n        const completionRate = completed / total * 100;\n        let message, suggestion;\n        if (completionRate >= 80) {\n            message = \"🎉 Excellent progress! You're crushing your goals!\";\n            suggestion = \"Consider setting more ambitious goals to keep challenging yourself.\";\n        } else if (completionRate >= 60) {\n            message = \"👍 Great work! You're making solid progress.\";\n            suggestion = \"Focus on the goals that need attention to boost your completion rate.\";\n        } else if (completionRate >= 40) {\n            message = \"📈 You're on the right track, keep pushing forward!\";\n            suggestion = \"Try breaking down larger goals into smaller, manageable tasks.\";\n        } else if (completionRate >= 20) {\n            message = \"💪 Every journey starts with a single step.\";\n            suggestion = \"Focus on 1-2 goals at a time to avoid feeling overwhelmed.\";\n        } else {\n            message = \"🌱 It's time to take action on your goals!\";\n            suggestion = \"Start with the easiest goal to build momentum.\";\n        }\n        if (notGood > 0) {\n            suggestion += \" You have \".concat(notGood, \" goal(s) that need attention - consider revising or breaking them down.\");\n        }\n        return {\n            message,\n            suggestion,\n            motivation: this.getRandomQuote(),\n            stats: {\n                total,\n                completed,\n                inProgress,\n                notGood,\n                completionRate: Math.round(completionRate)\n            }\n        };\n    }\n    // Smart categorization suggestions\n    suggestCategory(goalTitle) {\n        const title = goalTitle.toLowerCase();\n        // Work-related keywords\n        if (title.includes('work') || title.includes('job') || title.includes('career') || title.includes('business') || title.includes('project') || title.includes('meeting') || title.includes('client') || title.includes('professional')) {\n            return 'work';\n        }\n        // Learning keywords\n        if (title.includes('learn') || title.includes('study') || title.includes('course') || title.includes('skill') || title.includes('education') || title.includes('training') || title.includes('read') || title.includes('book')) {\n            return 'learn';\n        }\n        // Finance keywords\n        if (title.includes('money') || title.includes('save') || title.includes('invest') || title.includes('budget') || title.includes('financial') || title.includes('bank') || title.includes('debt') || title.includes('income')) {\n            return 'finance';\n        }\n        // Weekend/leisure keywords\n        if (title.includes('fun') || title.includes('hobby') || title.includes('travel') || title.includes('vacation') || title.includes('weekend') || title.includes('relax') || title.includes('entertainment') || title.includes('game')) {\n            return 'weekend';\n        }\n        // Default to personal\n        return 'personal';\n    }\n    // Get a random motivational quote\n    getRandomQuote() {\n        return this.motivationalQuotes[Math.floor(Math.random() * this.motivationalQuotes.length)];\n    }\n    // Generate goal recommendations based on user's current goals\n    getPersonalizedRecommendations(allGoals, categoryName) {\n        const analysis = this.analyzeProgress(allGoals);\n        const suggestions = this.getSuggestions(categoryName, allGoals);\n        return {\n            analysis,\n            suggestions: suggestions.slice(0, 3),\n            quote: this.getRandomQuote()\n        };\n    }\n    constructor(){\n        this.goalSuggestions = {\n            personal: [\n                {\n                    title: \"Read 12 books this year\",\n                    icon: \"📚\",\n                    description: \"Expand your knowledge and imagination\"\n                },\n                {\n                    title: \"Learn a new language\",\n                    icon: \"🗣️\",\n                    description: \"Broaden your communication skills\"\n                },\n                {\n                    title: \"Practice meditation daily\",\n                    icon: \"🧘\",\n                    description: \"Improve mental health and focus\"\n                },\n                {\n                    title: \"Organize digital photos\",\n                    icon: \"📸\",\n                    description: \"Preserve precious memories\"\n                },\n                {\n                    title: \"Write in a journal\",\n                    icon: \"✍️\",\n                    description: \"Reflect on your thoughts and experiences\"\n                },\n                {\n                    title: \"Learn to cook 5 new recipes\",\n                    icon: \"👨‍🍳\",\n                    description: \"Expand your culinary skills\"\n                },\n                {\n                    title: \"Declutter living space\",\n                    icon: \"🏠\",\n                    description: \"Create a more peaceful environment\"\n                },\n                {\n                    title: \"Practice gratitude daily\",\n                    icon: \"🙏\",\n                    description: \"Cultivate a positive mindset\"\n                }\n            ],\n            learn: [\n                {\n                    title: \"Complete an online course\",\n                    icon: \"🎓\",\n                    description: \"Gain new skills and knowledge\"\n                },\n                {\n                    title: \"Learn data analysis\",\n                    icon: \"📊\",\n                    description: \"Understand data-driven insights\"\n                },\n                {\n                    title: \"Master a programming language\",\n                    icon: \"💻\",\n                    description: \"Enhance your technical abilities\"\n                },\n                {\n                    title: \"Study UX/UI design\",\n                    icon: \"🎨\",\n                    description: \"Create better user experiences\"\n                },\n                {\n                    title: \"Learn digital marketing\",\n                    icon: \"📱\",\n                    description: \"Understand modern marketing strategies\"\n                },\n                {\n                    title: \"Study machine learning\",\n                    icon: \"🤖\",\n                    description: \"Explore AI and automation\"\n                },\n                {\n                    title: \"Learn video editing\",\n                    icon: \"🎬\",\n                    description: \"Create engaging visual content\"\n                },\n                {\n                    title: \"Study blockchain technology\",\n                    icon: \"⛓️\",\n                    description: \"Understand decentralized systems\"\n                }\n            ],\n            work: [\n                {\n                    title: \"Launch a side project\",\n                    icon: \"🚀\",\n                    description: \"Turn your ideas into reality\"\n                },\n                {\n                    title: \"Build a professional network\",\n                    icon: \"🤝\",\n                    description: \"Connect with industry professionals\"\n                },\n                {\n                    title: \"Improve public speaking\",\n                    icon: \"🎤\",\n                    description: \"Enhance your communication skills\"\n                },\n                {\n                    title: \"Get a professional certification\",\n                    icon: \"🏆\",\n                    description: \"Validate your expertise\"\n                },\n                {\n                    title: \"Mentor someone junior\",\n                    icon: \"👥\",\n                    description: \"Share your knowledge and experience\"\n                },\n                {\n                    title: \"Optimize workflow processes\",\n                    icon: \"⚡\",\n                    description: \"Increase productivity and efficiency\"\n                },\n                {\n                    title: \"Create a personal brand\",\n                    icon: \"⭐\",\n                    description: \"Establish your professional identity\"\n                },\n                {\n                    title: \"Learn project management\",\n                    icon: \"📋\",\n                    description: \"Lead teams more effectively\"\n                }\n            ],\n            finance: [\n                {\n                    title: \"Create an emergency fund\",\n                    icon: \"🛡️\",\n                    description: \"Build financial security\"\n                },\n                {\n                    title: \"Start investing in stocks\",\n                    icon: \"📈\",\n                    description: \"Grow your wealth over time\"\n                },\n                {\n                    title: \"Track monthly expenses\",\n                    icon: \"💰\",\n                    description: \"Understand your spending patterns\"\n                },\n                {\n                    title: \"Pay off credit card debt\",\n                    icon: \"💳\",\n                    description: \"Reduce financial burden\"\n                },\n                {\n                    title: \"Learn about cryptocurrency\",\n                    icon: \"₿\",\n                    description: \"Explore digital currencies\"\n                },\n                {\n                    title: \"Set up retirement savings\",\n                    icon: \"🏦\",\n                    description: \"Plan for your future\"\n                },\n                {\n                    title: \"Create multiple income streams\",\n                    icon: \"💼\",\n                    description: \"Diversify your earnings\"\n                },\n                {\n                    title: \"Learn about real estate\",\n                    icon: \"🏠\",\n                    description: \"Explore property investment\"\n                }\n            ],\n            weekend: [\n                {\n                    title: \"Plan a weekend getaway\",\n                    icon: \"🏖️\",\n                    description: \"Relax and recharge\"\n                },\n                {\n                    title: \"Try a new hobby\",\n                    icon: \"🎯\",\n                    description: \"Discover new interests\"\n                },\n                {\n                    title: \"Visit local museums\",\n                    icon: \"🏛️\",\n                    description: \"Explore culture and history\"\n                },\n                {\n                    title: \"Host a dinner party\",\n                    icon: \"🍽️\",\n                    description: \"Connect with friends and family\"\n                },\n                {\n                    title: \"Go hiking or camping\",\n                    icon: \"🏔️\",\n                    description: \"Connect with nature\"\n                },\n                {\n                    title: \"Learn a musical instrument\",\n                    icon: \"🎸\",\n                    description: \"Express yourself creatively\"\n                },\n                {\n                    title: \"Volunteer for a cause\",\n                    icon: \"❤️\",\n                    description: \"Give back to your community\"\n                },\n                {\n                    title: \"Start a garden\",\n                    icon: \"🌱\",\n                    description: \"Grow your own plants\"\n                }\n            ]\n        };\n        this.motivationalQuotes = [\n            \"The future belongs to those who believe in the beauty of their dreams.\",\n            \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n            \"The only way to do great work is to love what you do.\",\n            \"Your limitation—it's only your imagination.\",\n            \"Push yourself, because no one else is going to do it for you.\",\n            \"Great things never come from comfort zones.\",\n            \"Dream it. Wish it. Do it.\",\n            \"Success doesn't just find you. You have to go out and get it.\"\n        ];\n    }\n}\nconst aiService = new AIService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/aiService.js\n"));

/***/ })

});