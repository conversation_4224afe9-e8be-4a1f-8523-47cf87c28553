"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"926f791d4c6c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkyNmY3OTFkNGM2Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    // Real-time update handlers\n    const handleCategoryUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VisionBoard.useCallback[handleCategoryUpdate]\": (payload, events)=>{\n            const isCreate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isCreate\": (e)=>e.includes('.create')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isCreate\"]);\n            const isUpdate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\": (e)=>e.includes('.update')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\"]);\n            const isDelete = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isDelete\": (e)=>e.includes('.delete')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isDelete\"]);\n            if (isCreate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>[\n                            ...prev,\n                            payload\n                        ]\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isUpdate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.map({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id === payload.$id ? payload : cat\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isDelete) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.filter({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id !== payload.$id\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            }\n        }\n    }[\"VisionBoard.useCallback[handleCategoryUpdate]\"], []);\n    // Set up real-time subscriptions\n    (0,_hooks_useRealtime__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(userId, handleCategoryUpdate, null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"EalBv6uo19YompPwL9FszZ46qf0=\", false, function() {\n    return [\n        _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ })

});