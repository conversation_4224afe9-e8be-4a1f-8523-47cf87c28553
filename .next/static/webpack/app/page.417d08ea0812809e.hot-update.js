"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"04321e53529b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA0MzIxZTUzNTI5YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProgressOverview.js":
/*!********************************************!*\
  !*** ./src/components/ProgressOverview.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst ProgressOverview = (param)=>{\n    let { userId } = param;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        completed: 0,\n        inProgress: 0,\n        notStarted: 0,\n        notGood: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProgressOverview.useEffect\": ()=>{\n            if (userId) {\n                loadStats();\n            }\n        }\n    }[\"ProgressOverview.useEffect\"], [\n        userId\n    ]);\n    const loadStats = async ()=>{\n        try {\n            setLoading(true);\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.goalService.list(userId);\n            const goals = result.documents;\n            const newStats = {\n                total: goals.length,\n                completed: goals.filter((g)=>g.status === 'done').length,\n                inProgress: goals.filter((g)=>g.status === 'in-progress').length,\n                notStarted: goals.filter((g)=>g.status === 'not-started').length,\n                notGood: goals.filter((g)=>g.status === 'not-good').length\n            };\n            setStats(newStats);\n        } catch (error) {\n            console.error(\"Error loading stats:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const completionPercentage = stats.total > 0 ? Math.round(stats.completed / stats.total * 100) : 0;\n    if (loading || stats.total === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"bg-white/10 backdrop-blur-md rounded-lg p-6 mb-8 border border-white/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white\",\n                        children: \"Progress Overview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: [\n                            completionPercentage,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full\",\n                    initial: {\n                        width: 0\n                    },\n                    animate: {\n                        width: \"\".concat(completionPercentage, \"%\")\n                    },\n                    transition: {\n                        duration: 1,\n                        ease: \"easeOut\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: stats.completed\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-white/70\",\n                                children: \"Completed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: stats.inProgress\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-white/70\",\n                                children: \"In Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-400\",\n                                children: stats.notStarted\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-white/70\",\n                                children: \"Not Started\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-red-400\",\n                                children: stats.notGood\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-white/70\",\n                                children: \"Needs Attention\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/ProgressOverview.js\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProgressOverview, \"n09UC8EHIVr4Ur6fjB3lJEAFeQ8=\");\n_c = ProgressOverview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressOverview);\nvar _c;\n$RefreshReg$(_c, \"ProgressOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProgressOverview.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _ProgressOverview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressOverview */ \"(app-pages-browser)/./src/components/ProgressOverview.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    // Real-time update handlers\n    const handleCategoryUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VisionBoard.useCallback[handleCategoryUpdate]\": (payload, events)=>{\n            const isCreate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isCreate\": (e)=>e.includes('.create')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isCreate\"]);\n            const isUpdate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\": (e)=>e.includes('.update')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\"]);\n            const isDelete = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isDelete\": (e)=>e.includes('.delete')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isDelete\"]);\n            if (isCreate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>[\n                            ...prev,\n                            payload\n                        ]\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isUpdate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.map({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id === payload.$id ? payload : cat\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isDelete) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.filter({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id !== payload.$id\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            }\n        }\n    }[\"VisionBoard.useCallback[handleCategoryUpdate]\"], []);\n    // Set up real-time subscriptions\n    (0,_hooks_useRealtime__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(userId, handleCategoryUpdate, null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"EalBv6uo19YompPwL9FszZ46qf0=\", false, function() {\n    return [\n        _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ })

});