"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f383d124aeea\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYzODNkMTI0YWVlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Category.js":
/*!************************************!*\
  !*** ./src/components/Category.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _AddGoalModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddGoalModal */ \"(app-pages-browser)/./src/components/AddGoalModal.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Category = (param)=>{\n    let { category, userId } = param;\n    _s();\n    const [goals, setGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Sample goals based on the vision board image\n    const sampleGoals = {\n        personal: [\n            {\n                id: \"1\",\n                title: \"Documents\",\n                status: \"in-progress\",\n                icon: \"📄\"\n            },\n            {\n                id: \"2\",\n                title: \"Family\",\n                status: \"not-good\",\n                icon: \"👨‍👩‍👧‍👦\"\n            },\n            {\n                id: \"3\",\n                title: \"Exercise\",\n                status: \"not-good\",\n                icon: \"💪\"\n            },\n            {\n                id: \"4\",\n                title: \"Diet\",\n                status: \"not-good\",\n                icon: \"🥗\"\n            },\n            {\n                id: \"5\",\n                title: \"Prayers\",\n                status: \"not-good\",\n                icon: \"🙏\"\n            }\n        ],\n        learn: [\n            {\n                id: \"6\",\n                title: \"Coding\",\n                status: \"done\",\n                icon: \"💻\"\n            },\n            {\n                id: \"7\",\n                title: \"Design\",\n                status: \"done\",\n                icon: \"🎨\"\n            },\n            {\n                id: \"8\",\n                title: \"Business\",\n                status: \"done\",\n                icon: \"💼\"\n            },\n            {\n                id: \"9\",\n                title: \"Marketing\",\n                status: \"done\",\n                icon: \"📈\"\n            },\n            {\n                id: \"10\",\n                title: \"Finance\",\n                status: \"done\",\n                icon: \"💰\"\n            }\n        ],\n        work: [\n            {\n                id: \"11\",\n                title: \"Trading\",\n                status: \"in-progress\",\n                icon: \"📊\"\n            },\n            {\n                id: \"12\",\n                title: \"Real Estate\",\n                status: \"in-progress\",\n                icon: \"🏠\"\n            },\n            {\n                id: \"13\",\n                title: \"Digital Marketing\",\n                status: \"in-progress\",\n                icon: \"📱\"\n            },\n            {\n                id: \"14\",\n                title: \"Hubcv\",\n                status: \"not-good\",\n                icon: \"🔗\"\n            },\n            {\n                id: \"15\",\n                title: \"Codelude\",\n                status: \"not-good\",\n                icon: \"⚡\"\n            }\n        ],\n        finance: [\n            {\n                id: \"16\",\n                title: \"Net Worth\",\n                status: \"in-progress\",\n                icon: \"🏛️\"\n            },\n            {\n                id: \"17\",\n                title: \"Loans & Credits\",\n                status: \"not-good\",\n                icon: \"💳\"\n            },\n            {\n                id: \"18\",\n                title: \"Assets\",\n                status: \"not-started\",\n                icon: \"💎\"\n            },\n            {\n                id: \"19\",\n                title: \"Charity\",\n                status: \"not-started\",\n                icon: \"❤️\"\n            },\n            {\n                id: \"20\",\n                title: \"Investments\",\n                status: \"not-started\",\n                icon: \"💹\"\n            }\n        ],\n        weekend: [\n            {\n                id: \"21\",\n                title: \"Memories\",\n                status: \"in-progress\",\n                icon: \"📸\"\n            },\n            {\n                id: \"22\",\n                title: \"Travels\",\n                status: \"in-progress\",\n                icon: \"✈️\"\n            },\n            {\n                id: \"23\",\n                title: \"Shopping\",\n                status: \"not-good\",\n                icon: \"🛍️\"\n            },\n            {\n                id: \"24\",\n                title: \"Branding\",\n                status: \"not-started\",\n                icon: \"⭐\"\n            },\n            {\n                id: \"25\",\n                title: \"Events\",\n                status: \"not-started\",\n                icon: \"🎉\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Category.useEffect\": ()=>{\n            loadGoals();\n        }\n    }[\"Category.useEffect\"], [\n        category.id\n    ]);\n    const loadGoals = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load goals from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.list(userId, category.$id || category.id);\n                if (result.documents.length > 0) {\n                    setGoals(result.documents);\n                } else {\n                    // If no goals exist, create sample goals for this category\n                    await createSampleGoals();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using sample data:\", dbError);\n                const categoryGoals = sampleGoals[category.id] || [];\n                setGoals(categoryGoals);\n            }\n        } catch (error) {\n            console.error(\"Error loading goals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createSampleGoals = async ()=>{\n        try {\n            var _category_name;\n            const categoryGoals = sampleGoals[category.id] || sampleGoals[(_category_name = category.name) === null || _category_name === void 0 ? void 0 : _category_name.toLowerCase()] || [];\n            const createdGoals = [];\n            for(let i = 0; i < categoryGoals.length; i++){\n                const goal = categoryGoals[i];\n                try {\n                    const created = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.create(userId, {\n                        title: goal.title,\n                        icon: goal.icon,\n                        status: goal.status,\n                        categoryId: category.$id || category.id,\n                        order: i\n                    });\n                    createdGoals.push(created);\n                } catch (error) {\n                    console.error(\"Error creating sample goal:\", error);\n                    // If database creation fails, use sample data\n                    createdGoals.push(goal);\n                }\n            }\n            setGoals(createdGoals);\n        } catch (error) {\n            console.error(\"Error creating sample goals:\", error);\n            const categoryGoals = sampleGoals[category.id] || [];\n            setGoals(categoryGoals);\n        }\n    };\n    const addGoal = (newGoal)=>{\n        const goal = {\n            id: Date.now().toString(),\n            ...newGoal,\n            status: \"not-started\"\n        };\n        setGoals([\n            ...goals,\n            goal\n        ]);\n    };\n    const updateGoal = (goalId, updates)=>{\n        setGoals(goals.map((goal)=>goal.id === goalId ? {\n                ...goal,\n                ...updates\n            } : goal));\n    };\n    const deleteGoal = (goalId)=>{\n        setGoals(goals.filter((goal)=>goal.id !== goalId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(category.color, \" rounded-lg p-4 min-h-[400px] shadow-lg\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddModal(true),\n                        className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoalCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    goal: goal,\n                                    onUpdate: updateGoal,\n                                    onDelete: deleteGoal\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, goal.id, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    goals.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/70 text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No goals yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"mt-2 text-white/90 hover:text-white underline\",\n                                children: \"Add your first goal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddGoalModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onAdd: addGoal,\n                categoryName: category.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Category, \"UvPV4U8DXw8RWQ0vermlCsxiApQ=\");\n_c = Category;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\nvar _c;\n$RefreshReg$(_c, \"Category\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NhdGVnb3J5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNZO0FBQ3BCO0FBQ0Y7QUFDUTtBQUNHO0FBRTdDLE1BQU1RLFdBQVc7UUFBQyxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRTs7SUFDcEMsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFDLEVBQUU7SUFDckMsTUFBTSxDQUFDYSxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDZSxTQUFTQyxXQUFXLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUV2QywrQ0FBK0M7SUFDL0MsTUFBTWlCLGNBQWM7UUFDbEJDLFVBQVU7WUFDUjtnQkFBRUMsSUFBSTtnQkFBS0MsT0FBTztnQkFBYUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQ2pFO2dCQUFFSCxJQUFJO2dCQUFLQyxPQUFPO2dCQUFVQyxRQUFRO2dCQUFZQyxNQUFNO1lBQWM7WUFDcEU7Z0JBQUVILElBQUk7Z0JBQUtDLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQVlDLE1BQU07WUFBSztZQUM3RDtnQkFBRUgsSUFBSTtnQkFBS0MsT0FBTztnQkFBUUMsUUFBUTtnQkFBWUMsTUFBTTtZQUFLO1lBQ3pEO2dCQUFFSCxJQUFJO2dCQUFLQyxPQUFPO2dCQUFXQyxRQUFRO2dCQUFZQyxNQUFNO1lBQUs7U0FDN0Q7UUFDREMsT0FBTztZQUNMO2dCQUFFSixJQUFJO2dCQUFLQyxPQUFPO2dCQUFVQyxRQUFRO2dCQUFRQyxNQUFNO1lBQUs7WUFDdkQ7Z0JBQUVILElBQUk7Z0JBQUtDLE9BQU87Z0JBQVVDLFFBQVE7Z0JBQVFDLE1BQU07WUFBSztZQUN2RDtnQkFBRUgsSUFBSTtnQkFBS0MsT0FBTztnQkFBWUMsUUFBUTtnQkFBUUMsTUFBTTtZQUFLO1lBQ3pEO2dCQUFFSCxJQUFJO2dCQUFLQyxPQUFPO2dCQUFhQyxRQUFRO2dCQUFRQyxNQUFNO1lBQUs7WUFDMUQ7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQVdDLFFBQVE7Z0JBQVFDLE1BQU07WUFBSztTQUMxRDtRQUNERSxNQUFNO1lBQ0o7Z0JBQUVMLElBQUk7Z0JBQU1DLE9BQU87Z0JBQVdDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSztZQUNoRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBZUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQ3BFO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFxQkMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQzFFO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFTQyxRQUFRO2dCQUFZQyxNQUFNO1lBQUs7WUFDM0Q7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQVlDLE1BQU07WUFBSTtTQUM5RDtRQUNERyxTQUFTO1lBQ1A7Z0JBQUVOLElBQUk7Z0JBQU1DLE9BQU87Z0JBQWFDLFFBQVE7Z0JBQWVDLE1BQU07WUFBTTtZQUNuRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBbUJDLFFBQVE7Z0JBQVlDLE1BQU07WUFBSztZQUNyRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBVUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQy9EO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFXQyxRQUFRO2dCQUFlQyxNQUFNO1lBQUs7WUFDaEU7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQWVDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSztTQUNyRTtRQUNESSxTQUFTO1lBQ1A7Z0JBQUVQLElBQUk7Z0JBQU1DLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSztZQUNqRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBV0MsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQ2hFO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFZQyxRQUFRO2dCQUFZQyxNQUFNO1lBQU07WUFDL0Q7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSTtZQUNoRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBVUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1NBQ2hFO0lBQ0g7SUFFQXJCLGdEQUFTQTs4QkFBQztZQUNSMEI7UUFDRjs2QkFBRztRQUFDbEIsU0FBU1UsRUFBRTtLQUFDO0lBRWhCLE1BQU1RLFlBQVk7UUFDaEIsSUFBSTtZQUNGWCxXQUFXO1lBRVgsa0NBQWtDO1lBQ2xDLElBQUk7Z0JBQ0YsTUFBTVksU0FBUyxNQUFNckIsc0RBQVdBLENBQUNzQixJQUFJLENBQUNuQixRQUFRRCxTQUFTcUIsR0FBRyxJQUFJckIsU0FBU1UsRUFBRTtnQkFDekUsSUFBSVMsT0FBT0csU0FBUyxDQUFDQyxNQUFNLEdBQUcsR0FBRztvQkFDL0JwQixTQUFTZ0IsT0FBT0csU0FBUztnQkFDM0IsT0FBTztvQkFDTCwyREFBMkQ7b0JBQzNELE1BQU1FO2dCQUNSO1lBQ0YsRUFBRSxPQUFPQyxTQUFTO2dCQUNoQkMsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ0Y7Z0JBQ3RELE1BQU1HLGdCQUFnQnBCLFdBQVcsQ0FBQ1IsU0FBU1UsRUFBRSxDQUFDLElBQUksRUFBRTtnQkFDcERQLFNBQVN5QjtZQUNYO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3hDLFNBQVU7WUFDUnRCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWlCLG9CQUFvQjtRQUN4QixJQUFJO2dCQUM0RHhCO1lBQTlELE1BQU00QixnQkFBZ0JwQixXQUFXLENBQUNSLFNBQVNVLEVBQUUsQ0FBQyxJQUFJRixXQUFXLEVBQUNSLGlCQUFBQSxTQUFTOEIsSUFBSSxjQUFiOUIscUNBQUFBLGVBQWUrQixXQUFXLEdBQUcsSUFBSSxFQUFFO1lBQ2pHLE1BQU1DLGVBQWUsRUFBRTtZQUV2QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUwsY0FBY0wsTUFBTSxFQUFFVSxJQUFLO2dCQUM3QyxNQUFNQyxPQUFPTixhQUFhLENBQUNLLEVBQUU7Z0JBQzdCLElBQUk7b0JBQ0YsTUFBTUUsVUFBVSxNQUFNckMsc0RBQVdBLENBQUNzQyxNQUFNLENBQUNuQyxRQUFRO3dCQUMvQ1UsT0FBT3VCLEtBQUt2QixLQUFLO3dCQUNqQkUsTUFBTXFCLEtBQUtyQixJQUFJO3dCQUNmRCxRQUFRc0IsS0FBS3RCLE1BQU07d0JBQ25CeUIsWUFBWXJDLFNBQVNxQixHQUFHLElBQUlyQixTQUFTVSxFQUFFO3dCQUN2QzRCLE9BQU9MO29CQUNUO29CQUNBRCxhQUFhTyxJQUFJLENBQUNKO2dCQUNwQixFQUFFLE9BQU9OLE9BQU87b0JBQ2RILFFBQVFHLEtBQUssQ0FBQywrQkFBK0JBO29CQUM3Qyw4Q0FBOEM7b0JBQzlDRyxhQUFhTyxJQUFJLENBQUNMO2dCQUNwQjtZQUNGO1lBQ0EvQixTQUFTNkI7UUFDWCxFQUFFLE9BQU9ILE9BQU87WUFDZEgsUUFBUUcsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsTUFBTUQsZ0JBQWdCcEIsV0FBVyxDQUFDUixTQUFTVSxFQUFFLENBQUMsSUFBSSxFQUFFO1lBQ3BEUCxTQUFTeUI7UUFDWDtJQUNGO0lBRUEsTUFBTVksVUFBVSxDQUFDQztRQUNmLE1BQU1QLE9BQU87WUFDWHhCLElBQUlnQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7WUFDdkIsR0FBR0gsT0FBTztZQUNWN0IsUUFBUTtRQUNWO1FBQ0FULFNBQVM7ZUFBSUQ7WUFBT2dDO1NBQUs7SUFDM0I7SUFFQSxNQUFNVyxhQUFhLENBQUNDLFFBQVFDO1FBQzFCNUMsU0FBU0QsTUFBTThDLEdBQUcsQ0FBQ2QsQ0FBQUEsT0FDakJBLEtBQUt4QixFQUFFLEtBQUtvQyxTQUFTO2dCQUFFLEdBQUdaLElBQUk7Z0JBQUUsR0FBR2EsT0FBTztZQUFDLElBQUliO0lBRW5EO0lBRUEsTUFBTWUsYUFBYSxDQUFDSDtRQUNsQjNDLFNBQVNELE1BQU1nRCxNQUFNLENBQUNoQixDQUFBQSxPQUFRQSxLQUFLeEIsRUFBRSxLQUFLb0M7SUFDNUM7SUFFQSxxQkFDRSw4REFBQ0s7UUFBSUMsV0FBVyxHQUFrQixPQUFmcEQsU0FBU3FELEtBQUssRUFBQzs7MEJBQ2hDLDhEQUFDRjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNFO3dCQUFHRixXQUFVO2tDQUFnQ3BELFNBQVM4QixJQUFJOzs7Ozs7a0NBQzNELDhEQUFDeUI7d0JBQ0NDLFNBQVMsSUFBTW5ELGdCQUFnQjt3QkFDL0IrQyxXQUFVO2tDQUVWLDRFQUFDekQsZ0ZBQUlBOzRCQUFDOEQsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSWhCLDhEQUFDTjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUMxRCwwREFBZUE7a0NBQ2JRLE1BQU04QyxHQUFHLENBQUMsQ0FBQ2QsTUFBTXdCLHNCQUNoQiw4REFBQ2pFLGlEQUFNQSxDQUFDMEQsR0FBRztnQ0FFVFEsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRztnQ0FDN0JDLFNBQVM7b0NBQUVGLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUU7Z0NBQzVCRSxNQUFNO29DQUFFSCxTQUFTO29DQUFHQyxHQUFHLENBQUM7Z0NBQUc7Z0NBQzNCRyxZQUFZO29DQUFFQyxVQUFVO29DQUFLQyxPQUFPUixRQUFRO2dDQUFLOzBDQUVqRCw0RUFBQzlELGlEQUFRQTtvQ0FDUHNDLE1BQU1BO29DQUNOaUMsVUFBVXRCO29DQUNWdUIsVUFBVW5COzs7Ozs7K0JBVFBmLEtBQUt4QixFQUFFOzs7Ozs7Ozs7O29CQWVqQlIsTUFBTXFCLE1BQU0sS0FBSyxLQUFLLENBQUNqQix5QkFDdEIsOERBQUM2Qzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNpQjswQ0FBRTs7Ozs7OzBDQUNILDhEQUFDZDtnQ0FDQ0MsU0FBUyxJQUFNbkQsZ0JBQWdCO2dDQUMvQitDLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPUCw4REFBQ3ZELHFEQUFZQTtnQkFDWHlFLFFBQVFsRTtnQkFDUm1FLFNBQVMsSUFBTWxFLGdCQUFnQjtnQkFDL0JtRSxPQUFPaEM7Z0JBQ1BpQyxjQUFjekUsU0FBUzhCLElBQUk7Ozs7Ozs7Ozs7OztBQUluQztHQTlLTS9CO0tBQUFBO0FBZ0xOLGlFQUFlQSxRQUFRQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc2hhd2F6L0RldmVsb3Blci9zeGUvc3hlX2NsaWVudC9zcmMvY29tcG9uZW50cy9DYXRlZ29yeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuaW1wb3J0IHsgUGx1cyB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCBHb2FsQ2FyZCBmcm9tIFwiLi9Hb2FsQ2FyZFwiO1xuaW1wb3J0IEFkZEdvYWxNb2RhbCBmcm9tIFwiLi9BZGRHb2FsTW9kYWxcIjtcbmltcG9ydCB7IGdvYWxTZXJ2aWNlIH0gZnJvbSBcIkAvbGliL2RhdGFiYXNlXCI7XG5cbmNvbnN0IENhdGVnb3J5ID0gKHsgY2F0ZWdvcnksIHVzZXJJZCB9KSA9PiB7XG4gIGNvbnN0IFtnb2Fscywgc2V0R29hbHNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbc2hvd0FkZE1vZGFsLCBzZXRTaG93QWRkTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICAvLyBTYW1wbGUgZ29hbHMgYmFzZWQgb24gdGhlIHZpc2lvbiBib2FyZCBpbWFnZVxuICBjb25zdCBzYW1wbGVHb2FscyA9IHtcbiAgICBwZXJzb25hbDogW1xuICAgICAgeyBpZDogXCIxXCIsIHRpdGxlOiBcIkRvY3VtZW50c1wiLCBzdGF0dXM6IFwiaW4tcHJvZ3Jlc3NcIiwgaWNvbjogXCLwn5OEXCIgfSxcbiAgICAgIHsgaWQ6IFwiMlwiLCB0aXRsZTogXCJGYW1pbHlcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi8J+RqOKAjfCfkanigI3wn5Gn4oCN8J+RplwiIH0sXG4gICAgICB7IGlkOiBcIjNcIiwgdGl0bGU6IFwiRXhlcmNpc2VcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi8J+SqlwiIH0sXG4gICAgICB7IGlkOiBcIjRcIiwgdGl0bGU6IFwiRGlldFwiLCBzdGF0dXM6IFwibm90LWdvb2RcIiwgaWNvbjogXCLwn6WXXCIgfSxcbiAgICAgIHsgaWQ6IFwiNVwiLCB0aXRsZTogXCJQcmF5ZXJzXCIsIHN0YXR1czogXCJub3QtZ29vZFwiLCBpY29uOiBcIvCfmY9cIiB9LFxuICAgIF0sXG4gICAgbGVhcm46IFtcbiAgICAgIHsgaWQ6IFwiNlwiLCB0aXRsZTogXCJDb2RpbmdcIiwgc3RhdHVzOiBcImRvbmVcIiwgaWNvbjogXCLwn5K7XCIgfSxcbiAgICAgIHsgaWQ6IFwiN1wiLCB0aXRsZTogXCJEZXNpZ25cIiwgc3RhdHVzOiBcImRvbmVcIiwgaWNvbjogXCLwn46oXCIgfSxcbiAgICAgIHsgaWQ6IFwiOFwiLCB0aXRsZTogXCJCdXNpbmVzc1wiLCBzdGF0dXM6IFwiZG9uZVwiLCBpY29uOiBcIvCfkrxcIiB9LFxuICAgICAgeyBpZDogXCI5XCIsIHRpdGxlOiBcIk1hcmtldGluZ1wiLCBzdGF0dXM6IFwiZG9uZVwiLCBpY29uOiBcIvCfk4hcIiB9LFxuICAgICAgeyBpZDogXCIxMFwiLCB0aXRsZTogXCJGaW5hbmNlXCIsIHN0YXR1czogXCJkb25lXCIsIGljb246IFwi8J+SsFwiIH0sXG4gICAgXSxcbiAgICB3b3JrOiBbXG4gICAgICB7IGlkOiBcIjExXCIsIHRpdGxlOiBcIlRyYWRpbmdcIiwgc3RhdHVzOiBcImluLXByb2dyZXNzXCIsIGljb246IFwi8J+TilwiIH0sXG4gICAgICB7IGlkOiBcIjEyXCIsIHRpdGxlOiBcIlJlYWwgRXN0YXRlXCIsIHN0YXR1czogXCJpbi1wcm9ncmVzc1wiLCBpY29uOiBcIvCfj6BcIiB9LFxuICAgICAgeyBpZDogXCIxM1wiLCB0aXRsZTogXCJEaWdpdGFsIE1hcmtldGluZ1wiLCBzdGF0dXM6IFwiaW4tcHJvZ3Jlc3NcIiwgaWNvbjogXCLwn5OxXCIgfSxcbiAgICAgIHsgaWQ6IFwiMTRcIiwgdGl0bGU6IFwiSHViY3ZcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi8J+Ul1wiIH0sXG4gICAgICB7IGlkOiBcIjE1XCIsIHRpdGxlOiBcIkNvZGVsdWRlXCIsIHN0YXR1czogXCJub3QtZ29vZFwiLCBpY29uOiBcIuKaoVwiIH0sXG4gICAgXSxcbiAgICBmaW5hbmNlOiBbXG4gICAgICB7IGlkOiBcIjE2XCIsIHRpdGxlOiBcIk5ldCBXb3J0aFwiLCBzdGF0dXM6IFwiaW4tcHJvZ3Jlc3NcIiwgaWNvbjogXCLwn4+b77iPXCIgfSxcbiAgICAgIHsgaWQ6IFwiMTdcIiwgdGl0bGU6IFwiTG9hbnMgJiBDcmVkaXRzXCIsIHN0YXR1czogXCJub3QtZ29vZFwiLCBpY29uOiBcIvCfkrNcIiB9LFxuICAgICAgeyBpZDogXCIxOFwiLCB0aXRsZTogXCJBc3NldHNcIiwgc3RhdHVzOiBcIm5vdC1zdGFydGVkXCIsIGljb246IFwi8J+SjlwiIH0sXG4gICAgICB7IGlkOiBcIjE5XCIsIHRpdGxlOiBcIkNoYXJpdHlcIiwgc3RhdHVzOiBcIm5vdC1zdGFydGVkXCIsIGljb246IFwi4p2k77iPXCIgfSxcbiAgICAgIHsgaWQ6IFwiMjBcIiwgdGl0bGU6IFwiSW52ZXN0bWVudHNcIiwgc3RhdHVzOiBcIm5vdC1zdGFydGVkXCIsIGljb246IFwi8J+SuVwiIH0sXG4gICAgXSxcbiAgICB3ZWVrZW5kOiBbXG4gICAgICB7IGlkOiBcIjIxXCIsIHRpdGxlOiBcIk1lbW9yaWVzXCIsIHN0YXR1czogXCJpbi1wcm9ncmVzc1wiLCBpY29uOiBcIvCfk7hcIiB9LFxuICAgICAgeyBpZDogXCIyMlwiLCB0aXRsZTogXCJUcmF2ZWxzXCIsIHN0YXR1czogXCJpbi1wcm9ncmVzc1wiLCBpY29uOiBcIuKciO+4j1wiIH0sXG4gICAgICB7IGlkOiBcIjIzXCIsIHRpdGxlOiBcIlNob3BwaW5nXCIsIHN0YXR1czogXCJub3QtZ29vZFwiLCBpY29uOiBcIvCfm43vuI9cIiB9LFxuICAgICAgeyBpZDogXCIyNFwiLCB0aXRsZTogXCJCcmFuZGluZ1wiLCBzdGF0dXM6IFwibm90LXN0YXJ0ZWRcIiwgaWNvbjogXCLirZBcIiB9LFxuICAgICAgeyBpZDogXCIyNVwiLCB0aXRsZTogXCJFdmVudHNcIiwgc3RhdHVzOiBcIm5vdC1zdGFydGVkXCIsIGljb246IFwi8J+OiVwiIH0sXG4gICAgXSxcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRHb2FscygpO1xuICB9LCBbY2F0ZWdvcnkuaWRdKTtcblxuICBjb25zdCBsb2FkR29hbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIFRyeSB0byBsb2FkIGdvYWxzIGZyb20gZGF0YWJhc2VcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGdvYWxTZXJ2aWNlLmxpc3QodXNlcklkLCBjYXRlZ29yeS4kaWQgfHwgY2F0ZWdvcnkuaWQpO1xuICAgICAgICBpZiAocmVzdWx0LmRvY3VtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0R29hbHMocmVzdWx0LmRvY3VtZW50cyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gSWYgbm8gZ29hbHMgZXhpc3QsIGNyZWF0ZSBzYW1wbGUgZ29hbHMgZm9yIHRoaXMgY2F0ZWdvcnlcbiAgICAgICAgICBhd2FpdCBjcmVhdGVTYW1wbGVHb2FscygpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChkYkVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiRGF0YWJhc2Ugbm90IHJlYWR5LCB1c2luZyBzYW1wbGUgZGF0YTpcIiwgZGJFcnJvcik7XG4gICAgICAgIGNvbnN0IGNhdGVnb3J5R29hbHMgPSBzYW1wbGVHb2Fsc1tjYXRlZ29yeS5pZF0gfHwgW107XG4gICAgICAgIHNldEdvYWxzKGNhdGVnb3J5R29hbHMpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbG9hZGluZyBnb2FsczpcIiwgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgY3JlYXRlU2FtcGxlR29hbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhdGVnb3J5R29hbHMgPSBzYW1wbGVHb2Fsc1tjYXRlZ29yeS5pZF0gfHwgc2FtcGxlR29hbHNbY2F0ZWdvcnkubmFtZT8udG9Mb3dlckNhc2UoKV0gfHwgW107XG4gICAgICBjb25zdCBjcmVhdGVkR29hbHMgPSBbXTtcblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjYXRlZ29yeUdvYWxzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGdvYWwgPSBjYXRlZ29yeUdvYWxzW2ldO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGNyZWF0ZWQgPSBhd2FpdCBnb2FsU2VydmljZS5jcmVhdGUodXNlcklkLCB7XG4gICAgICAgICAgICB0aXRsZTogZ29hbC50aXRsZSxcbiAgICAgICAgICAgIGljb246IGdvYWwuaWNvbixcbiAgICAgICAgICAgIHN0YXR1czogZ29hbC5zdGF0dXMsXG4gICAgICAgICAgICBjYXRlZ29yeUlkOiBjYXRlZ29yeS4kaWQgfHwgY2F0ZWdvcnkuaWQsXG4gICAgICAgICAgICBvcmRlcjogaVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGNyZWF0ZWRHb2Fscy5wdXNoKGNyZWF0ZWQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBjcmVhdGluZyBzYW1wbGUgZ29hbDpcIiwgZXJyb3IpO1xuICAgICAgICAgIC8vIElmIGRhdGFiYXNlIGNyZWF0aW9uIGZhaWxzLCB1c2Ugc2FtcGxlIGRhdGFcbiAgICAgICAgICBjcmVhdGVkR29hbHMucHVzaChnb2FsKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgc2V0R29hbHMoY3JlYXRlZEdvYWxzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIHNhbXBsZSBnb2FsczpcIiwgZXJyb3IpO1xuICAgICAgY29uc3QgY2F0ZWdvcnlHb2FscyA9IHNhbXBsZUdvYWxzW2NhdGVnb3J5LmlkXSB8fCBbXTtcbiAgICAgIHNldEdvYWxzKGNhdGVnb3J5R29hbHMpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBhZGRHb2FsID0gKG5ld0dvYWwpID0+IHtcbiAgICBjb25zdCBnb2FsID0ge1xuICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIC4uLm5ld0dvYWwsXG4gICAgICBzdGF0dXM6IFwibm90LXN0YXJ0ZWRcIixcbiAgICB9O1xuICAgIHNldEdvYWxzKFsuLi5nb2FscywgZ29hbF0pO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUdvYWwgPSAoZ29hbElkLCB1cGRhdGVzKSA9PiB7XG4gICAgc2V0R29hbHMoZ29hbHMubWFwKGdvYWwgPT4gXG4gICAgICBnb2FsLmlkID09PSBnb2FsSWQgPyB7IC4uLmdvYWwsIC4uLnVwZGF0ZXMgfSA6IGdvYWxcbiAgICApKTtcbiAgfTtcblxuICBjb25zdCBkZWxldGVHb2FsID0gKGdvYWxJZCkgPT4ge1xuICAgIHNldEdvYWxzKGdvYWxzLmZpbHRlcihnb2FsID0+IGdvYWwuaWQgIT09IGdvYWxJZCkpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2Ake2NhdGVnb3J5LmNvbG9yfSByb3VuZGVkLWxnIHAtNCBtaW4taC1bNDAwcHhdIHNoYWRvdy1sZ2B9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPntjYXRlZ29yeS5uYW1lfTwvaDI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgcC0xIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxQbHVzIHNpemU9ezIwfSAvPlxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICA8QW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgIHtnb2Fscy5tYXAoKGdvYWwsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e2dvYWwuaWR9XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogLTIwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMywgZGVsYXk6IGluZGV4ICogMC4wNSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8R29hbENhcmRcbiAgICAgICAgICAgICAgICBnb2FsPXtnb2FsfVxuICAgICAgICAgICAgICAgIG9uVXBkYXRlPXt1cGRhdGVHb2FsfVxuICAgICAgICAgICAgICAgIG9uRGVsZXRlPXtkZWxldGVHb2FsfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cblxuICAgICAgICB7Z29hbHMubGVuZ3RoID09PSAwICYmICFsb2FkaW5nICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNzAgdGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgPHA+Tm8gZ29hbHMgeWV0PC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTIgdGV4dC13aGl0ZS85MCBob3Zlcjp0ZXh0LXdoaXRlIHVuZGVybGluZVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEFkZCB5b3VyIGZpcnN0IGdvYWxcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxBZGRHb2FsTW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93QWRkTW9kYWx9XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dBZGRNb2RhbChmYWxzZSl9XG4gICAgICAgIG9uQWRkPXthZGRHb2FsfVxuICAgICAgICBjYXRlZ29yeU5hbWU9e2NhdGVnb3J5Lm5hbWV9XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ2F0ZWdvcnk7XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJQbHVzIiwiR29hbENhcmQiLCJBZGRHb2FsTW9kYWwiLCJnb2FsU2VydmljZSIsIkNhdGVnb3J5IiwiY2F0ZWdvcnkiLCJ1c2VySWQiLCJnb2FscyIsInNldEdvYWxzIiwic2hvd0FkZE1vZGFsIiwic2V0U2hvd0FkZE1vZGFsIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzYW1wbGVHb2FscyIsInBlcnNvbmFsIiwiaWQiLCJ0aXRsZSIsInN0YXR1cyIsImljb24iLCJsZWFybiIsIndvcmsiLCJmaW5hbmNlIiwid2Vla2VuZCIsImxvYWRHb2FscyIsInJlc3VsdCIsImxpc3QiLCIkaWQiLCJkb2N1bWVudHMiLCJsZW5ndGgiLCJjcmVhdGVTYW1wbGVHb2FscyIsImRiRXJyb3IiLCJjb25zb2xlIiwibG9nIiwiY2F0ZWdvcnlHb2FscyIsImVycm9yIiwibmFtZSIsInRvTG93ZXJDYXNlIiwiY3JlYXRlZEdvYWxzIiwiaSIsImdvYWwiLCJjcmVhdGVkIiwiY3JlYXRlIiwiY2F0ZWdvcnlJZCIsIm9yZGVyIiwicHVzaCIsImFkZEdvYWwiLCJuZXdHb2FsIiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwidXBkYXRlR29hbCIsImdvYWxJZCIsInVwZGF0ZXMiLCJtYXAiLCJkZWxldGVHb2FsIiwiZmlsdGVyIiwiZGl2IiwiY2xhc3NOYW1lIiwiY29sb3IiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzaXplIiwiaW5kZXgiLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJhbmltYXRlIiwiZXhpdCIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImRlbGF5Iiwib25VcGRhdGUiLCJvbkRlbGV0ZSIsInAiLCJpc09wZW4iLCJvbkNsb3NlIiwib25BZGQiLCJjYXRlZ29yeU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Category.js\n"));

/***/ })

});