"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fe2a5d42baea\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZlMmE1ZDQyYmFlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Category.js":
/*!************************************!*\
  !*** ./src/components/Category.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _AddGoalModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddGoalModal */ \"(app-pages-browser)/./src/components/AddGoalModal.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Category = (param)=>{\n    let { category, userId } = param;\n    _s();\n    const [goals, setGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Sample goals based on the vision board image\n    const sampleGoals = {\n        personal: [\n            {\n                id: \"1\",\n                title: \"Documents\",\n                status: \"in-progress\",\n                icon: \"📄\"\n            },\n            {\n                id: \"2\",\n                title: \"Family\",\n                status: \"not-good\",\n                icon: \"👨‍👩‍👧‍👦\"\n            },\n            {\n                id: \"3\",\n                title: \"Exercise\",\n                status: \"not-good\",\n                icon: \"💪\"\n            },\n            {\n                id: \"4\",\n                title: \"Diet\",\n                status: \"not-good\",\n                icon: \"🥗\"\n            },\n            {\n                id: \"5\",\n                title: \"Prayers\",\n                status: \"not-good\",\n                icon: \"🙏\"\n            }\n        ],\n        learn: [\n            {\n                id: \"6\",\n                title: \"Coding\",\n                status: \"done\",\n                icon: \"💻\"\n            },\n            {\n                id: \"7\",\n                title: \"Design\",\n                status: \"done\",\n                icon: \"🎨\"\n            },\n            {\n                id: \"8\",\n                title: \"Business\",\n                status: \"done\",\n                icon: \"💼\"\n            },\n            {\n                id: \"9\",\n                title: \"Marketing\",\n                status: \"done\",\n                icon: \"📈\"\n            },\n            {\n                id: \"10\",\n                title: \"Finance\",\n                status: \"done\",\n                icon: \"💰\"\n            }\n        ],\n        work: [\n            {\n                id: \"11\",\n                title: \"Trading\",\n                status: \"in-progress\",\n                icon: \"📊\"\n            },\n            {\n                id: \"12\",\n                title: \"Real Estate\",\n                status: \"in-progress\",\n                icon: \"🏠\"\n            },\n            {\n                id: \"13\",\n                title: \"Digital Marketing\",\n                status: \"in-progress\",\n                icon: \"📱\"\n            },\n            {\n                id: \"14\",\n                title: \"Hubcv\",\n                status: \"not-good\",\n                icon: \"🔗\"\n            },\n            {\n                id: \"15\",\n                title: \"Codelude\",\n                status: \"not-good\",\n                icon: \"⚡\"\n            }\n        ],\n        finance: [\n            {\n                id: \"16\",\n                title: \"Net Worth\",\n                status: \"in-progress\",\n                icon: \"🏛️\"\n            },\n            {\n                id: \"17\",\n                title: \"Loans & Credits\",\n                status: \"not-good\",\n                icon: \"💳\"\n            },\n            {\n                id: \"18\",\n                title: \"Assets\",\n                status: \"not-started\",\n                icon: \"💎\"\n            },\n            {\n                id: \"19\",\n                title: \"Charity\",\n                status: \"not-started\",\n                icon: \"❤️\"\n            },\n            {\n                id: \"20\",\n                title: \"Investments\",\n                status: \"not-started\",\n                icon: \"💹\"\n            }\n        ],\n        weekend: [\n            {\n                id: \"21\",\n                title: \"Memories\",\n                status: \"in-progress\",\n                icon: \"📸\"\n            },\n            {\n                id: \"22\",\n                title: \"Travels\",\n                status: \"in-progress\",\n                icon: \"✈️\"\n            },\n            {\n                id: \"23\",\n                title: \"Shopping\",\n                status: \"not-good\",\n                icon: \"🛍️\"\n            },\n            {\n                id: \"24\",\n                title: \"Branding\",\n                status: \"not-started\",\n                icon: \"⭐\"\n            },\n            {\n                id: \"25\",\n                title: \"Events\",\n                status: \"not-started\",\n                icon: \"🎉\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Category.useEffect\": ()=>{\n            loadGoals();\n        }\n    }[\"Category.useEffect\"], [\n        category.id\n    ]);\n    const loadGoals = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load goals from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.list(userId, category.$id || category.id);\n                if (result.documents.length > 0) {\n                    setGoals(result.documents);\n                } else {\n                    // If no goals exist, create sample goals for this category\n                    await createSampleGoals();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using sample data:\", dbError);\n                const categoryGoals = sampleGoals[category.id] || [];\n                setGoals(categoryGoals);\n            }\n        } catch (error) {\n            console.error(\"Error loading goals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createSampleGoals = async ()=>{\n        try {\n            var _category_name;\n            const categoryGoals = sampleGoals[category.id] || sampleGoals[(_category_name = category.name) === null || _category_name === void 0 ? void 0 : _category_name.toLowerCase()] || [];\n            const createdGoals = [];\n            for(let i = 0; i < categoryGoals.length; i++){\n                const goal = categoryGoals[i];\n                try {\n                    const created = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.create(userId, {\n                        title: goal.title,\n                        icon: goal.icon,\n                        status: goal.status,\n                        categoryId: category.$id || category.id,\n                        order: i\n                    });\n                    createdGoals.push(created);\n                } catch (error) {\n                    console.error(\"Error creating sample goal:\", error);\n                    // If database creation fails, use sample data\n                    createdGoals.push(goal);\n                }\n            }\n            setGoals(createdGoals);\n        } catch (error) {\n            console.error(\"Error creating sample goals:\", error);\n            const categoryGoals = sampleGoals[category.id] || [];\n            setGoals(categoryGoals);\n        }\n    };\n    const addGoal = async (newGoal)=>{\n        try {\n            const created = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.create(userId, {\n                ...newGoal,\n                status: \"not-started\",\n                categoryId: category.$id || category.id,\n                order: goals.length\n            });\n            setGoals([\n                ...goals,\n                created\n            ]);\n        } catch (error) {\n            console.error(\"Error adding goal:\", error);\n            // Fallback to local state\n            const goal = {\n                id: Date.now().toString(),\n                ...newGoal,\n                status: \"not-started\"\n            };\n            setGoals([\n                ...goals,\n                goal\n            ]);\n        }\n    };\n    const updateGoal = async (goalId, updates)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.update(goalId, updates);\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        } catch (error) {\n            console.error(\"Error updating goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        }\n    };\n    const deleteGoal = async (goalId)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.delete(goalId);\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        } catch (error) {\n            console.error(\"Error deleting goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(category.color, \" rounded-lg p-4 min-h-[400px] shadow-lg\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddModal(true),\n                        className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoalCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    goal: goal,\n                                    onUpdate: updateGoal,\n                                    onDelete: deleteGoal\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, goal.id, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    goals.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/70 text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No goals yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"mt-2 text-white/90 hover:text-white underline\",\n                                children: \"Add your first goal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddGoalModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onAdd: addGoal,\n                categoryName: category.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Category, \"UvPV4U8DXw8RWQ0vermlCsxiApQ=\");\n_c = Category;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\nvar _c;\n$RefreshReg$(_c, \"Category\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Category.js\n"));

/***/ })

});