"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"02263fb08d6f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjAyMjYzZmIwOGQ2ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"Ku/3fYTZ4p+HhLbl/Ex0fsiHh1U=\");\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useRealtime.js":
/*!**********************************!*\
  !*** ./src/hooks/useRealtime.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useRealtime: () => (/* binding */ useRealtime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_appwrite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/appwrite */ \"(app-pages-browser)/./src/lib/appwrite.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ useRealtime,default auto */ var _s = $RefreshSig$();\n\n\n\nconst useRealtime = (userId, onCategoryUpdate, onGoalUpdate)=>{\n    _s();\n    const handleRealtimeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealtime.useCallback[handleRealtimeUpdate]\": (response)=>{\n            const { events, payload } = response;\n            // Handle category updates\n            if (events.some({\n                \"useRealtime.useCallback[handleRealtimeUpdate]\": (event)=>event.includes(\"databases.\".concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.DATABASE_ID, \".collections.\").concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CATEGORIES))\n            }[\"useRealtime.useCallback[handleRealtimeUpdate]\"])) {\n                if (payload.userId === userId) {\n                    onCategoryUpdate && onCategoryUpdate(payload, events);\n                }\n            }\n            // Handle goal updates\n            if (events.some({\n                \"useRealtime.useCallback[handleRealtimeUpdate]\": (event)=>event.includes(\"databases.\".concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.DATABASE_ID, \".collections.\").concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.GOALS))\n            }[\"useRealtime.useCallback[handleRealtimeUpdate]\"])) {\n                if (payload.userId === userId) {\n                    onGoalUpdate && onGoalUpdate(payload, events);\n                }\n            }\n        }\n    }[\"useRealtime.useCallback[handleRealtimeUpdate]\"], [\n        userId,\n        onCategoryUpdate,\n        onGoalUpdate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealtime.useEffect\": ()=>{\n            if (!userId) return;\n            // Subscribe to category changes\n            const categorySubscription = _lib_appwrite__WEBPACK_IMPORTED_MODULE_1__.client.subscribe(\"databases.\".concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.DATABASE_ID, \".collections.\").concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.CATEGORIES, \".documents\"), handleRealtimeUpdate);\n            // Subscribe to goal changes\n            const goalSubscription = _lib_appwrite__WEBPACK_IMPORTED_MODULE_1__.client.subscribe(\"databases.\".concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.DATABASE_ID, \".collections.\").concat(_lib_database__WEBPACK_IMPORTED_MODULE_2__.COLLECTIONS.GOALS, \".documents\"), handleRealtimeUpdate);\n            return ({\n                \"useRealtime.useEffect\": ()=>{\n                    categorySubscription();\n                    goalSubscription();\n                }\n            })[\"useRealtime.useEffect\"];\n        }\n    }[\"useRealtime.useEffect\"], [\n        userId,\n        handleRealtimeUpdate\n    ]);\n};\n_s(useRealtime, \"G+dS+ailqY0K1BeS23cTvxdDvh0=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRealtime);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZWFsdGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRStDO0FBQ1A7QUFDa0I7QUFFbkQsTUFBTUssY0FBYyxDQUFDQyxRQUFRQyxrQkFBa0JDOztJQUNwRCxNQUFNQyx1QkFBdUJSLGtEQUFXQTt5REFBQyxDQUFDUztZQUN4QyxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFLEdBQUdGO1lBRTVCLDBCQUEwQjtZQUMxQixJQUFJQyxPQUFPRSxJQUFJO2lFQUFDQyxDQUFBQSxRQUFTQSxNQUFNQyxRQUFRLENBQUMsYUFBd0NYLE9BQTNCRCxzREFBV0EsRUFBQyxpQkFBc0MsT0FBdkJDLHNEQUFXQSxDQUFDWSxVQUFVO2lFQUFNO2dCQUMxRyxJQUFJSixRQUFRTixNQUFNLEtBQUtBLFFBQVE7b0JBQzdCQyxvQkFBb0JBLGlCQUFpQkssU0FBU0Q7Z0JBQ2hEO1lBQ0Y7WUFFQSxzQkFBc0I7WUFDdEIsSUFBSUEsT0FBT0UsSUFBSTtpRUFBQ0MsQ0FBQUEsUUFBU0EsTUFBTUMsUUFBUSxDQUFDLGFBQXdDWCxPQUEzQkQsc0RBQVdBLEVBQUMsaUJBQWlDLE9BQWxCQyxzREFBV0EsQ0FBQ2EsS0FBSztpRUFBTTtnQkFDckcsSUFBSUwsUUFBUU4sTUFBTSxLQUFLQSxRQUFRO29CQUM3QkUsZ0JBQWdCQSxhQUFhSSxTQUFTRDtnQkFDeEM7WUFDRjtRQUNGO3dEQUFHO1FBQUNMO1FBQVFDO1FBQWtCQztLQUFhO0lBRTNDUixnREFBU0E7aUNBQUM7WUFDUixJQUFJLENBQUNNLFFBQVE7WUFFYixnQ0FBZ0M7WUFDaEMsTUFBTVksdUJBQXVCaEIsaURBQU1BLENBQUNpQixTQUFTLENBQzNDLGFBQXdDZixPQUEzQkQsc0RBQVdBLEVBQUMsaUJBQXNDLE9BQXZCQyxzREFBV0EsQ0FBQ1ksVUFBVSxFQUFDLGVBQy9EUDtZQUdGLDRCQUE0QjtZQUM1QixNQUFNVyxtQkFBbUJsQixpREFBTUEsQ0FBQ2lCLFNBQVMsQ0FDdkMsYUFBd0NmLE9BQTNCRCxzREFBV0EsRUFBQyxpQkFBaUMsT0FBbEJDLHNEQUFXQSxDQUFDYSxLQUFLLEVBQUMsZUFDMURSO1lBR0Y7eUNBQU87b0JBQ0xTO29CQUNBRTtnQkFDRjs7UUFDRjtnQ0FBRztRQUFDZDtRQUFRRztLQUFxQjtBQUNuQyxFQUFFO0dBdkNXSjtBQXlDYixpRUFBZUEsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3NoYXdhei9EZXZlbG9wZXIvc3hlL3N4ZV9jbGllbnQvc3JjL2hvb2tzL3VzZVJlYWx0aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjbGllbnQgfSBmcm9tIFwiQC9saWIvYXBwd3JpdGVcIjtcbmltcG9ydCB7IERBVEFCQVNFX0lELCBDT0xMRUNUSU9OUyB9IGZyb20gXCJAL2xpYi9kYXRhYmFzZVwiO1xuXG5leHBvcnQgY29uc3QgdXNlUmVhbHRpbWUgPSAodXNlcklkLCBvbkNhdGVnb3J5VXBkYXRlLCBvbkdvYWxVcGRhdGUpID0+IHtcbiAgY29uc3QgaGFuZGxlUmVhbHRpbWVVcGRhdGUgPSB1c2VDYWxsYmFjaygocmVzcG9uc2UpID0+IHtcbiAgICBjb25zdCB7IGV2ZW50cywgcGF5bG9hZCB9ID0gcmVzcG9uc2U7XG4gICAgXG4gICAgLy8gSGFuZGxlIGNhdGVnb3J5IHVwZGF0ZXNcbiAgICBpZiAoZXZlbnRzLnNvbWUoZXZlbnQgPT4gZXZlbnQuaW5jbHVkZXMoYGRhdGFiYXNlcy4ke0RBVEFCQVNFX0lEfS5jb2xsZWN0aW9ucy4ke0NPTExFQ1RJT05TLkNBVEVHT1JJRVN9YCkpKSB7XG4gICAgICBpZiAocGF5bG9hZC51c2VySWQgPT09IHVzZXJJZCkge1xuICAgICAgICBvbkNhdGVnb3J5VXBkYXRlICYmIG9uQ2F0ZWdvcnlVcGRhdGUocGF5bG9hZCwgZXZlbnRzKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgLy8gSGFuZGxlIGdvYWwgdXBkYXRlc1xuICAgIGlmIChldmVudHMuc29tZShldmVudCA9PiBldmVudC5pbmNsdWRlcyhgZGF0YWJhc2VzLiR7REFUQUJBU0VfSUR9LmNvbGxlY3Rpb25zLiR7Q09MTEVDVElPTlMuR09BTFN9YCkpKSB7XG4gICAgICBpZiAocGF5bG9hZC51c2VySWQgPT09IHVzZXJJZCkge1xuICAgICAgICBvbkdvYWxVcGRhdGUgJiYgb25Hb2FsVXBkYXRlKHBheWxvYWQsIGV2ZW50cyk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbdXNlcklkLCBvbkNhdGVnb3J5VXBkYXRlLCBvbkdvYWxVcGRhdGVdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghdXNlcklkKSByZXR1cm47XG5cbiAgICAvLyBTdWJzY3JpYmUgdG8gY2F0ZWdvcnkgY2hhbmdlc1xuICAgIGNvbnN0IGNhdGVnb3J5U3Vic2NyaXB0aW9uID0gY2xpZW50LnN1YnNjcmliZShcbiAgICAgIGBkYXRhYmFzZXMuJHtEQVRBQkFTRV9JRH0uY29sbGVjdGlvbnMuJHtDT0xMRUNUSU9OUy5DQVRFR09SSUVTfS5kb2N1bWVudHNgLFxuICAgICAgaGFuZGxlUmVhbHRpbWVVcGRhdGVcbiAgICApO1xuXG4gICAgLy8gU3Vic2NyaWJlIHRvIGdvYWwgY2hhbmdlc1xuICAgIGNvbnN0IGdvYWxTdWJzY3JpcHRpb24gPSBjbGllbnQuc3Vic2NyaWJlKFxuICAgICAgYGRhdGFiYXNlcy4ke0RBVEFCQVNFX0lEfS5jb2xsZWN0aW9ucy4ke0NPTExFQ1RJT05TLkdPQUxTfS5kb2N1bWVudHNgLFxuICAgICAgaGFuZGxlUmVhbHRpbWVVcGRhdGVcbiAgICApO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNhdGVnb3J5U3Vic2NyaXB0aW9uKCk7XG4gICAgICBnb2FsU3Vic2NyaXB0aW9uKCk7XG4gICAgfTtcbiAgfSwgW3VzZXJJZCwgaGFuZGxlUmVhbHRpbWVVcGRhdGVdKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHVzZVJlYWx0aW1lO1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiY2xpZW50IiwiREFUQUJBU0VfSUQiLCJDT0xMRUNUSU9OUyIsInVzZVJlYWx0aW1lIiwidXNlcklkIiwib25DYXRlZ29yeVVwZGF0ZSIsIm9uR29hbFVwZGF0ZSIsImhhbmRsZVJlYWx0aW1lVXBkYXRlIiwicmVzcG9uc2UiLCJldmVudHMiLCJwYXlsb2FkIiwic29tZSIsImV2ZW50IiwiaW5jbHVkZXMiLCJDQVRFR09SSUVTIiwiR09BTFMiLCJjYXRlZ29yeVN1YnNjcmlwdGlvbiIsInN1YnNjcmliZSIsImdvYWxTdWJzY3JpcHRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealtime.js\n"));

/***/ })

});