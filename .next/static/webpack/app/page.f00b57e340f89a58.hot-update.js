"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"358319ce2553\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM1ODMxOWNlMjU1M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Category.js":
/*!************************************!*\
  !*** ./src/components/Category.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _AddGoalModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddGoalModal */ \"(app-pages-browser)/./src/components/AddGoalModal.js\");\n/* harmony import */ var _AISuggestions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AISuggestions */ \"(app-pages-browser)/./src/components/AISuggestions.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Category = (param)=>{\n    let { category, userId } = param;\n    _s();\n    const [goals, setGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAISuggestions, setShowAISuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Sample goals based on the vision board image\n    const sampleGoals = {\n        personal: [\n            {\n                id: \"1\",\n                title: \"Documents\",\n                status: \"in-progress\",\n                icon: \"📄\"\n            },\n            {\n                id: \"2\",\n                title: \"Family\",\n                status: \"not-good\",\n                icon: \"👨‍👩‍👧‍👦\"\n            },\n            {\n                id: \"3\",\n                title: \"Exercise\",\n                status: \"not-good\",\n                icon: \"💪\"\n            },\n            {\n                id: \"4\",\n                title: \"Diet\",\n                status: \"not-good\",\n                icon: \"🥗\"\n            },\n            {\n                id: \"5\",\n                title: \"Prayers\",\n                status: \"not-good\",\n                icon: \"🙏\"\n            }\n        ],\n        learn: [\n            {\n                id: \"6\",\n                title: \"Coding\",\n                status: \"done\",\n                icon: \"💻\"\n            },\n            {\n                id: \"7\",\n                title: \"Design\",\n                status: \"done\",\n                icon: \"🎨\"\n            },\n            {\n                id: \"8\",\n                title: \"Business\",\n                status: \"done\",\n                icon: \"💼\"\n            },\n            {\n                id: \"9\",\n                title: \"Marketing\",\n                status: \"done\",\n                icon: \"📈\"\n            },\n            {\n                id: \"10\",\n                title: \"Finance\",\n                status: \"done\",\n                icon: \"💰\"\n            }\n        ],\n        work: [\n            {\n                id: \"11\",\n                title: \"Trading\",\n                status: \"in-progress\",\n                icon: \"📊\"\n            },\n            {\n                id: \"12\",\n                title: \"Real Estate\",\n                status: \"in-progress\",\n                icon: \"🏠\"\n            },\n            {\n                id: \"13\",\n                title: \"Digital Marketing\",\n                status: \"in-progress\",\n                icon: \"📱\"\n            },\n            {\n                id: \"14\",\n                title: \"Hubcv\",\n                status: \"not-good\",\n                icon: \"🔗\"\n            },\n            {\n                id: \"15\",\n                title: \"Codelude\",\n                status: \"not-good\",\n                icon: \"⚡\"\n            }\n        ],\n        finance: [\n            {\n                id: \"16\",\n                title: \"Net Worth\",\n                status: \"in-progress\",\n                icon: \"🏛️\"\n            },\n            {\n                id: \"17\",\n                title: \"Loans & Credits\",\n                status: \"not-good\",\n                icon: \"💳\"\n            },\n            {\n                id: \"18\",\n                title: \"Assets\",\n                status: \"not-started\",\n                icon: \"💎\"\n            },\n            {\n                id: \"19\",\n                title: \"Charity\",\n                status: \"not-started\",\n                icon: \"❤️\"\n            },\n            {\n                id: \"20\",\n                title: \"Investments\",\n                status: \"not-started\",\n                icon: \"💹\"\n            }\n        ],\n        weekend: [\n            {\n                id: \"21\",\n                title: \"Memories\",\n                status: \"in-progress\",\n                icon: \"📸\"\n            },\n            {\n                id: \"22\",\n                title: \"Travels\",\n                status: \"in-progress\",\n                icon: \"✈️\"\n            },\n            {\n                id: \"23\",\n                title: \"Shopping\",\n                status: \"not-good\",\n                icon: \"🛍️\"\n            },\n            {\n                id: \"24\",\n                title: \"Branding\",\n                status: \"not-started\",\n                icon: \"⭐\"\n            },\n            {\n                id: \"25\",\n                title: \"Events\",\n                status: \"not-started\",\n                icon: \"🎉\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Category.useEffect\": ()=>{\n            loadGoals();\n        }\n    }[\"Category.useEffect\"], [\n        category.id\n    ]);\n    const loadGoals = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load goals from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.list(userId, category.$id || category.id);\n                if (result.documents.length > 0) {\n                    setGoals(result.documents);\n                } else {\n                    // If no goals exist, create sample goals for this category\n                    await createSampleGoals();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using sample data:\", dbError);\n                const categoryGoals = sampleGoals[category.id] || [];\n                setGoals(categoryGoals);\n            }\n        } catch (error) {\n            console.error(\"Error loading goals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createSampleGoals = async ()=>{\n        try {\n            var _category_name;\n            const categoryGoals = sampleGoals[category.id] || sampleGoals[(_category_name = category.name) === null || _category_name === void 0 ? void 0 : _category_name.toLowerCase()] || [];\n            const createdGoals = [];\n            for(let i = 0; i < categoryGoals.length; i++){\n                const goal = categoryGoals[i];\n                try {\n                    const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.create(userId, {\n                        title: goal.title,\n                        icon: goal.icon,\n                        status: goal.status,\n                        categoryId: category.$id || category.id,\n                        order: i\n                    });\n                    createdGoals.push(created);\n                } catch (error) {\n                    console.error(\"Error creating sample goal:\", error);\n                    // If database creation fails, use sample data\n                    createdGoals.push(goal);\n                }\n            }\n            setGoals(createdGoals);\n        } catch (error) {\n            console.error(\"Error creating sample goals:\", error);\n            const categoryGoals = sampleGoals[category.id] || [];\n            setGoals(categoryGoals);\n        }\n    };\n    const addGoal = async (newGoal)=>{\n        try {\n            const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.create(userId, {\n                ...newGoal,\n                status: \"not-started\",\n                categoryId: category.$id || category.id,\n                order: goals.length\n            });\n            setGoals([\n                ...goals,\n                created\n            ]);\n        } catch (error) {\n            console.error(\"Error adding goal:\", error);\n            // Fallback to local state\n            const goal = {\n                id: Date.now().toString(),\n                ...newGoal,\n                status: \"not-started\"\n            };\n            setGoals([\n                ...goals,\n                goal\n            ]);\n        }\n    };\n    const updateGoal = async (goalId, updates)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.update(goalId, updates);\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        } catch (error) {\n            console.error(\"Error updating goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        }\n    };\n    const deleteGoal = async (goalId)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.delete(goalId);\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        } catch (error) {\n            console.error(\"Error deleting goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(category.color, \" rounded-lg p-4 min-h-[400px] shadow-lg hover:shadow-xl transition-shadow duration-300\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAISuggestions(true),\n                                className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                                title: \"AI Suggestions\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                                title: \"Add Goal\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoalCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    goal: goal,\n                                    onUpdate: updateGoal,\n                                    onDelete: deleteGoal\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, goal.$id || goal.id, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    goals.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/70 text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No goals yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"mt-2 text-white/90 hover:text-white underline\",\n                                children: \"Add your first goal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-2 border-t border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowAddModal(true),\n                    className: \"w-full flex items-center justify-center space-x-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg py-2 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"New goal\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddGoalModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onAdd: addGoal,\n                categoryName: category.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AISuggestions__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                categoryName: category.name,\n                existingGoals: goals,\n                onAddGoal: addGoal,\n                isOpen: showAISuggestions,\n                onClose: ()=>setShowAISuggestions(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Category, \"v6DLO3shCYQRP/AMsNflIU1Ns7Q=\");\n_c = Category;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\nvar _c;\n$RefreshReg$(_c, \"Category\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NhdGVnb3J5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1k7QUFDVjtBQUNaO0FBQ1E7QUFDRTtBQUNDO0FBRTdDLE1BQU1VLFdBQVc7UUFBQyxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRTs7SUFDcEMsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdkLCtDQUFRQSxDQUFDLEVBQUU7SUFDckMsTUFBTSxDQUFDZSxjQUFjQyxnQkFBZ0IsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2lCLG1CQUFtQkMscUJBQXFCLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNtQixTQUFTQyxXQUFXLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUV2QywrQ0FBK0M7SUFDL0MsTUFBTXFCLGNBQWM7UUFDbEJDLFVBQVU7WUFDUjtnQkFBRUMsSUFBSTtnQkFBS0MsT0FBTztnQkFBYUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQ2pFO2dCQUFFSCxJQUFJO2dCQUFLQyxPQUFPO2dCQUFVQyxRQUFRO2dCQUFZQyxNQUFNO1lBQWM7WUFDcEU7Z0JBQUVILElBQUk7Z0JBQUtDLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQVlDLE1BQU07WUFBSztZQUM3RDtnQkFBRUgsSUFBSTtnQkFBS0MsT0FBTztnQkFBUUMsUUFBUTtnQkFBWUMsTUFBTTtZQUFLO1lBQ3pEO2dCQUFFSCxJQUFJO2dCQUFLQyxPQUFPO2dCQUFXQyxRQUFRO2dCQUFZQyxNQUFNO1lBQUs7U0FDN0Q7UUFDREMsT0FBTztZQUNMO2dCQUFFSixJQUFJO2dCQUFLQyxPQUFPO2dCQUFVQyxRQUFRO2dCQUFRQyxNQUFNO1lBQUs7WUFDdkQ7Z0JBQUVILElBQUk7Z0JBQUtDLE9BQU87Z0JBQVVDLFFBQVE7Z0JBQVFDLE1BQU07WUFBSztZQUN2RDtnQkFBRUgsSUFBSTtnQkFBS0MsT0FBTztnQkFBWUMsUUFBUTtnQkFBUUMsTUFBTTtZQUFLO1lBQ3pEO2dCQUFFSCxJQUFJO2dCQUFLQyxPQUFPO2dCQUFhQyxRQUFRO2dCQUFRQyxNQUFNO1lBQUs7WUFDMUQ7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQVdDLFFBQVE7Z0JBQVFDLE1BQU07WUFBSztTQUMxRDtRQUNERSxNQUFNO1lBQ0o7Z0JBQUVMLElBQUk7Z0JBQU1DLE9BQU87Z0JBQVdDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSztZQUNoRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBZUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQ3BFO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFxQkMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQzFFO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFTQyxRQUFRO2dCQUFZQyxNQUFNO1lBQUs7WUFDM0Q7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQVlDLE1BQU07WUFBSTtTQUM5RDtRQUNERyxTQUFTO1lBQ1A7Z0JBQUVOLElBQUk7Z0JBQU1DLE9BQU87Z0JBQWFDLFFBQVE7Z0JBQWVDLE1BQU07WUFBTTtZQUNuRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBbUJDLFFBQVE7Z0JBQVlDLE1BQU07WUFBSztZQUNyRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBVUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQy9EO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFXQyxRQUFRO2dCQUFlQyxNQUFNO1lBQUs7WUFDaEU7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQWVDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSztTQUNyRTtRQUNESSxTQUFTO1lBQ1A7Z0JBQUVQLElBQUk7Z0JBQU1DLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSztZQUNqRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBV0MsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1lBQ2hFO2dCQUFFSCxJQUFJO2dCQUFNQyxPQUFPO2dCQUFZQyxRQUFRO2dCQUFZQyxNQUFNO1lBQU07WUFDL0Q7Z0JBQUVILElBQUk7Z0JBQU1DLE9BQU87Z0JBQVlDLFFBQVE7Z0JBQWVDLE1BQU07WUFBSTtZQUNoRTtnQkFBRUgsSUFBSTtnQkFBTUMsT0FBTztnQkFBVUMsUUFBUTtnQkFBZUMsTUFBTTtZQUFLO1NBQ2hFO0lBQ0g7SUFFQXpCLGdEQUFTQTs4QkFBQztZQUNSOEI7UUFDRjs2QkFBRztRQUFDcEIsU0FBU1ksRUFBRTtLQUFDO0lBSWhCLE1BQU1RLFlBQVk7UUFDaEIsSUFBSTtZQUNGWCxXQUFXO1lBRVgsa0NBQWtDO1lBQ2xDLElBQUk7Z0JBQ0YsTUFBTVksU0FBUyxNQUFNdkIsc0RBQVdBLENBQUN3QixJQUFJLENBQUNyQixRQUFRRCxTQUFTdUIsR0FBRyxJQUFJdkIsU0FBU1ksRUFBRTtnQkFDekUsSUFBSVMsT0FBT0csU0FBUyxDQUFDQyxNQUFNLEdBQUcsR0FBRztvQkFDL0J0QixTQUFTa0IsT0FBT0csU0FBUztnQkFDM0IsT0FBTztvQkFDTCwyREFBMkQ7b0JBQzNELE1BQU1FO2dCQUNSO1lBQ0YsRUFBRSxPQUFPQyxTQUFTO2dCQUNoQkMsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ0Y7Z0JBQ3RELE1BQU1HLGdCQUFnQnBCLFdBQVcsQ0FBQ1YsU0FBU1ksRUFBRSxDQUFDLElBQUksRUFBRTtnQkFDcERULFNBQVMyQjtZQUNYO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3hDLFNBQVU7WUFDUnRCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWlCLG9CQUFvQjtRQUN4QixJQUFJO2dCQUM0RDFCO1lBQTlELE1BQU04QixnQkFBZ0JwQixXQUFXLENBQUNWLFNBQVNZLEVBQUUsQ0FBQyxJQUFJRixXQUFXLEVBQUNWLGlCQUFBQSxTQUFTZ0MsSUFBSSxjQUFiaEMscUNBQUFBLGVBQWVpQyxXQUFXLEdBQUcsSUFBSSxFQUFFO1lBQ2pHLE1BQU1DLGVBQWUsRUFBRTtZQUV2QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUwsY0FBY0wsTUFBTSxFQUFFVSxJQUFLO2dCQUM3QyxNQUFNQyxPQUFPTixhQUFhLENBQUNLLEVBQUU7Z0JBQzdCLElBQUk7b0JBQ0YsTUFBTUUsVUFBVSxNQUFNdkMsc0RBQVdBLENBQUN3QyxNQUFNLENBQUNyQyxRQUFRO3dCQUMvQ1ksT0FBT3VCLEtBQUt2QixLQUFLO3dCQUNqQkUsTUFBTXFCLEtBQUtyQixJQUFJO3dCQUNmRCxRQUFRc0IsS0FBS3RCLE1BQU07d0JBQ25CeUIsWUFBWXZDLFNBQVN1QixHQUFHLElBQUl2QixTQUFTWSxFQUFFO3dCQUN2QzRCLE9BQU9MO29CQUNUO29CQUNBRCxhQUFhTyxJQUFJLENBQUNKO2dCQUNwQixFQUFFLE9BQU9OLE9BQU87b0JBQ2RILFFBQVFHLEtBQUssQ0FBQywrQkFBK0JBO29CQUM3Qyw4Q0FBOEM7b0JBQzlDRyxhQUFhTyxJQUFJLENBQUNMO2dCQUNwQjtZQUNGO1lBQ0FqQyxTQUFTK0I7UUFDWCxFQUFFLE9BQU9ILE9BQU87WUFDZEgsUUFBUUcsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsTUFBTUQsZ0JBQWdCcEIsV0FBVyxDQUFDVixTQUFTWSxFQUFFLENBQUMsSUFBSSxFQUFFO1lBQ3BEVCxTQUFTMkI7UUFDWDtJQUNGO0lBRUEsTUFBTVksVUFBVSxPQUFPQztRQUNyQixJQUFJO1lBQ0YsTUFBTU4sVUFBVSxNQUFNdkMsc0RBQVdBLENBQUN3QyxNQUFNLENBQUNyQyxRQUFRO2dCQUMvQyxHQUFHMEMsT0FBTztnQkFDVjdCLFFBQVE7Z0JBQ1J5QixZQUFZdkMsU0FBU3VCLEdBQUcsSUFBSXZCLFNBQVNZLEVBQUU7Z0JBQ3ZDNEIsT0FBT3RDLE1BQU11QixNQUFNO1lBQ3JCO1lBQ0F0QixTQUFTO21CQUFJRDtnQkFBT21DO2FBQVE7UUFDOUIsRUFBRSxPQUFPTixPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQyxzQkFBc0JBO1lBQ3BDLDBCQUEwQjtZQUMxQixNQUFNSyxPQUFPO2dCQUNYeEIsSUFBSWdDLEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtnQkFDdkIsR0FBR0gsT0FBTztnQkFDVjdCLFFBQVE7WUFDVjtZQUNBWCxTQUFTO21CQUFJRDtnQkFBT2tDO2FBQUs7UUFDM0I7SUFDRjtJQUVBLE1BQU1XLGFBQWEsT0FBT0MsUUFBUUM7UUFDaEMsSUFBSTtZQUNGLE1BQU1uRCxzREFBV0EsQ0FBQ29ELE1BQU0sQ0FBQ0YsUUFBUUM7WUFDakM5QyxTQUFTRCxNQUFNaUQsR0FBRyxDQUFDZixDQUFBQSxPQUNqQixDQUFDQSxLQUFLYixHQUFHLElBQUlhLEtBQUt4QixFQUFFLE1BQU1vQyxTQUFTO29CQUFFLEdBQUdaLElBQUk7b0JBQUUsR0FBR2EsT0FBTztnQkFBQyxJQUFJYjtRQUVqRSxFQUFFLE9BQU9MLE9BQU87WUFDZEgsUUFBUUcsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsaUNBQWlDO1lBQ2pDNUIsU0FBU0QsTUFBTWlELEdBQUcsQ0FBQ2YsQ0FBQUEsT0FDakIsQ0FBQ0EsS0FBS2IsR0FBRyxJQUFJYSxLQUFLeEIsRUFBRSxNQUFNb0MsU0FBUztvQkFBRSxHQUFHWixJQUFJO29CQUFFLEdBQUdhLE9BQU87Z0JBQUMsSUFBSWI7UUFFakU7SUFDRjtJQUVBLE1BQU1nQixhQUFhLE9BQU9KO1FBQ3hCLElBQUk7WUFDRixNQUFNbEQsc0RBQVdBLENBQUN1RCxNQUFNLENBQUNMO1lBQ3pCN0MsU0FBU0QsTUFBTW9ELE1BQU0sQ0FBQ2xCLENBQUFBLE9BQVEsQ0FBQ0EsS0FBS2IsR0FBRyxJQUFJYSxLQUFLeEIsRUFBRSxNQUFNb0M7UUFDMUQsRUFBRSxPQUFPakIsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsd0JBQXdCQTtZQUN0QyxpQ0FBaUM7WUFDakM1QixTQUFTRCxNQUFNb0QsTUFBTSxDQUFDbEIsQ0FBQUEsT0FBUSxDQUFDQSxLQUFLYixHQUFHLElBQUlhLEtBQUt4QixFQUFFLE1BQU1vQztRQUMxRDtJQUNGO0lBRUEscUJBQ0UsOERBQUNPO1FBQUlDLFdBQVcsR0FBa0IsT0FBZnhELFNBQVN5RCxLQUFLLEVBQUM7OzBCQUNoQyw4REFBQ0Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FBZ0N4RCxTQUFTZ0MsSUFBSTs7Ozs7O2tDQUMzRCw4REFBQ3VCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQ0NDLFNBQVMsSUFBTXJELHFCQUFxQjtnQ0FDcENpRCxXQUFVO2dDQUNWM0MsT0FBTTswQ0FFTiw0RUFBQ25CLHlGQUFRQTtvQ0FBQ21FLE1BQU07Ozs7Ozs7Ozs7OzBDQUVsQiw4REFBQ0Y7Z0NBQ0NDLFNBQVMsSUFBTXZELGdCQUFnQjtnQ0FDL0JtRCxXQUFVO2dDQUNWM0MsT0FBTTswQ0FFTiw0RUFBQ3BCLHlGQUFJQTtvQ0FBQ29FLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtsQiw4REFBQ047Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDaEUsMERBQWVBO2tDQUNiVSxNQUFNaUQsR0FBRyxDQUFDLENBQUNmLE1BQU0wQixzQkFDaEIsOERBQUN2RSxpREFBTUEsQ0FBQ2dFLEdBQUc7Z0NBRVRRLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUc7Z0NBQzdCQyxTQUFTO29DQUFFRixTQUFTO29DQUFHQyxHQUFHO2dDQUFFO2dDQUM1QkUsTUFBTTtvQ0FBRUgsU0FBUztvQ0FBR0MsR0FBRyxDQUFDO2dDQUFHO2dDQUMzQkcsWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBS0MsT0FBT1IsUUFBUTtnQ0FBSzswQ0FFakQsNEVBQUNuRSxpREFBUUE7b0NBQ1B5QyxNQUFNQTtvQ0FDTm1DLFVBQVV4QjtvQ0FDVnlCLFVBQVVwQjs7Ozs7OytCQVRQaEIsS0FBS2IsR0FBRyxJQUFJYSxLQUFLeEIsRUFBRTs7Ozs7Ozs7OztvQkFlN0JWLE1BQU11QixNQUFNLEtBQUssS0FBSyxDQUFDakIseUJBQ3RCLDhEQUFDK0M7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDaUI7MENBQUU7Ozs7OzswQ0FDSCw4REFBQ2Q7Z0NBQ0NDLFNBQVMsSUFBTXZELGdCQUFnQjtnQ0FDL0JtRCxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVAsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRztvQkFDQ0MsU0FBUyxJQUFNdkQsZ0JBQWdCO29CQUMvQm1ELFdBQVU7O3NDQUVWLDhEQUFDL0QseUZBQUlBOzRCQUFDb0UsTUFBTTs7Ozs7O3NDQUNaLDhEQUFDYTs0QkFBS2xCLFdBQVU7c0NBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUk5Qiw4REFBQzVELHFEQUFZQTtnQkFDWCtFLFFBQVF2RTtnQkFDUndFLFNBQVMsSUFBTXZFLGdCQUFnQjtnQkFDL0J3RSxPQUFPbkM7Z0JBQ1BvQyxjQUFjOUUsU0FBU2dDLElBQUk7Ozs7OzswQkFHN0IsOERBQUNuQyxzREFBYUE7Z0JBQ1ppRixjQUFjOUUsU0FBU2dDLElBQUk7Z0JBQzNCK0MsZUFBZTdFO2dCQUNmOEUsV0FBV3RDO2dCQUNYaUMsUUFBUXJFO2dCQUNSc0UsU0FBUyxJQUFNckUscUJBQXFCOzs7Ozs7Ozs7Ozs7QUFJNUM7R0ExT01SO0tBQUFBO0FBNE9OLGlFQUFlQSxRQUFRQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc2hhd2F6L0RldmVsb3Blci9zeGUvc3hlX2NsaWVudC9zcmMvY29tcG9uZW50cy9DYXRlZ29yeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuaW1wb3J0IHsgUGx1cywgU3BhcmtsZXMgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgR29hbENhcmQgZnJvbSBcIi4vR29hbENhcmRcIjtcbmltcG9ydCBBZGRHb2FsTW9kYWwgZnJvbSBcIi4vQWRkR29hbE1vZGFsXCI7XG5pbXBvcnQgQUlTdWdnZXN0aW9ucyBmcm9tIFwiLi9BSVN1Z2dlc3Rpb25zXCI7XG5pbXBvcnQgeyBnb2FsU2VydmljZSB9IGZyb20gXCJAL2xpYi9kYXRhYmFzZVwiO1xuXG5jb25zdCBDYXRlZ29yeSA9ICh7IGNhdGVnb3J5LCB1c2VySWQgfSkgPT4ge1xuICBjb25zdCBbZ29hbHMsIHNldEdvYWxzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3Nob3dBZGRNb2RhbCwgc2V0U2hvd0FkZE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dBSVN1Z2dlc3Rpb25zLCBzZXRTaG93QUlTdWdnZXN0aW9uc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vIFNhbXBsZSBnb2FscyBiYXNlZCBvbiB0aGUgdmlzaW9uIGJvYXJkIGltYWdlXG4gIGNvbnN0IHNhbXBsZUdvYWxzID0ge1xuICAgIHBlcnNvbmFsOiBbXG4gICAgICB7IGlkOiBcIjFcIiwgdGl0bGU6IFwiRG9jdW1lbnRzXCIsIHN0YXR1czogXCJpbi1wcm9ncmVzc1wiLCBpY29uOiBcIvCfk4RcIiB9LFxuICAgICAgeyBpZDogXCIyXCIsIHRpdGxlOiBcIkZhbWlseVwiLCBzdGF0dXM6IFwibm90LWdvb2RcIiwgaWNvbjogXCLwn5Go4oCN8J+RqeKAjfCfkafigI3wn5GmXCIgfSxcbiAgICAgIHsgaWQ6IFwiM1wiLCB0aXRsZTogXCJFeGVyY2lzZVwiLCBzdGF0dXM6IFwibm90LWdvb2RcIiwgaWNvbjogXCLwn5KqXCIgfSxcbiAgICAgIHsgaWQ6IFwiNFwiLCB0aXRsZTogXCJEaWV0XCIsIHN0YXR1czogXCJub3QtZ29vZFwiLCBpY29uOiBcIvCfpZdcIiB9LFxuICAgICAgeyBpZDogXCI1XCIsIHRpdGxlOiBcIlByYXllcnNcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi8J+Zj1wiIH0sXG4gICAgXSxcbiAgICBsZWFybjogW1xuICAgICAgeyBpZDogXCI2XCIsIHRpdGxlOiBcIkNvZGluZ1wiLCBzdGF0dXM6IFwiZG9uZVwiLCBpY29uOiBcIvCfkrtcIiB9LFxuICAgICAgeyBpZDogXCI3XCIsIHRpdGxlOiBcIkRlc2lnblwiLCBzdGF0dXM6IFwiZG9uZVwiLCBpY29uOiBcIvCfjqhcIiB9LFxuICAgICAgeyBpZDogXCI4XCIsIHRpdGxlOiBcIkJ1c2luZXNzXCIsIHN0YXR1czogXCJkb25lXCIsIGljb246IFwi8J+SvFwiIH0sXG4gICAgICB7IGlkOiBcIjlcIiwgdGl0bGU6IFwiTWFya2V0aW5nXCIsIHN0YXR1czogXCJkb25lXCIsIGljb246IFwi8J+TiFwiIH0sXG4gICAgICB7IGlkOiBcIjEwXCIsIHRpdGxlOiBcIkZpbmFuY2VcIiwgc3RhdHVzOiBcImRvbmVcIiwgaWNvbjogXCLwn5KwXCIgfSxcbiAgICBdLFxuICAgIHdvcms6IFtcbiAgICAgIHsgaWQ6IFwiMTFcIiwgdGl0bGU6IFwiVHJhZGluZ1wiLCBzdGF0dXM6IFwiaW4tcHJvZ3Jlc3NcIiwgaWNvbjogXCLwn5OKXCIgfSxcbiAgICAgIHsgaWQ6IFwiMTJcIiwgdGl0bGU6IFwiUmVhbCBFc3RhdGVcIiwgc3RhdHVzOiBcImluLXByb2dyZXNzXCIsIGljb246IFwi8J+PoFwiIH0sXG4gICAgICB7IGlkOiBcIjEzXCIsIHRpdGxlOiBcIkRpZ2l0YWwgTWFya2V0aW5nXCIsIHN0YXR1czogXCJpbi1wcm9ncmVzc1wiLCBpY29uOiBcIvCfk7FcIiB9LFxuICAgICAgeyBpZDogXCIxNFwiLCB0aXRsZTogXCJIdWJjdlwiLCBzdGF0dXM6IFwibm90LWdvb2RcIiwgaWNvbjogXCLwn5SXXCIgfSxcbiAgICAgIHsgaWQ6IFwiMTVcIiwgdGl0bGU6IFwiQ29kZWx1ZGVcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi4pqhXCIgfSxcbiAgICBdLFxuICAgIGZpbmFuY2U6IFtcbiAgICAgIHsgaWQ6IFwiMTZcIiwgdGl0bGU6IFwiTmV0IFdvcnRoXCIsIHN0YXR1czogXCJpbi1wcm9ncmVzc1wiLCBpY29uOiBcIvCfj5vvuI9cIiB9LFxuICAgICAgeyBpZDogXCIxN1wiLCB0aXRsZTogXCJMb2FucyAmIENyZWRpdHNcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi8J+Ss1wiIH0sXG4gICAgICB7IGlkOiBcIjE4XCIsIHRpdGxlOiBcIkFzc2V0c1wiLCBzdGF0dXM6IFwibm90LXN0YXJ0ZWRcIiwgaWNvbjogXCLwn5KOXCIgfSxcbiAgICAgIHsgaWQ6IFwiMTlcIiwgdGl0bGU6IFwiQ2hhcml0eVwiLCBzdGF0dXM6IFwibm90LXN0YXJ0ZWRcIiwgaWNvbjogXCLinaTvuI9cIiB9LFxuICAgICAgeyBpZDogXCIyMFwiLCB0aXRsZTogXCJJbnZlc3RtZW50c1wiLCBzdGF0dXM6IFwibm90LXN0YXJ0ZWRcIiwgaWNvbjogXCLwn5K5XCIgfSxcbiAgICBdLFxuICAgIHdlZWtlbmQ6IFtcbiAgICAgIHsgaWQ6IFwiMjFcIiwgdGl0bGU6IFwiTWVtb3JpZXNcIiwgc3RhdHVzOiBcImluLXByb2dyZXNzXCIsIGljb246IFwi8J+TuFwiIH0sXG4gICAgICB7IGlkOiBcIjIyXCIsIHRpdGxlOiBcIlRyYXZlbHNcIiwgc3RhdHVzOiBcImluLXByb2dyZXNzXCIsIGljb246IFwi4pyI77iPXCIgfSxcbiAgICAgIHsgaWQ6IFwiMjNcIiwgdGl0bGU6IFwiU2hvcHBpbmdcIiwgc3RhdHVzOiBcIm5vdC1nb29kXCIsIGljb246IFwi8J+bje+4j1wiIH0sXG4gICAgICB7IGlkOiBcIjI0XCIsIHRpdGxlOiBcIkJyYW5kaW5nXCIsIHN0YXR1czogXCJub3Qtc3RhcnRlZFwiLCBpY29uOiBcIuKtkFwiIH0sXG4gICAgICB7IGlkOiBcIjI1XCIsIHRpdGxlOiBcIkV2ZW50c1wiLCBzdGF0dXM6IFwibm90LXN0YXJ0ZWRcIiwgaWNvbjogXCLwn46JXCIgfSxcbiAgICBdLFxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZEdvYWxzKCk7XG4gIH0sIFtjYXRlZ29yeS5pZF0pO1xuXG5cblxuICBjb25zdCBsb2FkR29hbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIFRyeSB0byBsb2FkIGdvYWxzIGZyb20gZGF0YWJhc2VcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGdvYWxTZXJ2aWNlLmxpc3QodXNlcklkLCBjYXRlZ29yeS4kaWQgfHwgY2F0ZWdvcnkuaWQpO1xuICAgICAgICBpZiAocmVzdWx0LmRvY3VtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0R29hbHMocmVzdWx0LmRvY3VtZW50cyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gSWYgbm8gZ29hbHMgZXhpc3QsIGNyZWF0ZSBzYW1wbGUgZ29hbHMgZm9yIHRoaXMgY2F0ZWdvcnlcbiAgICAgICAgICBhd2FpdCBjcmVhdGVTYW1wbGVHb2FscygpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChkYkVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiRGF0YWJhc2Ugbm90IHJlYWR5LCB1c2luZyBzYW1wbGUgZGF0YTpcIiwgZGJFcnJvcik7XG4gICAgICAgIGNvbnN0IGNhdGVnb3J5R29hbHMgPSBzYW1wbGVHb2Fsc1tjYXRlZ29yeS5pZF0gfHwgW107XG4gICAgICAgIHNldEdvYWxzKGNhdGVnb3J5R29hbHMpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbG9hZGluZyBnb2FsczpcIiwgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgY3JlYXRlU2FtcGxlR29hbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhdGVnb3J5R29hbHMgPSBzYW1wbGVHb2Fsc1tjYXRlZ29yeS5pZF0gfHwgc2FtcGxlR29hbHNbY2F0ZWdvcnkubmFtZT8udG9Mb3dlckNhc2UoKV0gfHwgW107XG4gICAgICBjb25zdCBjcmVhdGVkR29hbHMgPSBbXTtcblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjYXRlZ29yeUdvYWxzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGdvYWwgPSBjYXRlZ29yeUdvYWxzW2ldO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGNyZWF0ZWQgPSBhd2FpdCBnb2FsU2VydmljZS5jcmVhdGUodXNlcklkLCB7XG4gICAgICAgICAgICB0aXRsZTogZ29hbC50aXRsZSxcbiAgICAgICAgICAgIGljb246IGdvYWwuaWNvbixcbiAgICAgICAgICAgIHN0YXR1czogZ29hbC5zdGF0dXMsXG4gICAgICAgICAgICBjYXRlZ29yeUlkOiBjYXRlZ29yeS4kaWQgfHwgY2F0ZWdvcnkuaWQsXG4gICAgICAgICAgICBvcmRlcjogaVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGNyZWF0ZWRHb2Fscy5wdXNoKGNyZWF0ZWQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBjcmVhdGluZyBzYW1wbGUgZ29hbDpcIiwgZXJyb3IpO1xuICAgICAgICAgIC8vIElmIGRhdGFiYXNlIGNyZWF0aW9uIGZhaWxzLCB1c2Ugc2FtcGxlIGRhdGFcbiAgICAgICAgICBjcmVhdGVkR29hbHMucHVzaChnb2FsKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgc2V0R29hbHMoY3JlYXRlZEdvYWxzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIHNhbXBsZSBnb2FsczpcIiwgZXJyb3IpO1xuICAgICAgY29uc3QgY2F0ZWdvcnlHb2FscyA9IHNhbXBsZUdvYWxzW2NhdGVnb3J5LmlkXSB8fCBbXTtcbiAgICAgIHNldEdvYWxzKGNhdGVnb3J5R29hbHMpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBhZGRHb2FsID0gYXN5bmMgKG5ld0dvYWwpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY3JlYXRlZCA9IGF3YWl0IGdvYWxTZXJ2aWNlLmNyZWF0ZSh1c2VySWQsIHtcbiAgICAgICAgLi4ubmV3R29hbCxcbiAgICAgICAgc3RhdHVzOiBcIm5vdC1zdGFydGVkXCIsXG4gICAgICAgIGNhdGVnb3J5SWQ6IGNhdGVnb3J5LiRpZCB8fCBjYXRlZ29yeS5pZCxcbiAgICAgICAgb3JkZXI6IGdvYWxzLmxlbmd0aFxuICAgICAgfSk7XG4gICAgICBzZXRHb2FscyhbLi4uZ29hbHMsIGNyZWF0ZWRdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFkZGluZyBnb2FsOlwiLCBlcnJvcik7XG4gICAgICAvLyBGYWxsYmFjayB0byBsb2NhbCBzdGF0ZVxuICAgICAgY29uc3QgZ29hbCA9IHtcbiAgICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgICAgLi4ubmV3R29hbCxcbiAgICAgICAgc3RhdHVzOiBcIm5vdC1zdGFydGVkXCIsXG4gICAgICB9O1xuICAgICAgc2V0R29hbHMoWy4uLmdvYWxzLCBnb2FsXSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUdvYWwgPSBhc3luYyAoZ29hbElkLCB1cGRhdGVzKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGdvYWxTZXJ2aWNlLnVwZGF0ZShnb2FsSWQsIHVwZGF0ZXMpO1xuICAgICAgc2V0R29hbHMoZ29hbHMubWFwKGdvYWwgPT5cbiAgICAgICAgKGdvYWwuJGlkIHx8IGdvYWwuaWQpID09PSBnb2FsSWQgPyB7IC4uLmdvYWwsIC4uLnVwZGF0ZXMgfSA6IGdvYWxcbiAgICAgICkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdXBkYXRpbmcgZ29hbDpcIiwgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gbG9jYWwgc3RhdGUgdXBkYXRlXG4gICAgICBzZXRHb2Fscyhnb2Fscy5tYXAoZ29hbCA9PlxuICAgICAgICAoZ29hbC4kaWQgfHwgZ29hbC5pZCkgPT09IGdvYWxJZCA/IHsgLi4uZ29hbCwgLi4udXBkYXRlcyB9IDogZ29hbFxuICAgICAgKSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGRlbGV0ZUdvYWwgPSBhc3luYyAoZ29hbElkKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGdvYWxTZXJ2aWNlLmRlbGV0ZShnb2FsSWQpO1xuICAgICAgc2V0R29hbHMoZ29hbHMuZmlsdGVyKGdvYWwgPT4gKGdvYWwuJGlkIHx8IGdvYWwuaWQpICE9PSBnb2FsSWQpKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRlbGV0aW5nIGdvYWw6XCIsIGVycm9yKTtcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsIHN0YXRlIHVwZGF0ZVxuICAgICAgc2V0R29hbHMoZ29hbHMuZmlsdGVyKGdvYWwgPT4gKGdvYWwuJGlkIHx8IGdvYWwuaWQpICE9PSBnb2FsSWQpKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YCR7Y2F0ZWdvcnkuY29sb3J9IHJvdW5kZWQtbGcgcC00IG1pbi1oLVs0MDBweF0gc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0zMDBgfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj57Y2F0ZWdvcnkubmFtZX08L2gyPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBSVN1Z2dlc3Rpb25zKHRydWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgcC0xIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIHRpdGxlPVwiQUkgU3VnZ2VzdGlvbnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTcGFya2xlcyBzaXplPXsxOH0gLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzIwIHJvdW5kZWQtZnVsbCBwLTEgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgdGl0bGU9XCJBZGQgR29hbFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFBsdXMgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgICAge2dvYWxzLm1hcCgoZ29hbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT17Z29hbC4kaWQgfHwgZ29hbC5pZH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zLCBkZWxheTogaW5kZXggKiAwLjA1IH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxHb2FsQ2FyZFxuICAgICAgICAgICAgICAgIGdvYWw9e2dvYWx9XG4gICAgICAgICAgICAgICAgb25VcGRhdGU9e3VwZGF0ZUdvYWx9XG4gICAgICAgICAgICAgICAgb25EZWxldGU9e2RlbGV0ZUdvYWx9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuXG4gICAgICAgIHtnb2Fscy5sZW5ndGggPT09IDAgJiYgIWxvYWRpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS83MCB0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICA8cD5ObyBnb2FscyB5ZXQ8L3A+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBZGRNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXdoaXRlLzkwIGhvdmVyOnRleHQtd2hpdGUgdW5kZXJsaW5lXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQWRkIHlvdXIgZmlyc3QgZ29hbFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEFkZCBOZXcgR29hbCBCdXR0b24gYXQgYm90dG9tICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHB0LTIgYm9yZGVyLXQgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXdoaXRlLzcwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvMTAgcm91bmRlZC1sZyBweS0yIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxQbHVzIHNpemU9ezE2fSAvPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5OZXcgZ29hbDwvc3Bhbj5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPEFkZEdvYWxNb2RhbFxuICAgICAgICBpc09wZW49e3Nob3dBZGRNb2RhbH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0FkZE1vZGFsKGZhbHNlKX1cbiAgICAgICAgb25BZGQ9e2FkZEdvYWx9XG4gICAgICAgIGNhdGVnb3J5TmFtZT17Y2F0ZWdvcnkubmFtZX1cbiAgICAgIC8+XG5cbiAgICAgIDxBSVN1Z2dlc3Rpb25zXG4gICAgICAgIGNhdGVnb3J5TmFtZT17Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgZXhpc3RpbmdHb2Fscz17Z29hbHN9XG4gICAgICAgIG9uQWRkR29hbD17YWRkR29hbH1cbiAgICAgICAgaXNPcGVuPXtzaG93QUlTdWdnZXN0aW9uc31cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0FJU3VnZ2VzdGlvbnMoZmFsc2UpfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENhdGVnb3J5O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiUGx1cyIsIlNwYXJrbGVzIiwiR29hbENhcmQiLCJBZGRHb2FsTW9kYWwiLCJBSVN1Z2dlc3Rpb25zIiwiZ29hbFNlcnZpY2UiLCJDYXRlZ29yeSIsImNhdGVnb3J5IiwidXNlcklkIiwiZ29hbHMiLCJzZXRHb2FscyIsInNob3dBZGRNb2RhbCIsInNldFNob3dBZGRNb2RhbCIsInNob3dBSVN1Z2dlc3Rpb25zIiwic2V0U2hvd0FJU3VnZ2VzdGlvbnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhbXBsZUdvYWxzIiwicGVyc29uYWwiLCJpZCIsInRpdGxlIiwic3RhdHVzIiwiaWNvbiIsImxlYXJuIiwid29yayIsImZpbmFuY2UiLCJ3ZWVrZW5kIiwibG9hZEdvYWxzIiwicmVzdWx0IiwibGlzdCIsIiRpZCIsImRvY3VtZW50cyIsImxlbmd0aCIsImNyZWF0ZVNhbXBsZUdvYWxzIiwiZGJFcnJvciIsImNvbnNvbGUiLCJsb2ciLCJjYXRlZ29yeUdvYWxzIiwiZXJyb3IiLCJuYW1lIiwidG9Mb3dlckNhc2UiLCJjcmVhdGVkR29hbHMiLCJpIiwiZ29hbCIsImNyZWF0ZWQiLCJjcmVhdGUiLCJjYXRlZ29yeUlkIiwib3JkZXIiLCJwdXNoIiwiYWRkR29hbCIsIm5ld0dvYWwiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJ1cGRhdGVHb2FsIiwiZ29hbElkIiwidXBkYXRlcyIsInVwZGF0ZSIsIm1hcCIsImRlbGV0ZUdvYWwiLCJkZWxldGUiLCJmaWx0ZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJjb2xvciIsImgyIiwiYnV0dG9uIiwib25DbGljayIsInNpemUiLCJpbmRleCIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJleGl0IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZGVsYXkiLCJvblVwZGF0ZSIsIm9uRGVsZXRlIiwicCIsInNwYW4iLCJpc09wZW4iLCJvbkNsb3NlIiwib25BZGQiLCJjYXRlZ29yeU5hbWUiLCJleGlzdGluZ0dvYWxzIiwib25BZGRHb2FsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Category.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/GoalCard.js":
/*!************************************!*\
  !*** ./src/components/GoalCard.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Edit2_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Edit2,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Edit2_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Edit2,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit2_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit2,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst GoalCard = (param)=>{\n    let { goal, onUpdate, onDelete, isDragging = false } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTitle, setEditTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(goal.title);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"done\":\n                return \"bg-green-500\";\n            case \"in-progress\":\n                return \"bg-blue-500\";\n            case \"not-good\":\n                return \"bg-red-500\";\n            case \"not-started\":\n                return \"bg-gray-500\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"done\":\n                return \"Done\";\n            case \"in-progress\":\n                return \"In progress\";\n            case \"not-good\":\n                return \"Not Good\";\n            case \"not-started\":\n                return \"Not started\";\n            default:\n                return \"Not started\";\n        }\n    };\n    const statusOptions = [\n        {\n            value: \"not-started\",\n            label: \"Not started\",\n            color: \"bg-gray-500\"\n        },\n        {\n            value: \"in-progress\",\n            label: \"In progress\",\n            color: \"bg-blue-500\"\n        },\n        {\n            value: \"done\",\n            label: \"Done\",\n            color: \"bg-green-500\"\n        },\n        {\n            value: \"not-good\",\n            label: \"Not Good\",\n            color: \"bg-red-500\"\n        }\n    ];\n    const handleStatusChange = (newStatus)=>{\n        onUpdate(goal.$id || goal.id, {\n            status: newStatus\n        });\n        setShowMenu(false);\n    };\n    const handleSaveEdit = ()=>{\n        if (editTitle.trim()) {\n            onUpdate(goal.$id || goal.id, {\n                title: editTitle.trim()\n            });\n            setIsEditing(false);\n        }\n    };\n    const handleCancelEdit = ()=>{\n        setEditTitle(goal.title);\n        setIsEditing(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 relative group cursor-grab active:cursor-grabbing \".concat(isDragging ? 'shadow-lg ring-2 ring-blue-400' : ''),\n        whileHover: !isDragging ? {\n            scale: 1.02\n        } : {},\n        whileTap: !isDragging ? {\n            scale: 0.98\n        } : {},\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: goal.icon\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined),\n                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: editTitle,\n                                    onChange: (e)=>setEditTitle(e.target.value),\n                                    className: \"w-full px-2 py-1 border rounded text-sm\",\n                                    onKeyPress: (e)=>{\n                                        if (e.key === \"Enter\") handleSaveEdit();\n                                        if (e.key === \"Escape\") handleCancelEdit();\n                                    },\n                                    onBlur: handleSaveEdit,\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-800 text-sm\",\n                                        children: goal.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: getStatusText(goal.status)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full \".concat(getStatusColor(goal.status), \" cursor-pointer\"),\n                                        onClick: ()=>setShowMenu(!showMenu),\n                                        title: getStatusText(goal.status)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        className: \"absolute right-0 top-6 bg-white rounded-lg shadow-lg border z-10 py-1 min-w-[120px]\",\n                                        children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleStatusChange(option.value),\n                                                className: \"w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center space-x-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(option.color)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, option.value, true, {\n                                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowMenu(false),\n                                    className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit2_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end space-x-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsEditing(true),\n                        className: \"text-gray-400 hover:text-blue-600 p-1 rounded hover:bg-blue-50 transition-colors\",\n                        title: \"Edit\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit2_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            size: 12\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onDelete(goal.$id || goal.id),\n                        className: \"text-gray-400 hover:text-red-600 p-1 rounded hover:bg-red-50 transition-colors\",\n                        title: \"Delete\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit2_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 12\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/GoalCard.js\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GoalCard, \"jC2ix/OV5j7O1azdhqaabXJZ1DI=\");\n_c = GoalCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GoalCard);\nvar _c;\n$RefreshReg$(_c, \"GoalCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GoalCard.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _ProgressOverview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressOverview */ \"(app-pages-browser)/./src/components/ProgressOverview.js\");\n/* harmony import */ var _AIInsights__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AIInsights */ \"(app-pages-browser)/./src/components/AIInsights.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGoals, setAllGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    // Real-time update handlers\n    const handleCategoryUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VisionBoard.useCallback[handleCategoryUpdate]\": (payload, events)=>{\n            const isCreate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isCreate\": (e)=>e.includes('.create')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isCreate\"]);\n            const isUpdate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\": (e)=>e.includes('.update')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\"]);\n            const isDelete = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isDelete\": (e)=>e.includes('.delete')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isDelete\"]);\n            if (isCreate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>[\n                            ...prev,\n                            payload\n                        ]\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isUpdate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.map({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id === payload.$id ? payload : cat\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isDelete) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.filter({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id !== payload.$id\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            }\n        }\n    }[\"VisionBoard.useCallback[handleCategoryUpdate]\"], []);\n    // Set up real-time subscriptions\n    (0,_hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(userId, handleCategoryUpdate, null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n                loadAllGoals();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadAllGoals = async ()=>{\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.list(userId);\n            setAllGoals(result.documents);\n        } catch (error) {\n            console.error(\"Error loading all goals:\", error);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressOverview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userId: userId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIInsights__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    goals: allGoals,\n                    userId: userId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.$id || category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"sPZE3qNsc/NOweeUWYagq2a0U0c=\", false, function() {\n    return [\n        _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ })

});