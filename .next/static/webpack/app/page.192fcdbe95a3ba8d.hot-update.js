"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"88852b29700a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg4ODUyYjI5NzAwYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Category.js":
/*!************************************!*\
  !*** ./src/components/Category.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _AddGoalModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddGoalModal */ \"(app-pages-browser)/./src/components/AddGoalModal.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Category = (param)=>{\n    let { category, userId } = param;\n    _s();\n    const [goals, setGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Sample goals based on the vision board image\n    const sampleGoals = {\n        personal: [\n            {\n                id: \"1\",\n                title: \"Documents\",\n                status: \"in-progress\",\n                icon: \"📄\"\n            },\n            {\n                id: \"2\",\n                title: \"Family\",\n                status: \"not-good\",\n                icon: \"👨‍👩‍👧‍👦\"\n            },\n            {\n                id: \"3\",\n                title: \"Exercise\",\n                status: \"not-good\",\n                icon: \"💪\"\n            },\n            {\n                id: \"4\",\n                title: \"Diet\",\n                status: \"not-good\",\n                icon: \"🥗\"\n            },\n            {\n                id: \"5\",\n                title: \"Prayers\",\n                status: \"not-good\",\n                icon: \"🙏\"\n            }\n        ],\n        learn: [\n            {\n                id: \"6\",\n                title: \"Coding\",\n                status: \"done\",\n                icon: \"💻\"\n            },\n            {\n                id: \"7\",\n                title: \"Design\",\n                status: \"done\",\n                icon: \"🎨\"\n            },\n            {\n                id: \"8\",\n                title: \"Business\",\n                status: \"done\",\n                icon: \"💼\"\n            },\n            {\n                id: \"9\",\n                title: \"Marketing\",\n                status: \"done\",\n                icon: \"📈\"\n            },\n            {\n                id: \"10\",\n                title: \"Finance\",\n                status: \"done\",\n                icon: \"💰\"\n            }\n        ],\n        work: [\n            {\n                id: \"11\",\n                title: \"Trading\",\n                status: \"in-progress\",\n                icon: \"📊\"\n            },\n            {\n                id: \"12\",\n                title: \"Real Estate\",\n                status: \"in-progress\",\n                icon: \"🏠\"\n            },\n            {\n                id: \"13\",\n                title: \"Digital Marketing\",\n                status: \"in-progress\",\n                icon: \"📱\"\n            },\n            {\n                id: \"14\",\n                title: \"Hubcv\",\n                status: \"not-good\",\n                icon: \"🔗\"\n            },\n            {\n                id: \"15\",\n                title: \"Codelude\",\n                status: \"not-good\",\n                icon: \"⚡\"\n            }\n        ],\n        finance: [\n            {\n                id: \"16\",\n                title: \"Net Worth\",\n                status: \"in-progress\",\n                icon: \"🏛️\"\n            },\n            {\n                id: \"17\",\n                title: \"Loans & Credits\",\n                status: \"not-good\",\n                icon: \"💳\"\n            },\n            {\n                id: \"18\",\n                title: \"Assets\",\n                status: \"not-started\",\n                icon: \"💎\"\n            },\n            {\n                id: \"19\",\n                title: \"Charity\",\n                status: \"not-started\",\n                icon: \"❤️\"\n            },\n            {\n                id: \"20\",\n                title: \"Investments\",\n                status: \"not-started\",\n                icon: \"💹\"\n            }\n        ],\n        weekend: [\n            {\n                id: \"21\",\n                title: \"Memories\",\n                status: \"in-progress\",\n                icon: \"📸\"\n            },\n            {\n                id: \"22\",\n                title: \"Travels\",\n                status: \"in-progress\",\n                icon: \"✈️\"\n            },\n            {\n                id: \"23\",\n                title: \"Shopping\",\n                status: \"not-good\",\n                icon: \"🛍️\"\n            },\n            {\n                id: \"24\",\n                title: \"Branding\",\n                status: \"not-started\",\n                icon: \"⭐\"\n            },\n            {\n                id: \"25\",\n                title: \"Events\",\n                status: \"not-started\",\n                icon: \"🎉\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Category.useEffect\": ()=>{\n            loadGoals();\n        }\n    }[\"Category.useEffect\"], [\n        category.id\n    ]);\n    const loadGoals = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load goals from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.list(userId, category.$id || category.id);\n                if (result.documents.length > 0) {\n                    setGoals(result.documents);\n                } else {\n                    // If no goals exist, create sample goals for this category\n                    await createSampleGoals();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using sample data:\", dbError);\n                const categoryGoals = sampleGoals[category.id] || [];\n                setGoals(categoryGoals);\n            }\n        } catch (error) {\n            console.error(\"Error loading goals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createSampleGoals = async ()=>{\n        try {\n            var _category_name;\n            const categoryGoals = sampleGoals[category.id] || sampleGoals[(_category_name = category.name) === null || _category_name === void 0 ? void 0 : _category_name.toLowerCase()] || [];\n            const createdGoals = [];\n            for(let i = 0; i < categoryGoals.length; i++){\n                const goal = categoryGoals[i];\n                try {\n                    const created = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.create(userId, {\n                        title: goal.title,\n                        icon: goal.icon,\n                        status: goal.status,\n                        categoryId: category.$id || category.id,\n                        order: i\n                    });\n                    createdGoals.push(created);\n                } catch (error) {\n                    console.error(\"Error creating sample goal:\", error);\n                    // If database creation fails, use sample data\n                    createdGoals.push(goal);\n                }\n            }\n            setGoals(createdGoals);\n        } catch (error) {\n            console.error(\"Error creating sample goals:\", error);\n            const categoryGoals = sampleGoals[category.id] || [];\n            setGoals(categoryGoals);\n        }\n    };\n    const addGoal = async (newGoal)=>{\n        try {\n            const created = await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.create(userId, {\n                ...newGoal,\n                status: \"not-started\",\n                categoryId: category.$id || category.id,\n                order: goals.length\n            });\n            setGoals([\n                ...goals,\n                created\n            ]);\n        } catch (error) {\n            console.error(\"Error adding goal:\", error);\n            // Fallback to local state\n            const goal = {\n                id: Date.now().toString(),\n                ...newGoal,\n                status: \"not-started\"\n            };\n            setGoals([\n                ...goals,\n                goal\n            ]);\n        }\n    };\n    const updateGoal = async (goalId, updates)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.update(goalId, updates);\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        } catch (error) {\n            console.error(\"Error updating goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        }\n    };\n    const deleteGoal = async (goalId)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_4__.goalService.delete(goalId);\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        } catch (error) {\n            console.error(\"Error deleting goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(category.color, \" rounded-lg p-4 min-h-[400px] shadow-lg hover:shadow-xl transition-shadow duration-300\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddModal(true),\n                        className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoalCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    goal: goal,\n                                    onUpdate: updateGoal,\n                                    onDelete: deleteGoal\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, goal.id, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    goals.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/70 text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No goals yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"mt-2 text-white/90 hover:text-white underline\",\n                                children: \"Add your first goal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-2 border-t border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddModal(true),\n                            className: \"w-full flex items-center justify-center space-x-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg py-2 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"New goal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddGoalModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onAdd: addGoal,\n                categoryName: category.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Category, \"UvPV4U8DXw8RWQ0vermlCsxiApQ=\");\n_c = Category;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\nvar _c;\n$RefreshReg$(_c, \"Category\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Category.js\n"));

/***/ })

});