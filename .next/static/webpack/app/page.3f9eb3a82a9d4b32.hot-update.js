"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4ee063a4e25a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjRlZTA2M2E0ZTI1YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Category.js":
/*!************************************!*\
  !*** ./src/components/Category.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _GoalCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoalCard */ \"(app-pages-browser)/./src/components/GoalCard.js\");\n/* harmony import */ var _AddGoalModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AddGoalModal */ \"(app-pages-browser)/./src/components/AddGoalModal.js\");\n/* harmony import */ var _AISuggestions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AISuggestions */ \"(app-pages-browser)/./src/components/AISuggestions.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Category = (param)=>{\n    let { category, userId } = param;\n    _s();\n    const [goals, setGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAISuggestions, setShowAISuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Sample goals based on the vision board image\n    const sampleGoals = {\n        personal: [\n            {\n                id: \"1\",\n                title: \"Documents\",\n                status: \"in-progress\",\n                icon: \"📄\"\n            },\n            {\n                id: \"2\",\n                title: \"Family\",\n                status: \"not-good\",\n                icon: \"👨‍👩‍👧‍👦\"\n            },\n            {\n                id: \"3\",\n                title: \"Exercise\",\n                status: \"not-good\",\n                icon: \"💪\"\n            },\n            {\n                id: \"4\",\n                title: \"Diet\",\n                status: \"not-good\",\n                icon: \"🥗\"\n            },\n            {\n                id: \"5\",\n                title: \"Prayers\",\n                status: \"not-good\",\n                icon: \"🙏\"\n            }\n        ],\n        learn: [\n            {\n                id: \"6\",\n                title: \"Coding\",\n                status: \"done\",\n                icon: \"💻\"\n            },\n            {\n                id: \"7\",\n                title: \"Design\",\n                status: \"done\",\n                icon: \"🎨\"\n            },\n            {\n                id: \"8\",\n                title: \"Business\",\n                status: \"done\",\n                icon: \"💼\"\n            },\n            {\n                id: \"9\",\n                title: \"Marketing\",\n                status: \"done\",\n                icon: \"📈\"\n            },\n            {\n                id: \"10\",\n                title: \"Finance\",\n                status: \"done\",\n                icon: \"💰\"\n            }\n        ],\n        work: [\n            {\n                id: \"11\",\n                title: \"Trading\",\n                status: \"in-progress\",\n                icon: \"📊\"\n            },\n            {\n                id: \"12\",\n                title: \"Real Estate\",\n                status: \"in-progress\",\n                icon: \"🏠\"\n            },\n            {\n                id: \"13\",\n                title: \"Digital Marketing\",\n                status: \"in-progress\",\n                icon: \"📱\"\n            },\n            {\n                id: \"14\",\n                title: \"Hubcv\",\n                status: \"not-good\",\n                icon: \"🔗\"\n            },\n            {\n                id: \"15\",\n                title: \"Codelude\",\n                status: \"not-good\",\n                icon: \"⚡\"\n            }\n        ],\n        finance: [\n            {\n                id: \"16\",\n                title: \"Net Worth\",\n                status: \"in-progress\",\n                icon: \"🏛️\"\n            },\n            {\n                id: \"17\",\n                title: \"Loans & Credits\",\n                status: \"not-good\",\n                icon: \"💳\"\n            },\n            {\n                id: \"18\",\n                title: \"Assets\",\n                status: \"not-started\",\n                icon: \"💎\"\n            },\n            {\n                id: \"19\",\n                title: \"Charity\",\n                status: \"not-started\",\n                icon: \"❤️\"\n            },\n            {\n                id: \"20\",\n                title: \"Investments\",\n                status: \"not-started\",\n                icon: \"💹\"\n            }\n        ],\n        weekend: [\n            {\n                id: \"21\",\n                title: \"Memories\",\n                status: \"in-progress\",\n                icon: \"📸\"\n            },\n            {\n                id: \"22\",\n                title: \"Travels\",\n                status: \"in-progress\",\n                icon: \"✈️\"\n            },\n            {\n                id: \"23\",\n                title: \"Shopping\",\n                status: \"not-good\",\n                icon: \"🛍️\"\n            },\n            {\n                id: \"24\",\n                title: \"Branding\",\n                status: \"not-started\",\n                icon: \"⭐\"\n            },\n            {\n                id: \"25\",\n                title: \"Events\",\n                status: \"not-started\",\n                icon: \"🎉\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Category.useEffect\": ()=>{\n            loadGoals();\n        }\n    }[\"Category.useEffect\"], [\n        category.id\n    ]);\n    const loadGoals = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load goals from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.list(userId, category.$id || category.id);\n                if (result.documents.length > 0) {\n                    setGoals(result.documents);\n                } else {\n                    // If no goals exist, create sample goals for this category\n                    await createSampleGoals();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using sample data:\", dbError);\n                const categoryGoals = sampleGoals[category.id] || [];\n                setGoals(categoryGoals);\n            }\n        } catch (error) {\n            console.error(\"Error loading goals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createSampleGoals = async ()=>{\n        try {\n            var _category_name;\n            const categoryGoals = sampleGoals[category.id] || sampleGoals[(_category_name = category.name) === null || _category_name === void 0 ? void 0 : _category_name.toLowerCase()] || [];\n            const createdGoals = [];\n            for(let i = 0; i < categoryGoals.length; i++){\n                const goal = categoryGoals[i];\n                try {\n                    const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.create(userId, {\n                        title: goal.title,\n                        icon: goal.icon,\n                        status: goal.status,\n                        categoryId: category.$id || category.id,\n                        order: i\n                    });\n                    createdGoals.push(created);\n                } catch (error) {\n                    console.error(\"Error creating sample goal:\", error);\n                    // If database creation fails, use sample data\n                    createdGoals.push(goal);\n                }\n            }\n            setGoals(createdGoals);\n        } catch (error) {\n            console.error(\"Error creating sample goals:\", error);\n            const categoryGoals = sampleGoals[category.id] || [];\n            setGoals(categoryGoals);\n        }\n    };\n    const addGoal = async (newGoal)=>{\n        try {\n            const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.create(userId, {\n                ...newGoal,\n                status: \"not-started\",\n                categoryId: category.$id || category.id,\n                order: goals.length\n            });\n            setGoals([\n                ...goals,\n                created\n            ]);\n        } catch (error) {\n            console.error(\"Error adding goal:\", error);\n            // Fallback to local state\n            const goal = {\n                id: Date.now().toString(),\n                ...newGoal,\n                status: \"not-started\"\n            };\n            setGoals([\n                ...goals,\n                goal\n            ]);\n        }\n    };\n    const updateGoal = async (goalId, updates)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.update(goalId, updates);\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        } catch (error) {\n            console.error(\"Error updating goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.map((goal)=>(goal.$id || goal.id) === goalId ? {\n                    ...goal,\n                    ...updates\n                } : goal));\n        }\n    };\n    const deleteGoal = async (goalId)=>{\n        try {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_5__.goalService.delete(goalId);\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        } catch (error) {\n            console.error(\"Error deleting goal:\", error);\n            // Fallback to local state update\n            setGoals(goals.filter((goal)=>(goal.$id || goal.id) !== goalId));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(category.color, \" rounded-lg p-4 min-h-[400px] shadow-lg hover:shadow-xl transition-shadow duration-300\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddModal(true),\n                        className: \"text-white hover:bg-white/20 rounded-full p-1 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: goals.map((goal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GoalCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    goal: goal,\n                                    onUpdate: updateGoal,\n                                    onDelete: deleteGoal\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, goal.id, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined),\n                    goals.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/70 text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No goals yet\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"mt-2 text-white/90 hover:text-white underline\",\n                                children: \"Add your first goal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-2 border-t border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddModal(true),\n                            className: \"w-full flex items-center justify-center space-x-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg py-2 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"New goal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddGoalModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onAdd: addGoal,\n                categoryName: category.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/Category.js\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Category, \"v6DLO3shCYQRP/AMsNflIU1Ns7Q=\");\n_c = Category;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\nvar _c;\n$RefreshReg$(_c, \"Category\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Category.js\n"));

/***/ })

});