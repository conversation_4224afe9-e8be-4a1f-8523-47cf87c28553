"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/brain.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Brain)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 18V5\",\n            key: \"adv99a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4\",\n            key: \"1e3is1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5\",\n            key: \"1gqd8o\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17.997 5.125a4 4 0 0 1 2.526 5.77\",\n            key: \"iwvgf7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 18a4 4 0 0 0 2-7.464\",\n            key: \"efp6ie\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517\",\n            key: \"1gq6am\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 18a4 4 0 0 1-2-7.464\",\n            key: \"k1g0md\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6.003 5.125a4 4 0 0 0-2.526 5.77\",\n            key: \"q97ue3\"\n        }\n    ]\n];\nconst Brain = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"brain\", __iconNode);\n //# sourceMappingURL=brain.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/quote.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Quote)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z\",\n            key: \"rib7q0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z\",\n            key: \"1ymkrd\"\n        }\n    ]\n];\nconst Quote = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"quote\", __iconNode);\n //# sourceMappingURL=quote.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/target.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Target)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"6\",\n            key: \"1vlfrh\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"2\",\n            key: \"1c9p78\"\n        }\n    ]\n];\nconst Target = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"target\", __iconNode);\n //# sourceMappingURL=target.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 7h6v6\",\n            key: \"box55l\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 7-8.5 8.5-5-5L2 17\",\n            key: \"1t1m79\"\n        }\n    ]\n];\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trending-up\", __iconNode);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/app.css":
/*!*************************!*\
  !*** ./src/app/app.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8edd665e524a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXBwLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L3NyYy9hcHAvYXBwLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhlZGQ2NjVlNTI0YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AIInsights.js":
/*!**************************************!*\
  !*** ./src/components/AIInsights.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Quote,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Quote,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Quote,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Quote,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _lib_aiService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/aiService */ \"(app-pages-browser)/./src/lib/aiService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AIInsights = (param)=>{\n    let { goals, userId } = param;\n    _s();\n    const [insights, setInsights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIInsights.useEffect\": ()=>{\n            if (goals && goals.length >= 0) {\n                generateInsights();\n            }\n        }\n    }[\"AIInsights.useEffect\"], [\n        goals\n    ]);\n    const generateInsights = async ()=>{\n        setLoading(true);\n        try {\n            // Simulate AI processing\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const analysis = _lib_aiService__WEBPACK_IMPORTED_MODULE_2__.aiService.analyzeProgress(goals);\n            setInsights(analysis);\n        } catch (error) {\n            console.error(\"Error generating insights:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            className: \"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"AI is analyzing your progress...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/20 rounded mb-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/20 rounded w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!insights) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"AI Insights\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium\",\n                                        children: \"Progress Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 mb-3\",\n                                children: insights.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined),\n                            insights.stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/70\",\n                                                    children: \"Completion Rate:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold text-lg\",\n                                                    children: [\n                                                        insights.stats.completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/70\",\n                                                    children: \"Total Goals:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold text-lg\",\n                                                    children: insights.stats.total\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium\",\n                                        children: \"Smart Suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 text-sm mb-3\",\n                                children: insights.suggestion\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Quote_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16,\n                                            className: \"mt-1 text-white/70\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm italic text-white/90\",\n                                            children: [\n                                                '\"',\n                                                insights.motivation,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            insights.stats && insights.stats.notGood > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.5\n                },\n                className: \"mt-4 p-3 bg-yellow-500/20 border border-yellow-400/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-yellow-300\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-yellow-100\",\n                                children: \"Attention Needed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-100 text-sm\",\n                        children: [\n                            \"You have \",\n                            insights.stats.notGood,\n                            ' goal(s) marked as \"Not Good\". Consider breaking them down into smaller tasks or adjusting your approach.'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex flex-wrap gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generateInsights,\n                        className: \"bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full text-sm transition-colors\",\n                        children: \"Refresh Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    insights.stats && insights.stats.completionRate >= 80 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-green-500/30 px-3 py-1 rounded-full text-sm\",\n                        children: \"\\uD83C\\uDF89 Goal Crusher!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, undefined),\n                    insights.stats && insights.stats.inProgress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-blue-500/30 px-3 py-1 rounded-full text-sm\",\n                        children: [\n                            \"\\uD83D\\uDCAA \",\n                            insights.stats.inProgress,\n                            \" In Progress\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/AIInsights.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AIInsights, \"EgWb806INQm+ZueGrN37MX6QyP0=\");\n_c = AIInsights;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIInsights);\nvar _c;\n$RefreshReg$(_c, \"AIInsights\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIInsights.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VisionBoard.js":
/*!***************************************!*\
  !*** ./src/components/VisionBoard.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Category */ \"(app-pages-browser)/./src/components/Category.js\");\n/* harmony import */ var _ProgressOverview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressOverview */ \"(app-pages-browser)/./src/components/ProgressOverview.js\");\n/* harmony import */ var _AIInsights__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AIInsights */ \"(app-pages-browser)/./src/components/AIInsights.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.js\");\n/* harmony import */ var _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRealtime */ \"(app-pages-browser)/./src/hooks/useRealtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst VisionBoard = (param)=>{\n    let { userId } = param;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Default categories based on the vision board image\n    const defaultCategories = [\n        {\n            id: \"personal\",\n            name: \"Personal\",\n            color: \"bg-purple-600\"\n        },\n        {\n            id: \"learn\",\n            name: \"Learn\",\n            color: \"bg-orange-600\"\n        },\n        {\n            id: \"work\",\n            name: \"Work\",\n            color: \"bg-yellow-600\"\n        },\n        {\n            id: \"finance\",\n            name: \"Finance\",\n            color: \"bg-blue-600\"\n        },\n        {\n            id: \"weekend\",\n            name: \"Weekend\",\n            color: \"bg-pink-600\"\n        }\n    ];\n    // Real-time update handlers\n    const handleCategoryUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VisionBoard.useCallback[handleCategoryUpdate]\": (payload, events)=>{\n            const isCreate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isCreate\": (e)=>e.includes('.create')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isCreate\"]);\n            const isUpdate = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\": (e)=>e.includes('.update')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isUpdate\"]);\n            const isDelete = events.some({\n                \"VisionBoard.useCallback[handleCategoryUpdate].isDelete\": (e)=>e.includes('.delete')\n            }[\"VisionBoard.useCallback[handleCategoryUpdate].isDelete\"]);\n            if (isCreate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>[\n                            ...prev,\n                            payload\n                        ]\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isUpdate) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.map({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id === payload.$id ? payload : cat\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            } else if (isDelete) {\n                setCategories({\n                    \"VisionBoard.useCallback[handleCategoryUpdate]\": (prev)=>prev.filter({\n                            \"VisionBoard.useCallback[handleCategoryUpdate]\": (cat)=>cat.$id !== payload.$id\n                        }[\"VisionBoard.useCallback[handleCategoryUpdate]\"])\n                }[\"VisionBoard.useCallback[handleCategoryUpdate]\"]);\n            }\n        }\n    }[\"VisionBoard.useCallback[handleCategoryUpdate]\"], []);\n    // Set up real-time subscriptions\n    (0,_hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(userId, handleCategoryUpdate, null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VisionBoard.useEffect\": ()=>{\n            if (userId) {\n                loadCategories();\n            }\n        }\n    }[\"VisionBoard.useEffect\"], [\n        userId\n    ]);\n    const loadCategories = async ()=>{\n        try {\n            setLoading(true);\n            // Try to load categories from database\n            try {\n                const result = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.categoryService.list(userId);\n                if (result.documents.length > 0) {\n                    setCategories(result.documents);\n                } else {\n                    // If no categories exist, create default ones\n                    await createDefaultCategories();\n                }\n            } catch (dbError) {\n                console.log(\"Database not ready, using default categories:\", dbError);\n                setCategories(defaultCategories);\n            }\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n            setCategories(defaultCategories);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createDefaultCategories = async ()=>{\n        try {\n            const createdCategories = [];\n            for(let i = 0; i < defaultCategories.length; i++){\n                const category = defaultCategories[i];\n                const created = await _lib_database__WEBPACK_IMPORTED_MODULE_5__.categoryService.create(userId, {\n                    name: category.name,\n                    color: category.color,\n                    order: i\n                });\n                createdCategories.push(created);\n            }\n            setCategories(createdCategories);\n        } catch (error) {\n            console.error(\"Error creating default categories:\", error);\n            setCategories(defaultCategories);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-white mb-2\",\n                            children: \"SXE Vision Board\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-lg\",\n                            children: \"Visualize your goals, track your progress, achieve your dreams\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressOverview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userId: userId\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                    children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                category: category,\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, undefined)\n                        }, category.id, false, {\n                            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/sxe/sxe_client/src/components/VisionBoard.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VisionBoard, \"EalBv6uo19YompPwL9FszZ46qf0=\", false, function() {\n    return [\n        _hooks_useRealtime__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = VisionBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisionBoard);\nvar _c;\n$RefreshReg$(_c, \"VisionBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VisionBoard.js\n"));

/***/ })

});