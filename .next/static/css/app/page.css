/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/app.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --font-weight-light: 300;
    --font-weight-semibold: 600;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --animate-spin: spin 1s linear infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .fixed {
    position: fixed;
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-25 {
    margin-top: calc(var(--spacing) * 25);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-52 {
    height: calc(var(--spacing) * 52);
  }
  .h-\[1px\] {
    height: 1px;
  }
  .h-full {
    height: 100%;
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-38 {
    width: calc(var(--spacing) * 38);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .max-w-\[40em\] {
    max-width: 40em;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-rows-3 {
    grid-template-rows: repeat(3, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .items-center {
    align-items: center;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-7 {
    gap: calc(var(--spacing) * 7);
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .rounded-\[25\%\] {
    border-radius: 25%;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-\[\#19191C0A\] {
    border-color: #19191C0A;
  }
  .border-\[\#EDEDF0\] {
    border-color: #EDEDF0;
  }
  .border-\[\#FAFAFB\] {
    border-color: #FAFAFB;
  }
  .border-\[\#FD366E52\] {
    border-color: #FD366E52;
  }
  .bg-\[\#10B9813D\] {
    background-color: #10B9813D;
  }
  .bg-\[\#E6E6E6\] {
    background-color: #E6E6E6;
  }
  .bg-\[\#EDEDF0\] {
    background-color: #EDEDF0;
  }
  .bg-\[\#F9F9FA\] {
    background-color: #F9F9FA;
  }
  .bg-\[\#FAFAFB\] {
    background-color: #FAFAFB;
  }
  .bg-\[\#FD366E14\] {
    background-color: #FD366E14;
  }
  .bg-\[\#FD366E\] {
    background-color: #FD366E;
  }
  .bg-\[\#FF453A3D\] {
    background-color: #FF453A3D;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-gradient-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-\[\#f02e65\] {
    --tw-gradient-from: #f02e65;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .fill-\[\#FD366E\] {
    fill: #FD366E;
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .font-\[Fira_Code\] {
    font-family: Fira Code;
  }
  .font-\[Inter\] {
    font-family: Inter;
  }
  .font-\[Poppins\] {
    font-family: Poppins;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .text-\[\#0A714F\] {
    color: #0A714F;
  }
  .text-\[\#2D2D31\] {
    color: #2D2D31;
  }
  .text-\[\#56565C\] {
    color: #56565C;
  }
  .text-\[\#97979B\] {
    color: #97979B;
  }
  .text-\[\#B31212\] {
    color: #B31212;
  }
  .text-\[\#D8D8DB\] {
    color: #D8D8DB;
  }
  .text-\[\#FD366E\] {
    color: #FD366E;
  }
  .text-gray-200 {
    color: var(--color-gray-200);
  }
  .text-white {
    color: var(--color-white);
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow-\[0px_2px_12px_0px_hsla\(0\,0\%\,0\%\,0\.03\)\] {
    --tw-shadow: 0px 2px 12px 0px var(--tw-shadow-color, hsla(0,0%,0%,0.03));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0px_9\.36px_9\.36px_0px_hsla\(0\,0\%\,0\%\,0\.04\)\] {
    --tw-shadow: 0px 9.36px 9.36px 0px var(--tw-shadow-color, hsla(0,0%,0%,0.04));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-2500 {
    --tw-duration: 2500ms;
    transition-duration: 2500ms;
  }
  .marker\:content-none {
    & *::marker {
      --tw-content: none;
      content: none;
    }
    &::marker {
      --tw-content: none;
      content: none;
    }
    & *::-webkit-details-marker {
      --tw-content: none;
      content: none;
    }
    &::-webkit-details-marker {
      --tw-content: none;
      content: none;
    }
  }
  .lg\:mt-34 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 34);
    }
  }
  .lg\:table-cell {
    @media (width >= 64rem) {
      display: table-cell;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-rows-none {
    @media (width >= 64rem) {
      grid-template-rows: none;
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:p-9 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 9);
    }
  }
  .dark\:text-gray-600 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-600);
    }
  }
}
summary::-webkit-details-marker {
  display: none;
}
.checker-background::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(#e6e6e690 1px, transparent 1px),
    linear-gradient(90deg, #e6e6e690 1px, transparent 1px);
  background-size: 3.7em 3.7em;
  mask-image: radial-gradient(ellipse at 50% 40%, black 0%, transparent 55%);
  z-index: -1;
  background-position-x: center;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-duration: initial;
    }
  }
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@appwrite.io/pink-icons/dist/icon.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
    font-family: "icon";
    font-display: swap;
    src: url(/_next/static/media/icon.e32c0ef3.eot); /* IE9*/
    src: url(/_next/static/media/icon.e32c0ef3.eot#iefix) format('embedded-opentype'), /* IE6-IE8 */
    url(/_next/static/media/icon.273cebc3.woff2) format("woff2"),
    url(/_next/static/media/icon.7f6bffb2.woff) format("woff"),
    url(/_next/static/media/icon.efd7606d.ttf) format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
    url(/_next/static/media/icon.75a9875a.svg#icon) format('svg'); /* iOS 4.1- */
}

[class^="icon-"], [class*=" icon-"] {
    font-family: 'icon' !important;font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

:root {
--icon-academic-cap: "\ea01";
--icon-adjustments: "\ea02";
--icon-akamai: "\ea03";
--icon-algolia: "\ea04";
--icon-amazon: "\ea05";
--icon-android: "\ea06";
--icon-angular: "\ea07";
--icon-annotation: "\ea08";
--icon-anonymous: "\ea09";
--icon-api: "\ea0a";
--icon-apple: "\ea0b";
--icon-appwrite: "\ea0c";
--icon-archive: "\ea0d";
--icon-arrow-circle-down: "\ea0e";
--icon-arrow-circle-left: "\ea0f";
--icon-arrow-circle-right: "\ea10";
--icon-arrow-circle-up: "\ea11";
--icon-arrow-down: "\ea12";
--icon-arrow-expand: "\ea13";
--icon-arrow-left: "\ea14";
--icon-arrow-narrow-down: "\ea15";
--icon-arrow-narrow-left: "\ea16";
--icon-arrow-narrow-right: "\ea17";
--icon-arrow-narrow-up: "\ea18";
--icon-arrow-right: "\ea19";
--icon-arrow-sm-down: "\ea1a";
--icon-arrow-sm-left: "\ea1b";
--icon-arrow-sm-right: "\ea1c";
--icon-arrow-sm-up: "\ea1d";
--icon-arrow-up: "\ea1e";
--icon-astro: "\ea1f";
--icon-at-symbol: "\ea20";
--icon-auth0: "\ea21";
--icon-authentik: "\ea22";
--icon-autodesk: "\ea23";
--icon-azure: "\ea24";
--icon-backspace: "\ea25";
--icon-badge-check: "\ea26";
--icon-ban: "\ea27";
--icon-beaker: "\ea28";
--icon-behance: "\ea29";
--icon-bell: "\ea2a";
--icon-bitBucket: "\ea2b";
--icon-bitly: "\ea2c";
--icon-book-open: "\ea2d";
--icon-bookmark-alt: "\ea2e";
--icon-bookmark: "\ea2f";
--icon-box: "\ea30";
--icon-briefcase: "\ea31";
--icon-bun-sh: "\ea32";
--icon-cake: "\ea33";
--icon-calculator: "\ea34";
--icon-calendar: "\ea35";
--icon-camera: "\ea36";
--icon-cash: "\ea37";
--icon-chart-bar: "\ea38";
--icon-chart-pie: "\ea39";
--icon-chart-square-bar: "\ea3a";
--icon-chat-alt-2: "\ea3b";
--icon-chat-alt: "\ea3c";
--icon-chat: "\ea3d";
--icon-check-circle: "\ea3e";
--icon-check: "\ea3f";
--icon-cheveron-down: "\ea40";
--icon-cheveron-left: "\ea41";
--icon-cheveron-right: "\ea42";
--icon-cheveron-up: "\ea43";
--icon-chevron-double-down: "\ea44";
--icon-chevron-double-left: "\ea45";
--icon-chevron-double-right: "\ea46";
--icon-chevron-double-up: "\ea47";
--icon-chip: "\ea48";
--icon-clipboard-arrow: "\ea49";
--icon-clipboard-check: "\ea4a";
--icon-clipboard-copy: "\ea4b";
--icon-clipboard-list: "\ea4c";
--icon-clock: "\ea4d";
--icon-cloud-download: "\ea4e";
--icon-cloud-upload: "\ea4f";
--icon-cloud: "\ea50";
--icon-code: "\ea51";
--icon-cog: "\ea52";
--icon-collection: "\ea53";
--icon-color-swatch: "\ea54";
--icon-cpp: "\ea55";
--icon-credit-card: "\ea56";
--icon-css3: "\ea57";
--icon-cube-transparent: "\ea58";
--icon-cube: "\ea59";
--icon-currency-bangladesh: "\ea5a";
--icon-currency-dollar: "\ea5b";
--icon-currency-euro: "\ea5c";
--icon-currency-pound: "\ea5d";
--icon-currency-rupee: "\ea5e";
--icon-currency-yen: "\ea5f";
--icon-cursor-click: "\ea60";
--icon-dailymotion: "\ea61";
--icon-dart: "\ea62";
--icon-database: "\ea63";
--icon-deno: "\ea64";
--icon-desktop-computer: "\ea65";
--icon-device-ipad: "\ea66";
--icon-device-mobile: "\ea67";
--icon-discord: "\ea68";
--icon-disqus: "\ea69";
--icon-docker: "\ea6a";
--icon-document-add: "\ea6b";
--icon-document-download: "\ea6c";
--icon-document-duplicate: "\ea6d";
--icon-document-remove: "\ea6e";
--icon-document-report: "\ea6f";
--icon-document-search: "\ea70";
--icon-document-text: "\ea71";
--icon-document: "\ea72";
--icon-dotnet: "\ea73";
--icon-dots-circle-horizontal: "\ea74";
--icon-dots-horizontal: "\ea75";
--icon-dots-vertical: "\ea76";
--icon-download: "\ea77";
--icon-dribbble: "\ea78";
--icon-dropbox: "\ea79";
--icon-duplicate: "\ea7a";
--icon-emoji-happy: "\ea7b";
--icon-emoji-sad: "\ea7c";
--icon-etsy: "\ea7d";
--icon-exclamation-circle: "\ea7e";
--icon-exclamation: "\ea7f";
--icon-external-link: "\ea80";
--icon-eye-off: "\ea81";
--icon-eye: "\ea82";
--icon-facebook: "\ea83";
--icon-fast-forward: "\ea84";
--icon-figma: "\ea85";
--icon-film: "\ea86";
--icon-filter-line: "\ea87";
--icon-filter: "\ea88";
--icon-finger-print: "\ea89";
--icon-fire: "\ea8a";
--icon-firefox: "\ea8b";
--icon-flag: "\ea8c";
--icon-flutter: "\ea8d";
--icon-folder-add: "\ea8e";
--icon-folder-download: "\ea8f";
--icon-folder-open: "\ea90";
--icon-folder-remove: "\ea91";
--icon-folder: "\ea92";
--icon-gift: "\ea93";
--icon-git-branch: "\ea94";
--icon-git-commit: "\ea95";
--icon-git: "\ea96";
--icon-github: "\ea97";
--icon-gitlab: "\ea98";
--icon-globe-alt: "\ea99";
--icon-globe: "\ea9a";
--icon-go: "\ea9b";
--icon-google: "\ea9c";
--icon-graphql: "\ea9d";
--icon-hand: "\ea9e";
--icon-hashtag: "\ea9f";
--icon-heart: "\eaa0";
--icon-home: "\eaa1";
--icon-html5: "\eaa2";
--icon-identification: "\eaa3";
--icon-inbox-in: "\eaa4";
--icon-inbox: "\eaa5";
--icon-info: "\eaa6";
--icon-instagram: "\eaa7";
--icon-ionic: "\eaa8";
--icon-ios: "\eaa9";
--icon-java: "\eaaa";
--icon-js: "\eaab";
--icon-key: "\eaac";
--icon-kotlin: "\eaad";
--icon-light-bulb: "\eaae";
--icon-lightning-bolt: "\eaaf";
--icon-link: "\eab0";
--icon-linkedin: "\eab1";
--icon-linux: "\eab2";
--icon-list: "\eab3";
--icon-location-marker: "\eab4";
--icon-lock-closed: "\eab5";
--icon-lock-open: "\eab6";
--icon-logout-left: "\eab7";
--icon-logout-right: "\eab8";
--icon-mail-open: "\eab9";
--icon-mail: "\eaba";
--icon-map: "\eabb";
--icon-md-library: "\eabc";
--icon-medium: "\eabd";
--icon-meilisearch: "\eabe";
--icon-menu-alt-1: "\eabf";
--icon-menu-alt-2: "\eac0";
--icon-menu-alt-3: "\eac1";
--icon-menu-alt-4: "\eac2";
--icon-menu: "\eac3";
--icon-microphone: "\eac4";
--icon-microsoft: "\eac5";
--icon-microsoft_edge: "\eac6";
--icon-minus-circle: "\eac7";
--icon-minus-sm: "\eac8";
--icon-minus: "\eac9";
--icon-mode: "\eaca";
--icon-mongodb: "\eacb";
--icon-moon: "\eacc";
--icon-ms_yammer: "\eacd";
--icon-msg91: "\eace";
--icon-music-note: "\eacf";
--icon-neo4j: "\ead0";
--icon-neon: "\ead1";
--icon-newspaper: "\ead2";
--icon-nextjs: "\ead3";
--icon-node_js: "\ead4";
--icon-notion: "\ead5";
--icon-null: "\ead6";
--icon-nuxt: "\ead7";
--icon-office-building: "\ead8";
--icon-okta: "\ead9";
--icon-open-ai: "\eada";
--icon-openid: "\eadb";
--icon-opera: "\eadc";
--icon-pangea: "\eadd";
--icon-paper-airplane: "\eade";
--icon-paper-clip: "\eadf";
--icon-pause: "\eae0";
--icon-paypal: "\eae1";
--icon-pencil-alt: "\eae2";
--icon-pencil: "\eae3";
--icon-perspective-api: "\eae4";
--icon-phone-incoming: "\eae5";
--icon-phone-missed-call: "\eae6";
--icon-phone-outgoing: "\eae7";
--icon-phone: "\eae8";
--icon-photograph: "\eae9";
--icon-php: "\eaea";
--icon-pinterest: "\eaeb";
--icon-play-button: "\eaec";
--icon-play: "\eaed";
--icon-plus-circle: "\eaee";
--icon-plus-sm: "\eaef";
--icon-plus: "\eaf0";
--icon-podio: "\eaf1";
--icon-presentation-chart-1: "\eaf2";
--icon-presentation-chart-2: "\eaf3";
--icon-printer: "\eaf4";
--icon-product_hunt: "\eaf5";
--icon-puzzle: "\eaf6";
--icon-python: "\eaf7";
--icon-qrcode: "\eaf8";
--icon-question-mark-circle: "\eaf9";
--icon-qwik: "\eafa";
--icon-react-native: "\eafb";
--icon-react: "\eafc";
--icon-receipt-refund: "\eafd";
--icon-receipt-tax: "\eafe";
--icon-reddit: "\eaff";
--icon-redis: "\eb00";
--icon-refresh: "\eb01";
--icon-relation: "\eb02";
--icon-relationship: "\eb03";
--icon-replay: "\eb04";
--icon-rewind: "\eb05";
--icon-rss: "\eb06";
--icon-ruby: "\eb07";
--icon-safari: "\eb08";
--icon-salesforce: "\eb09";
--icon-save-as: "\eb0a";
--icon-save: "\eb0b";
--icon-scale: "\eb0c";
--icon-scissors: "\eb0d";
--icon-search-circle: "\eb0e";
--icon-search: "\eb0f";
--icon-selector: "\eb10";
--icon-send: "\eb11";
--icon-server: "\eb12";
--icon-share: "\eb13";
--icon-shield-check: "\eb14";
--icon-shield-exclamation: "\eb15";
--icon-shopping-bag: "\eb16";
--icon-shopping-cart: "\eb17";
--icon-skype: "\eb18";
--icon-slack: "\eb19";
--icon-solidjs: "\eb1a";
--icon-sort-ascending: "\eb1b";
--icon-sort-descending: "\eb1c";
--icon-sparkles: "\eb1d";
--icon-speakerphone: "\eb1e";
--icon-spin: "\eb1f";
--icon-spotify: "\eb20";
--icon-star: "\eb21";
--icon-status-offline: "\eb22";
--icon-status-online: "\eb23";
--icon-stop: "\eb24";
--icon-stripe: "\eb25";
--icon-sun: "\eb26";
--icon-support: "\eb27";
--icon-svelte: "\eb28";
--icon-swift: "\eb29";
--icon-switch-horizontal: "\eb2a";
--icon-switch-vertical: "\eb2b";
--icon-table: "\eb2c";
--icon-tag: "\eb2d";
--icon-telegram: "\eb2e";
--icon-telesign: "\eb2f";
--icon-template: "\eb30";
--icon-terminal: "\eb31";
--icon-text: "\eb32";
--icon-textmagic: "\eb33";
--icon-thumb-dowm: "\eb34";
--icon-thumb-up: "\eb35";
--icon-ticket: "\eb36";
--icon-tiktok: "\eb37";
--icon-toggle: "\eb38";
--icon-tradeshift: "\eb39";
--icon-translate: "\eb3a";
--icon-trash: "\eb3b";
--icon-trending-down: "\eb3c";
--icon-trending-up: "\eb3d";
--icon-truck: "\eb3e";
--icon-tumbir: "\eb3f";
--icon-twilio: "\eb40";
--icon-twitch: "\eb41";
--icon-twitter: "\eb42";
--icon-typescript: "\eb43";
--icon-unity: "\eb44";
--icon-upload: "\eb45";
--icon-upstash: "\eb46";
--icon-user-add: "\eb47";
--icon-user-circle: "\eb48";
--icon-user-group: "\eb49";
--icon-user-remove: "\eb4a";
--icon-user: "\eb4b";
--icon-users: "\eb4c";
--icon-variable: "\eb4d";
--icon-video-camera: "\eb4e";
--icon-video: "\eb4f";
--icon-view-boards: "\eb50";
--icon-view-grid-add: "\eb51";
--icon-view-grid: "\eb52";
--icon-view-list: "\eb53";
--icon-vimeo: "\eb54";
--icon-vk: "\eb55";
--icon-volume-off: "\eb56";
--icon-volume-up: "\eb57";
--icon-vonage: "\eb58";
--icon-vs_code: "\eb59";
--icon-vue: "\eb5a";
--icon-whatsapp: "\eb5b";
--icon-wifi: "\eb5c";
--icon-wordpress: "\eb5d";
--icon-x-circle: "\eb5e";
--icon-x: "\eb5f";
--icon-yahoo: "\eb60";
--icon-yandex: "\eb61";
--icon-ycombinator: "\eb62";
--icon-youtube: "\eb63";
--icon-zoom-in: "\eb64";
--icon-zoom-out: "\eb65";
--icon-zoom: "\eb66";
}
.icon-academic-cap:before { content: var(--icon-academic-cap); }
.icon-adjustments:before { content: var(--icon-adjustments); }
.icon-akamai:before { content: var(--icon-akamai); }
.icon-algolia:before { content: var(--icon-algolia); }
.icon-amazon:before { content: var(--icon-amazon); }
.icon-android:before { content: var(--icon-android); }
.icon-angular:before { content: var(--icon-angular); }
.icon-annotation:before { content: var(--icon-annotation); }
.icon-anonymous:before { content: var(--icon-anonymous); }
.icon-api:before { content: var(--icon-api); }
.icon-apple:before { content: var(--icon-apple); }
.icon-appwrite:before { content: var(--icon-appwrite); }
.icon-archive:before { content: var(--icon-archive); }
.icon-arrow-circle-down:before { content: var(--icon-arrow-circle-down); }
.icon-arrow-circle-left:before { content: var(--icon-arrow-circle-left); }
.icon-arrow-circle-right:before { content: var(--icon-arrow-circle-right); }
.icon-arrow-circle-up:before { content: var(--icon-arrow-circle-up); }
.icon-arrow-down:before { content: var(--icon-arrow-down); }
.icon-arrow-expand:before { content: var(--icon-arrow-expand); }
.icon-arrow-left:before { content: var(--icon-arrow-left); }
.icon-arrow-narrow-down:before { content: var(--icon-arrow-narrow-down); }
.icon-arrow-narrow-left:before { content: var(--icon-arrow-narrow-left); }
.icon-arrow-narrow-right:before { content: var(--icon-arrow-narrow-right); }
.icon-arrow-narrow-up:before { content: var(--icon-arrow-narrow-up); }
.icon-arrow-right:before { content: var(--icon-arrow-right); }
.icon-arrow-sm-down:before { content: var(--icon-arrow-sm-down); }
.icon-arrow-sm-left:before { content: var(--icon-arrow-sm-left); }
.icon-arrow-sm-right:before { content: var(--icon-arrow-sm-right); }
.icon-arrow-sm-up:before { content: var(--icon-arrow-sm-up); }
.icon-arrow-up:before { content: var(--icon-arrow-up); }
.icon-astro:before { content: var(--icon-astro); }
.icon-at-symbol:before { content: var(--icon-at-symbol); }
.icon-auth0:before { content: var(--icon-auth0); }
.icon-authentik:before { content: var(--icon-authentik); }
.icon-autodesk:before { content: var(--icon-autodesk); }
.icon-azure:before { content: var(--icon-azure); }
.icon-backspace:before { content: var(--icon-backspace); }
.icon-badge-check:before { content: var(--icon-badge-check); }
.icon-ban:before { content: var(--icon-ban); }
.icon-beaker:before { content: var(--icon-beaker); }
.icon-behance:before { content: var(--icon-behance); }
.icon-bell:before { content: var(--icon-bell); }
.icon-bitBucket:before { content: var(--icon-bitBucket); }
.icon-bitly:before { content: var(--icon-bitly); }
.icon-book-open:before { content: var(--icon-book-open); }
.icon-bookmark-alt:before { content: var(--icon-bookmark-alt); }
.icon-bookmark:before { content: var(--icon-bookmark); }
.icon-box:before { content: var(--icon-box); }
.icon-briefcase:before { content: var(--icon-briefcase); }
.icon-bun-sh:before { content: var(--icon-bun-sh); }
.icon-cake:before { content: var(--icon-cake); }
.icon-calculator:before { content: var(--icon-calculator); }
.icon-calendar:before { content: var(--icon-calendar); }
.icon-camera:before { content: var(--icon-camera); }
.icon-cash:before { content: var(--icon-cash); }
.icon-chart-bar:before { content: var(--icon-chart-bar); }
.icon-chart-pie:before { content: var(--icon-chart-pie); }
.icon-chart-square-bar:before { content: var(--icon-chart-square-bar); }
.icon-chat-alt-2:before { content: var(--icon-chat-alt-2); }
.icon-chat-alt:before { content: var(--icon-chat-alt); }
.icon-chat:before { content: var(--icon-chat); }
.icon-check-circle:before { content: var(--icon-check-circle); }
.icon-check:before { content: var(--icon-check); }
.icon-cheveron-down:before { content: var(--icon-cheveron-down); }
.icon-cheveron-left:before { content: var(--icon-cheveron-left); }
.icon-cheveron-right:before { content: var(--icon-cheveron-right); }
.icon-cheveron-up:before { content: var(--icon-cheveron-up); }
.icon-chevron-double-down:before { content: var(--icon-chevron-double-down); }
.icon-chevron-double-left:before { content: var(--icon-chevron-double-left); }
.icon-chevron-double-right:before { content: var(--icon-chevron-double-right); }
.icon-chevron-double-up:before { content: var(--icon-chevron-double-up); }
.icon-chip:before { content: var(--icon-chip); }
.icon-clipboard-arrow:before { content: var(--icon-clipboard-arrow); }
.icon-clipboard-check:before { content: var(--icon-clipboard-check); }
.icon-clipboard-copy:before { content: var(--icon-clipboard-copy); }
.icon-clipboard-list:before { content: var(--icon-clipboard-list); }
.icon-clock:before { content: var(--icon-clock); }
.icon-cloud-download:before { content: var(--icon-cloud-download); }
.icon-cloud-upload:before { content: var(--icon-cloud-upload); }
.icon-cloud:before { content: var(--icon-cloud); }
.icon-code:before { content: var(--icon-code); }
.icon-cog:before { content: var(--icon-cog); }
.icon-collection:before { content: var(--icon-collection); }
.icon-color-swatch:before { content: var(--icon-color-swatch); }
.icon-cpp:before { content: var(--icon-cpp); }
.icon-credit-card:before { content: var(--icon-credit-card); }
.icon-css3:before { content: var(--icon-css3); }
.icon-cube-transparent:before { content: var(--icon-cube-transparent); }
.icon-cube:before { content: var(--icon-cube); }
.icon-currency-bangladesh:before { content: var(--icon-currency-bangladesh); }
.icon-currency-dollar:before { content: var(--icon-currency-dollar); }
.icon-currency-euro:before { content: var(--icon-currency-euro); }
.icon-currency-pound:before { content: var(--icon-currency-pound); }
.icon-currency-rupee:before { content: var(--icon-currency-rupee); }
.icon-currency-yen:before { content: var(--icon-currency-yen); }
.icon-cursor-click:before { content: var(--icon-cursor-click); }
.icon-dailymotion:before { content: var(--icon-dailymotion); }
.icon-dart:before { content: var(--icon-dart); }
.icon-database:before { content: var(--icon-database); }
.icon-deno:before { content: var(--icon-deno); }
.icon-desktop-computer:before { content: var(--icon-desktop-computer); }
.icon-device-ipad:before { content: var(--icon-device-ipad); }
.icon-device-mobile:before { content: var(--icon-device-mobile); }
.icon-discord:before { content: var(--icon-discord); }
.icon-disqus:before { content: var(--icon-disqus); }
.icon-docker:before { content: var(--icon-docker); }
.icon-document-add:before { content: var(--icon-document-add); }
.icon-document-download:before { content: var(--icon-document-download); }
.icon-document-duplicate:before { content: var(--icon-document-duplicate); }
.icon-document-remove:before { content: var(--icon-document-remove); }
.icon-document-report:before { content: var(--icon-document-report); }
.icon-document-search:before { content: var(--icon-document-search); }
.icon-document-text:before { content: var(--icon-document-text); }
.icon-document:before { content: var(--icon-document); }
.icon-dotnet:before { content: var(--icon-dotnet); }
.icon-dots-circle-horizontal:before { content: var(--icon-dots-circle-horizontal); }
.icon-dots-horizontal:before { content: var(--icon-dots-horizontal); }
.icon-dots-vertical:before { content: var(--icon-dots-vertical); }
.icon-download:before { content: var(--icon-download); }
.icon-dribbble:before { content: var(--icon-dribbble); }
.icon-dropbox:before { content: var(--icon-dropbox); }
.icon-duplicate:before { content: var(--icon-duplicate); }
.icon-emoji-happy:before { content: var(--icon-emoji-happy); }
.icon-emoji-sad:before { content: var(--icon-emoji-sad); }
.icon-etsy:before { content: var(--icon-etsy); }
.icon-exclamation-circle:before { content: var(--icon-exclamation-circle); }
.icon-exclamation:before { content: var(--icon-exclamation); }
.icon-external-link:before { content: var(--icon-external-link); }
.icon-eye-off:before { content: var(--icon-eye-off); }
.icon-eye:before { content: var(--icon-eye); }
.icon-facebook:before { content: var(--icon-facebook); }
.icon-fast-forward:before { content: var(--icon-fast-forward); }
.icon-figma:before { content: var(--icon-figma); }
.icon-film:before { content: var(--icon-film); }
.icon-filter-line:before { content: var(--icon-filter-line); }
.icon-filter:before { content: var(--icon-filter); }
.icon-finger-print:before { content: var(--icon-finger-print); }
.icon-fire:before { content: var(--icon-fire); }
.icon-firefox:before { content: var(--icon-firefox); }
.icon-flag:before { content: var(--icon-flag); }
.icon-flutter:before { content: var(--icon-flutter); }
.icon-folder-add:before { content: var(--icon-folder-add); }
.icon-folder-download:before { content: var(--icon-folder-download); }
.icon-folder-open:before { content: var(--icon-folder-open); }
.icon-folder-remove:before { content: var(--icon-folder-remove); }
.icon-folder:before { content: var(--icon-folder); }
.icon-gift:before { content: var(--icon-gift); }
.icon-git-branch:before { content: var(--icon-git-branch); }
.icon-git-commit:before { content: var(--icon-git-commit); }
.icon-git:before { content: var(--icon-git); }
.icon-github:before { content: var(--icon-github); }
.icon-gitlab:before { content: var(--icon-gitlab); }
.icon-globe-alt:before { content: var(--icon-globe-alt); }
.icon-globe:before { content: var(--icon-globe); }
.icon-go:before { content: var(--icon-go); }
.icon-google:before { content: var(--icon-google); }
.icon-graphql:before { content: var(--icon-graphql); }
.icon-hand:before { content: var(--icon-hand); }
.icon-hashtag:before { content: var(--icon-hashtag); }
.icon-heart:before { content: var(--icon-heart); }
.icon-home:before { content: var(--icon-home); }
.icon-html5:before { content: var(--icon-html5); }
.icon-identification:before { content: var(--icon-identification); }
.icon-inbox-in:before { content: var(--icon-inbox-in); }
.icon-inbox:before { content: var(--icon-inbox); }
.icon-info:before { content: var(--icon-info); }
.icon-instagram:before { content: var(--icon-instagram); }
.icon-ionic:before { content: var(--icon-ionic); }
.icon-ios:before { content: var(--icon-ios); }
.icon-java:before { content: var(--icon-java); }
.icon-js:before { content: var(--icon-js); }
.icon-key:before { content: var(--icon-key); }
.icon-kotlin:before { content: var(--icon-kotlin); }
.icon-light-bulb:before { content: var(--icon-light-bulb); }
.icon-lightning-bolt:before { content: var(--icon-lightning-bolt); }
.icon-link:before { content: var(--icon-link); }
.icon-linkedin:before { content: var(--icon-linkedin); }
.icon-linux:before { content: var(--icon-linux); }
.icon-list:before { content: var(--icon-list); }
.icon-location-marker:before { content: var(--icon-location-marker); }
.icon-lock-closed:before { content: var(--icon-lock-closed); }
.icon-lock-open:before { content: var(--icon-lock-open); }
.icon-logout-left:before { content: var(--icon-logout-left); }
.icon-logout-right:before { content: var(--icon-logout-right); }
.icon-mail-open:before { content: var(--icon-mail-open); }
.icon-mail:before { content: var(--icon-mail); }
.icon-map:before { content: var(--icon-map); }
.icon-md-library:before { content: var(--icon-md-library); }
.icon-medium:before { content: var(--icon-medium); }
.icon-meilisearch:before { content: var(--icon-meilisearch); }
.icon-menu-alt-1:before { content: var(--icon-menu-alt-1); }
.icon-menu-alt-2:before { content: var(--icon-menu-alt-2); }
.icon-menu-alt-3:before { content: var(--icon-menu-alt-3); }
.icon-menu-alt-4:before { content: var(--icon-menu-alt-4); }
.icon-menu:before { content: var(--icon-menu); }
.icon-microphone:before { content: var(--icon-microphone); }
.icon-microsoft:before { content: var(--icon-microsoft); }
.icon-microsoft_edge:before { content: var(--icon-microsoft_edge); }
.icon-minus-circle:before { content: var(--icon-minus-circle); }
.icon-minus-sm:before { content: var(--icon-minus-sm); }
.icon-minus:before { content: var(--icon-minus); }
.icon-mode:before { content: var(--icon-mode); }
.icon-mongodb:before { content: var(--icon-mongodb); }
.icon-moon:before { content: var(--icon-moon); }
.icon-ms_yammer:before { content: var(--icon-ms_yammer); }
.icon-msg91:before { content: var(--icon-msg91); }
.icon-music-note:before { content: var(--icon-music-note); }
.icon-neo4j:before { content: var(--icon-neo4j); }
.icon-neon:before { content: var(--icon-neon); }
.icon-newspaper:before { content: var(--icon-newspaper); }
.icon-nextjs:before { content: var(--icon-nextjs); }
.icon-node_js:before { content: var(--icon-node_js); }
.icon-notion:before { content: var(--icon-notion); }
.icon-null:before { content: var(--icon-null); }
.icon-nuxt:before { content: var(--icon-nuxt); }
.icon-office-building:before { content: var(--icon-office-building); }
.icon-okta:before { content: var(--icon-okta); }
.icon-open-ai:before { content: var(--icon-open-ai); }
.icon-openid:before { content: var(--icon-openid); }
.icon-opera:before { content: var(--icon-opera); }
.icon-pangea:before { content: var(--icon-pangea); }
.icon-paper-airplane:before { content: var(--icon-paper-airplane); }
.icon-paper-clip:before { content: var(--icon-paper-clip); }
.icon-pause:before { content: var(--icon-pause); }
.icon-paypal:before { content: var(--icon-paypal); }
.icon-pencil-alt:before { content: var(--icon-pencil-alt); }
.icon-pencil:before { content: var(--icon-pencil); }
.icon-perspective-api:before { content: var(--icon-perspective-api); }
.icon-phone-incoming:before { content: var(--icon-phone-incoming); }
.icon-phone-missed-call:before { content: var(--icon-phone-missed-call); }
.icon-phone-outgoing:before { content: var(--icon-phone-outgoing); }
.icon-phone:before { content: var(--icon-phone); }
.icon-photograph:before { content: var(--icon-photograph); }
.icon-php:before { content: var(--icon-php); }
.icon-pinterest:before { content: var(--icon-pinterest); }
.icon-play-button:before { content: var(--icon-play-button); }
.icon-play:before { content: var(--icon-play); }
.icon-plus-circle:before { content: var(--icon-plus-circle); }
.icon-plus-sm:before { content: var(--icon-plus-sm); }
.icon-plus:before { content: var(--icon-plus); }
.icon-podio:before { content: var(--icon-podio); }
.icon-presentation-chart-1:before { content: var(--icon-presentation-chart-1); }
.icon-presentation-chart-2:before { content: var(--icon-presentation-chart-2); }
.icon-printer:before { content: var(--icon-printer); }
.icon-product_hunt:before { content: var(--icon-product_hunt); }
.icon-puzzle:before { content: var(--icon-puzzle); }
.icon-python:before { content: var(--icon-python); }
.icon-qrcode:before { content: var(--icon-qrcode); }
.icon-question-mark-circle:before { content: var(--icon-question-mark-circle); }
.icon-qwik:before { content: var(--icon-qwik); }
.icon-react-native:before { content: var(--icon-react-native); }
.icon-react:before { content: var(--icon-react); }
.icon-receipt-refund:before { content: var(--icon-receipt-refund); }
.icon-receipt-tax:before { content: var(--icon-receipt-tax); }
.icon-reddit:before { content: var(--icon-reddit); }
.icon-redis:before { content: var(--icon-redis); }
.icon-refresh:before { content: var(--icon-refresh); }
.icon-relation:before { content: var(--icon-relation); }
.icon-relationship:before { content: var(--icon-relationship); }
.icon-replay:before { content: var(--icon-replay); }
.icon-rewind:before { content: var(--icon-rewind); }
.icon-rss:before { content: var(--icon-rss); }
.icon-ruby:before { content: var(--icon-ruby); }
.icon-safari:before { content: var(--icon-safari); }
.icon-salesforce:before { content: var(--icon-salesforce); }
.icon-save-as:before { content: var(--icon-save-as); }
.icon-save:before { content: var(--icon-save); }
.icon-scale:before { content: var(--icon-scale); }
.icon-scissors:before { content: var(--icon-scissors); }
.icon-search-circle:before { content: var(--icon-search-circle); }
.icon-search:before { content: var(--icon-search); }
.icon-selector:before { content: var(--icon-selector); }
.icon-send:before { content: var(--icon-send); }
.icon-server:before { content: var(--icon-server); }
.icon-share:before { content: var(--icon-share); }
.icon-shield-check:before { content: var(--icon-shield-check); }
.icon-shield-exclamation:before { content: var(--icon-shield-exclamation); }
.icon-shopping-bag:before { content: var(--icon-shopping-bag); }
.icon-shopping-cart:before { content: var(--icon-shopping-cart); }
.icon-skype:before { content: var(--icon-skype); }
.icon-slack:before { content: var(--icon-slack); }
.icon-solidjs:before { content: var(--icon-solidjs); }
.icon-sort-ascending:before { content: var(--icon-sort-ascending); }
.icon-sort-descending:before { content: var(--icon-sort-descending); }
.icon-sparkles:before { content: var(--icon-sparkles); }
.icon-speakerphone:before { content: var(--icon-speakerphone); }
.icon-spin:before { content: var(--icon-spin); }
.icon-spotify:before { content: var(--icon-spotify); }
.icon-star:before { content: var(--icon-star); }
.icon-status-offline:before { content: var(--icon-status-offline); }
.icon-status-online:before { content: var(--icon-status-online); }
.icon-stop:before { content: var(--icon-stop); }
.icon-stripe:before { content: var(--icon-stripe); }
.icon-sun:before { content: var(--icon-sun); }
.icon-support:before { content: var(--icon-support); }
.icon-svelte:before { content: var(--icon-svelte); }
.icon-swift:before { content: var(--icon-swift); }
.icon-switch-horizontal:before { content: var(--icon-switch-horizontal); }
.icon-switch-vertical:before { content: var(--icon-switch-vertical); }
.icon-table:before { content: var(--icon-table); }
.icon-tag:before { content: var(--icon-tag); }
.icon-telegram:before { content: var(--icon-telegram); }
.icon-telesign:before { content: var(--icon-telesign); }
.icon-template:before { content: var(--icon-template); }
.icon-terminal:before { content: var(--icon-terminal); }
.icon-text:before { content: var(--icon-text); }
.icon-textmagic:before { content: var(--icon-textmagic); }
.icon-thumb-dowm:before { content: var(--icon-thumb-dowm); }
.icon-thumb-up:before { content: var(--icon-thumb-up); }
.icon-ticket:before { content: var(--icon-ticket); }
.icon-tiktok:before { content: var(--icon-tiktok); }
.icon-toggle:before { content: var(--icon-toggle); }
.icon-tradeshift:before { content: var(--icon-tradeshift); }
.icon-translate:before { content: var(--icon-translate); }
.icon-trash:before { content: var(--icon-trash); }
.icon-trending-down:before { content: var(--icon-trending-down); }
.icon-trending-up:before { content: var(--icon-trending-up); }
.icon-truck:before { content: var(--icon-truck); }
.icon-tumbir:before { content: var(--icon-tumbir); }
.icon-twilio:before { content: var(--icon-twilio); }
.icon-twitch:before { content: var(--icon-twitch); }
.icon-twitter:before { content: var(--icon-twitter); }
.icon-typescript:before { content: var(--icon-typescript); }
.icon-unity:before { content: var(--icon-unity); }
.icon-upload:before { content: var(--icon-upload); }
.icon-upstash:before { content: var(--icon-upstash); }
.icon-user-add:before { content: var(--icon-user-add); }
.icon-user-circle:before { content: var(--icon-user-circle); }
.icon-user-group:before { content: var(--icon-user-group); }
.icon-user-remove:before { content: var(--icon-user-remove); }
.icon-user:before { content: var(--icon-user); }
.icon-users:before { content: var(--icon-users); }
.icon-variable:before { content: var(--icon-variable); }
.icon-video-camera:before { content: var(--icon-video-camera); }
.icon-video:before { content: var(--icon-video); }
.icon-view-boards:before { content: var(--icon-view-boards); }
.icon-view-grid-add:before { content: var(--icon-view-grid-add); }
.icon-view-grid:before { content: var(--icon-view-grid); }
.icon-view-list:before { content: var(--icon-view-list); }
.icon-vimeo:before { content: var(--icon-vimeo); }
.icon-vk:before { content: var(--icon-vk); }
.icon-volume-off:before { content: var(--icon-volume-off); }
.icon-volume-up:before { content: var(--icon-volume-up); }
.icon-vonage:before { content: var(--icon-vonage); }
.icon-vs_code:before { content: var(--icon-vs_code); }
.icon-vue:before { content: var(--icon-vue); }
.icon-whatsapp:before { content: var(--icon-whatsapp); }
.icon-wifi:before { content: var(--icon-wifi); }
.icon-wordpress:before { content: var(--icon-wordpress); }
.icon-x-circle:before { content: var(--icon-x-circle); }
.icon-x:before { content: var(--icon-x); }
.icon-yahoo:before { content: var(--icon-yahoo); }
.icon-yandex:before { content: var(--icon-yandex); }
.icon-ycombinator:before { content: var(--icon-ycombinator); }
.icon-youtube:before { content: var(--icon-youtube); }
.icon-zoom-in:before { content: var(--icon-zoom-in); }
.icon-zoom-out:before { content: var(--icon-zoom-out); }
.icon-zoom:before { content: var(--icon-zoom); }

