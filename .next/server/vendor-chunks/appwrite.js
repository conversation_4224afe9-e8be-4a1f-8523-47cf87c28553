"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/appwrite";
exports.ids = ["vendor-chunks/appwrite"];
exports.modules = {

/***/ "(ssr)/./node_modules/appwrite/dist/esm/sdk.js":
/*!***********************************************!*\
  !*** ./node_modules/appwrite/dist/esm/sdk.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Account: () => (/* binding */ Account),\n/* harmony export */   AppwriteException: () => (/* binding */ AppwriteException),\n/* harmony export */   AuthenticationFactor: () => (/* binding */ AuthenticationFactor),\n/* harmony export */   AuthenticatorType: () => (/* binding */ AuthenticatorType),\n/* harmony export */   Avatars: () => (/* binding */ Avatars),\n/* harmony export */   Browser: () => (/* binding */ Browser),\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CreditCard: () => (/* binding */ CreditCard),\n/* harmony export */   Databases: () => (/* binding */ Databases),\n/* harmony export */   ExecutionMethod: () => (/* binding */ ExecutionMethod),\n/* harmony export */   Flag: () => (/* binding */ Flag),\n/* harmony export */   Functions: () => (/* binding */ Functions),\n/* harmony export */   Graphql: () => (/* binding */ Graphql),\n/* harmony export */   ID: () => (/* binding */ ID),\n/* harmony export */   ImageFormat: () => (/* binding */ ImageFormat),\n/* harmony export */   ImageGravity: () => (/* binding */ ImageGravity),\n/* harmony export */   Locale: () => (/* binding */ Locale),\n/* harmony export */   Messaging: () => (/* binding */ Messaging),\n/* harmony export */   OAuthProvider: () => (/* binding */ OAuthProvider),\n/* harmony export */   Permission: () => (/* binding */ Permission),\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   Role: () => (/* binding */ Role),\n/* harmony export */   Storage: () => (/* binding */ Storage),\n/* harmony export */   Teams: () => (/* binding */ Teams)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\n\n/**\n * Helper class to generate query strings.\n */\nclass Query {\n    /**\n     * Constructor for Query class.\n     *\n     * @param {string} method\n     * @param {AttributesTypes} attribute\n     * @param {QueryTypes} values\n     */\n    constructor(method, attribute, values) {\n        this.method = method;\n        this.attribute = attribute;\n        if (values !== undefined) {\n            if (Array.isArray(values)) {\n                this.values = values;\n            }\n            else {\n                this.values = [values];\n            }\n        }\n    }\n    /**\n     * Convert the query object to a JSON string.\n     *\n     * @returns {string}\n     */\n    toString() {\n        return JSON.stringify({\n            method: this.method,\n            attribute: this.attribute,\n            values: this.values,\n        });\n    }\n}\n/**\n * Filter resources where attribute is equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.equal = (attribute, value) => new Query(\"equal\", attribute, value).toString();\n/**\n * Filter resources where attribute is not equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.notEqual = (attribute, value) => new Query(\"notEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is less than value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.lessThan = (attribute, value) => new Query(\"lessThan\", attribute, value).toString();\n/**\n * Filter resources where attribute is less than or equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.lessThanEqual = (attribute, value) => new Query(\"lessThanEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is greater than value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.greaterThan = (attribute, value) => new Query(\"greaterThan\", attribute, value).toString();\n/**\n * Filter resources where attribute is greater than or equal to value.\n *\n * @param {string} attribute\n * @param {QueryTypes} value\n * @returns {string}\n */\nQuery.greaterThanEqual = (attribute, value) => new Query(\"greaterThanEqual\", attribute, value).toString();\n/**\n * Filter resources where attribute is null.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.isNull = (attribute) => new Query(\"isNull\", attribute).toString();\n/**\n * Filter resources where attribute is not null.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.isNotNull = (attribute) => new Query(\"isNotNull\", attribute).toString();\n/**\n * Filter resources where attribute is between start and end (inclusive).\n *\n * @param {string} attribute\n * @param {string | number} start\n * @param {string | number} end\n * @returns {string}\n */\nQuery.between = (attribute, start, end) => new Query(\"between\", attribute, [start, end]).toString();\n/**\n * Filter resources where attribute starts with value.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.startsWith = (attribute, value) => new Query(\"startsWith\", attribute, value).toString();\n/**\n * Filter resources where attribute ends with value.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.endsWith = (attribute, value) => new Query(\"endsWith\", attribute, value).toString();\n/**\n * Specify which attributes should be returned by the API call.\n *\n * @param {string[]} attributes\n * @returns {string}\n */\nQuery.select = (attributes) => new Query(\"select\", undefined, attributes).toString();\n/**\n * Filter resources by searching attribute for value.\n * A fulltext index on attribute is required for this query to work.\n *\n * @param {string} attribute\n * @param {string} value\n * @returns {string}\n */\nQuery.search = (attribute, value) => new Query(\"search\", attribute, value).toString();\n/**\n * Sort results by attribute descending.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.orderDesc = (attribute) => new Query(\"orderDesc\", attribute).toString();\n/**\n * Sort results by attribute ascending.\n *\n * @param {string} attribute\n * @returns {string}\n */\nQuery.orderAsc = (attribute) => new Query(\"orderAsc\", attribute).toString();\n/**\n * Return results after documentId.\n *\n * @param {string} documentId\n * @returns {string}\n */\nQuery.cursorAfter = (documentId) => new Query(\"cursorAfter\", undefined, documentId).toString();\n/**\n * Return results before documentId.\n *\n * @param {string} documentId\n * @returns {string}\n */\nQuery.cursorBefore = (documentId) => new Query(\"cursorBefore\", undefined, documentId).toString();\n/**\n * Return only limit results.\n *\n * @param {number} limit\n * @returns {string}\n */\nQuery.limit = (limit) => new Query(\"limit\", undefined, limit).toString();\n/**\n * Filter resources by skipping the first offset results.\n *\n * @param {number} offset\n * @returns {string}\n */\nQuery.offset = (offset) => new Query(\"offset\", undefined, offset).toString();\n/**\n * Filter resources where attribute contains the specified value.\n *\n * @param {string} attribute\n * @param {string | string[]} value\n * @returns {string}\n */\nQuery.contains = (attribute, value) => new Query(\"contains\", attribute, value).toString();\n/**\n * Combine multiple queries using logical OR operator.\n *\n * @param {string[]} queries\n * @returns {string}\n */\nQuery.or = (queries) => new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n/**\n * Combine multiple queries using logical AND operator.\n *\n * @param {string[]} queries\n * @returns {string}\n */\nQuery.and = (queries) => new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n/**\n * Exception thrown by the  package\n */\nclass AppwriteException extends Error {\n    /**\n     * Initializes a Appwrite Exception.\n     *\n     * @param {string} message - The error message.\n     * @param {number} code - The error code. Default is 0.\n     * @param {string} type - The error type. Default is an empty string.\n     * @param {string} response - The response string. Default is an empty string.\n     */\n    constructor(message, code = 0, type = '', response = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n/**\n * Client that handles requests to Appwrite\n */\nclass Client {\n    constructor() {\n        /**\n         * Holds configuration such as project.\n         */\n        this.config = {\n            endpoint: 'https://cloud.appwrite.io/v1',\n            endpointRealtime: '',\n            project: '',\n            jwt: '',\n            locale: '',\n            session: '',\n            devkey: '',\n        };\n        /**\n         * Custom headers for API requests.\n         */\n        this.headers = {\n            'x-sdk-name': 'Web',\n            'x-sdk-platform': 'client',\n            'x-sdk-language': 'web',\n            'x-sdk-version': '18.1.1',\n            'X-Appwrite-Response-Format': '1.7.0',\n        };\n        this.realtime = {\n            socket: undefined,\n            timeout: undefined,\n            heartbeat: undefined,\n            url: '',\n            channels: new Set(),\n            subscriptions: new Map(),\n            subscriptionsCounter: 0,\n            reconnect: true,\n            reconnectAttempts: 0,\n            lastMessage: undefined,\n            connect: () => {\n                clearTimeout(this.realtime.timeout);\n                this.realtime.timeout = window === null || window === void 0 ? void 0 : window.setTimeout(() => {\n                    this.realtime.createSocket();\n                }, 50);\n            },\n            getTimeout: () => {\n                switch (true) {\n                    case this.realtime.reconnectAttempts < 5:\n                        return 1000;\n                    case this.realtime.reconnectAttempts < 15:\n                        return 5000;\n                    case this.realtime.reconnectAttempts < 100:\n                        return 10000;\n                    default:\n                        return 60000;\n                }\n            },\n            createHeartbeat: () => {\n                if (this.realtime.heartbeat) {\n                    clearTimeout(this.realtime.heartbeat);\n                }\n                this.realtime.heartbeat = window === null || window === void 0 ? void 0 : window.setInterval(() => {\n                    var _a;\n                    (_a = this.realtime.socket) === null || _a === void 0 ? void 0 : _a.send(JSON.stringify({\n                        type: 'ping'\n                    }));\n                }, 20000);\n            },\n            createSocket: () => {\n                var _a, _b, _c;\n                if (this.realtime.channels.size < 1) {\n                    this.realtime.reconnect = false;\n                    (_a = this.realtime.socket) === null || _a === void 0 ? void 0 : _a.close();\n                    return;\n                }\n                const channels = new URLSearchParams();\n                channels.set('project', this.config.project);\n                this.realtime.channels.forEach(channel => {\n                    channels.append('channels[]', channel);\n                });\n                const url = this.config.endpointRealtime + '/realtime?' + channels.toString();\n                if (url !== this.realtime.url || // Check if URL is present\n                    !this.realtime.socket || // Check if WebSocket has not been created\n                    ((_b = this.realtime.socket) === null || _b === void 0 ? void 0 : _b.readyState) > WebSocket.OPEN // Check if WebSocket is CLOSING (3) or CLOSED (4)\n                ) {\n                    if (this.realtime.socket &&\n                        ((_c = this.realtime.socket) === null || _c === void 0 ? void 0 : _c.readyState) < WebSocket.CLOSING // Close WebSocket if it is CONNECTING (0) or OPEN (1)\n                    ) {\n                        this.realtime.reconnect = false;\n                        this.realtime.socket.close();\n                    }\n                    this.realtime.url = url;\n                    this.realtime.socket = new WebSocket(url);\n                    this.realtime.socket.addEventListener('message', this.realtime.onMessage);\n                    this.realtime.socket.addEventListener('open', _event => {\n                        this.realtime.reconnectAttempts = 0;\n                        this.realtime.createHeartbeat();\n                    });\n                    this.realtime.socket.addEventListener('close', event => {\n                        var _a, _b, _c;\n                        if (!this.realtime.reconnect ||\n                            (((_b = (_a = this.realtime) === null || _a === void 0 ? void 0 : _a.lastMessage) === null || _b === void 0 ? void 0 : _b.type) === 'error' && // Check if last message was of type error\n                                ((_c = this.realtime) === null || _c === void 0 ? void 0 : _c.lastMessage.data).code === 1008 // Check for policy violation 1008\n                            )) {\n                            this.realtime.reconnect = true;\n                            return;\n                        }\n                        const timeout = this.realtime.getTimeout();\n                        console.error(`Realtime got disconnected. Reconnect will be attempted in ${timeout / 1000} seconds.`, event.reason);\n                        setTimeout(() => {\n                            this.realtime.reconnectAttempts++;\n                            this.realtime.createSocket();\n                        }, timeout);\n                    });\n                }\n            },\n            onMessage: (event) => {\n                var _a, _b;\n                try {\n                    const message = JSON.parse(event.data);\n                    this.realtime.lastMessage = message;\n                    switch (message.type) {\n                        case 'connected':\n                            const cookie = JSON.parse((_a = window.localStorage.getItem('cookieFallback')) !== null && _a !== void 0 ? _a : '{}');\n                            const session = cookie === null || cookie === void 0 ? void 0 : cookie[`a_session_${this.config.project}`];\n                            const messageData = message.data;\n                            if (session && !messageData.user) {\n                                (_b = this.realtime.socket) === null || _b === void 0 ? void 0 : _b.send(JSON.stringify({\n                                    type: 'authentication',\n                                    data: {\n                                        session\n                                    }\n                                }));\n                            }\n                            break;\n                        case 'event':\n                            let data = message.data;\n                            if (data === null || data === void 0 ? void 0 : data.channels) {\n                                const isSubscribed = data.channels.some(channel => this.realtime.channels.has(channel));\n                                if (!isSubscribed)\n                                    return;\n                                this.realtime.subscriptions.forEach(subscription => {\n                                    if (data.channels.some(channel => subscription.channels.includes(channel))) {\n                                        setTimeout(() => subscription.callback(data));\n                                    }\n                                });\n                            }\n                            break;\n                        case 'pong':\n                            break; // Handle pong response if needed\n                        case 'error':\n                            throw message.data;\n                        default:\n                            break;\n                    }\n                }\n                catch (e) {\n                    console.error(e);\n                }\n            },\n            cleanUp: channels => {\n                this.realtime.channels.forEach(channel => {\n                    if (channels.includes(channel)) {\n                        let found = Array.from(this.realtime.subscriptions).some(([_key, subscription]) => {\n                            return subscription.channels.includes(channel);\n                        });\n                        if (!found) {\n                            this.realtime.channels.delete(channel);\n                        }\n                    }\n                });\n            }\n        };\n    }\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint) {\n        if (!endpoint.startsWith('http://') && !endpoint.startsWith('https://')) {\n            throw new AppwriteException('Invalid endpoint URL: ' + endpoint);\n        }\n        this.config.endpoint = endpoint;\n        this.config.endpointRealtime = endpoint.replace('https://', 'wss://').replace('http://', 'ws://');\n        return this;\n    }\n    /**\n     * Set Realtime Endpoint\n     *\n     * @param {string} endpointRealtime\n     *\n     * @returns {this}\n     */\n    setEndpointRealtime(endpointRealtime) {\n        if (!endpointRealtime.startsWith('ws://') && !endpointRealtime.startsWith('wss://')) {\n            throw new AppwriteException('Invalid realtime endpoint URL: ' + endpointRealtime);\n        }\n        this.config.endpointRealtime = endpointRealtime;\n        return this;\n    }\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value) {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value) {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value) {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value) {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n    /**\n     * Set DevKey\n     *\n     * Your secret dev API key\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setDevKey(value) {\n        this.headers['X-Appwrite-Dev-Key'] = value;\n        this.config.devkey = value;\n        return this;\n    }\n    /**\n     * Subscribes to Appwrite events and passes you the payload in realtime.\n     *\n     * @param {string|string[]} channels\n     * Channel to subscribe - pass a single channel as a string or multiple with an array of strings.\n     *\n     * Possible channels are:\n     * - account\n     * - collections\n     * - collections.[ID]\n     * - collections.[ID].documents\n     * - documents\n     * - documents.[ID]\n     * - files\n     * - files.[ID]\n     * - executions\n     * - executions.[ID]\n     * - functions.[ID]\n     * - teams\n     * - teams.[ID]\n     * - memberships\n     * - memberships.[ID]\n     * @param {(payload: RealtimeMessage) => void} callback Is called on every realtime update.\n     * @returns {() => void} Unsubscribes from events.\n     */\n    subscribe(channels, callback) {\n        let channelArray = typeof channels === 'string' ? [channels] : channels;\n        channelArray.forEach(channel => this.realtime.channels.add(channel));\n        const counter = this.realtime.subscriptionsCounter++;\n        this.realtime.subscriptions.set(counter, {\n            channels: channelArray,\n            callback\n        });\n        this.realtime.connect();\n        return () => {\n            this.realtime.subscriptions.delete(counter);\n            this.realtime.cleanUp(channelArray);\n            this.realtime.connect();\n        };\n    }\n    prepareRequest(method, url, headers = {}, params = {}) {\n        method = method.toUpperCase();\n        headers = Object.assign({}, this.headers, headers);\n        if (typeof window !== 'undefined' && window.localStorage) {\n            const cookieFallback = window.localStorage.getItem('cookieFallback');\n            if (cookieFallback) {\n                headers['X-Fallback-Cookies'] = cookieFallback;\n            }\n        }\n        let options = {\n            method,\n            headers,\n        };\n        if (headers['X-Appwrite-Dev-Key'] === undefined) {\n            options.credentials = 'include';\n        }\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Client.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        }\n        else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n                case 'multipart/form-data':\n                    const formData = new FormData();\n                    for (const [key, value] of Object.entries(params)) {\n                        if (value instanceof File) {\n                            formData.append(key, value, value.name);\n                        }\n                        else if (Array.isArray(value)) {\n                            for (const nestedValue of value) {\n                                formData.append(`${key}[]`, nestedValue);\n                            }\n                        }\n                        else {\n                            formData.append(key, value);\n                        }\n                    }\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n        return { uri: url.toString(), options };\n    }\n    chunkedUpload(method, url, headers = {}, originalPayload = {}, onProgress) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const file = Object.values(originalPayload).find((value) => value instanceof File);\n            if (!file) {\n                throw new Error('File not found in payload');\n            }\n            if (file.size <= Client.CHUNK_SIZE) {\n                return yield this.call(method, url, headers, originalPayload);\n            }\n            let start = 0;\n            let response = null;\n            while (start < file.size) {\n                let end = start + Client.CHUNK_SIZE; // Prepare end for the next chunk\n                if (end >= file.size) {\n                    end = file.size; // Adjust for the last chunk to include the last byte\n                }\n                headers['content-range'] = `bytes ${start}-${end - 1}/${file.size}`;\n                const chunk = file.slice(start, end);\n                let payload = Object.assign(Object.assign({}, originalPayload), { file: new File([chunk], file.name) });\n                response = yield this.call(method, url, headers, payload);\n                if (onProgress && typeof onProgress === 'function') {\n                    onProgress({\n                        $id: response.$id,\n                        progress: Math.round((end / file.size) * 100),\n                        sizeUploaded: end,\n                        chunksTotal: Math.ceil(file.size / Client.CHUNK_SIZE),\n                        chunksUploaded: Math.ceil(end / Client.CHUNK_SIZE)\n                    });\n                }\n                if (response && response.$id) {\n                    headers['x-appwrite-id'] = response.$id;\n                }\n                start = end;\n            }\n            return response;\n        });\n    }\n    ping() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.call('GET', new URL(this.config.endpoint + '/ping'));\n        });\n    }\n    call(method, url, headers = {}, params = {}, responseType = 'json') {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            const { uri, options } = this.prepareRequest(method, url, headers, params);\n            let data = null;\n            const response = yield fetch(uri, options);\n            // type opaque: No-CORS, different-origin response (CORS-issue)\n            if (response.type === 'opaque') {\n                throw new AppwriteException(`Invalid Origin. Register your new client (${window.location.host}) as a new Web platform on your project console dashboard`, 403, \"forbidden\", \"\");\n            }\n            const warnings = response.headers.get('x-appwrite-warning');\n            if (warnings) {\n                warnings.split(';').forEach((warning) => console.warn('Warning: ' + warning));\n            }\n            if ((_a = response.headers.get('content-type')) === null || _a === void 0 ? void 0 : _a.includes('application/json')) {\n                data = yield response.json();\n            }\n            else if (responseType === 'arrayBuffer') {\n                data = yield response.arrayBuffer();\n            }\n            else {\n                data = {\n                    message: yield response.text()\n                };\n            }\n            if (400 <= response.status) {\n                let responseText = '';\n                if (((_b = response.headers.get('content-type')) === null || _b === void 0 ? void 0 : _b.includes('application/json')) || responseType === 'arrayBuffer') {\n                    responseText = JSON.stringify(data);\n                }\n                else {\n                    responseText = data === null || data === void 0 ? void 0 : data.message;\n                }\n                throw new AppwriteException(data === null || data === void 0 ? void 0 : data.message, response.status, data === null || data === void 0 ? void 0 : data.type, responseText);\n            }\n            const cookieFallback = response.headers.get('X-Fallback-Cookies');\n            if (typeof window !== 'undefined' && window.localStorage && cookieFallback) {\n                window.console.warn('Appwrite is using localStorage for session management. Increase your security by adding a custom domain as your API endpoint.');\n                window.localStorage.setItem('cookieFallback', cookieFallback);\n            }\n            return data;\n        });\n    }\n    static flatten(data, prefix = '') {\n        let output = {};\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key + ']' : key;\n            if (Array.isArray(value)) {\n                output = Object.assign(Object.assign({}, output), Client.flatten(value, finalKey));\n            }\n            else {\n                output[finalKey] = value;\n            }\n        }\n        return output;\n    }\n}\nClient.CHUNK_SIZE = 1024 * 1024 * 5;\n\nclass Service {\n    constructor(client) {\n        this.client = client;\n    }\n    static flatten(data, prefix = '') {\n        let output = {};\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key + ']' : key;\n            if (Array.isArray(value)) {\n                output = Object.assign(Object.assign({}, output), Service.flatten(value, finalKey));\n            }\n            else {\n                output[finalKey] = value;\n            }\n        }\n        return output;\n    }\n}\n/**\n * The size for chunked uploads in bytes.\n */\nService.CHUNK_SIZE = 5 * 1024 * 1024; // 5MB\n\nclass Account {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    get() {\n        const apiPath = '/account';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a new user to register a new account in your project. After the user registration completes successfully, you can use the [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification) route to start verifying the user email address. To allow the new user to login to their new account, you need to create a new [account session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    create(userId, email, password, name) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account email address. After changing user address, the user confirmation status will get reset. A new confirmation email is not sent automatically however you can use the send confirmation email endpoint again to send the confirmation email. For security measures, user password is required to complete this request.\nThis endpoint can also be used to convert an anonymous account to a normal one, by passing an email address and a new password.\n\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateEmail(email, password) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/email';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IdentityList>}\n     */\n    listIdentities(queries) {\n        const apiPath = '/account/identities';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteIdentity(identityId) {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT to authenticate on behalf of the current user when working with the Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes from its creation and will be invalid if the user will logout in that time frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Jwt>}\n     */\n    createJWT() {\n        const apiPath = '/account/jwts';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of latest security activity logs for the currently logged in user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    listLogs(queries) {\n        const apiPath = '/account/logs';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMFA(mfa) {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n        const apiPath = '/account/mfa';\n        const payload = {};\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Add an authenticator app to be used as an MFA factor. Verify the authenticator using the [verify authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaType>}\n     */\n    createMfaAuthenticator(type) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Verify an authenticator app after adding it using the [add authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateMfaAuthenticator(type, otp) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMfaAuthenticator(type) {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Begin the process of MFA verification after sign-in. Finish the flow with [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge) method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaChallenge>}\n     */\n    createMfaChallenge(factor) {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload = {};\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Complete the MFA challenge by providing the one-time password. Finish the process of MFA verification by providing the one-time password. To begin the flow, use [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMfaChallenge(challengeId, otp) {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload = {};\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaFactors>}\n     */\n    listMfaFactors() {\n        const apiPath = '/account/mfa/factors';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Get recovery codes that can be used as backup for MFA flow. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    getMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Generate recovery codes as backup for MFA flow. It&#039;s recommended to generate and show then immediately after user successfully adds their authehticator. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    createMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    updateMfaRecoveryCodes() {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateName(name) {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/account/name';\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user password. For validation, user is required to pass in the new password, and the old password. For users created with OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePassword(password, oldPassword) {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/password';\n        const payload = {};\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the currently logged in user&#039;s phone number. After updating the phone number, the phone verification status will be reset. A confirmation SMS is not sent automatically, however you can use the [POST /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification) endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePhone(phone, password) {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/phone';\n        const payload = {};\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs() {\n        const apiPath = '/account/prefs';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update currently logged in user account preferences. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updatePrefs(prefs) {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/account/prefs';\n        const payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a temporary secret key for password reset. When the user clicks the confirmation link he is redirected back to your app password reset URL with the secret key and email address values attached to the URL query string. Use the query string params to submit a request to the [PUT /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery) endpoint to complete the process. The verification link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createRecovery(email, url) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user account password reset. Both the **userId** and **secret** arguments will be passed as query parameters to the redirect URL you have provided when sending your request to the [POST /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery) endpoint.\n\nPlease note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateRecovery(userId, secret, password) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the list of active sessions across different devices for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SessionList>}\n     */\n    listSessions() {\n        const apiPath = '/account/sessions';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete all sessions from the user account and remove any sessions cookies from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSessions() {\n        const apiPath = '/account/sessions';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a new user to register an anonymous account in your project. This route will also create a new session for the user. To allow the new user to convert an anonymous account to a normal account, you need to update its [email and password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail) or create an [OAuth2 session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createAnonymousSession() {\n        const apiPath = '/account/sessions/anonymous';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login into their account by providing a valid email and password combination. This route will create a new session for the user.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createEmailPasswordSession(email, password) {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/sessions/email';\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateMagicURLSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/magic-url';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n\nIf there is already an active session, the new session will be attached to the logged-in account. If there are no active sessions, the server will attempt to look for a user with the same email address as the email received from the OAuth2 provider and attach the new session to the existing user. If no matching user is found - the server will create a new user.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Session(provider, success, failure, scopes) {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/sessions/oauth2/{provider}'.replace('{provider}', provider);\n        const payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && (window === null || window === void 0 ? void 0 : window.location)) {\n            window.location.href = uri.toString();\n            return;\n        }\n        else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updatePhoneSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    createSession(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/token';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to get a logged in user&#039;s session using a Session ID. Inputting &#039;current&#039; will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    getSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to extend a session&#039;s length. Extending a session is useful when session expiry is short. If the session was created using an OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    updateSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Logout the user. Use &#039;current&#039; as the session ID to logout on this device, use a session ID to logout on another device. If you&#039;re looking to logout the user on all devices, use [Delete Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions) instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSession(sessionId) {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Block the currently logged in user account. Behind the scene, the user record is not deleted but permanently blocked from any access. To completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    updateStatus() {\n        const apiPath = '/account/status';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to register a device for push notifications. Provide a target ID (custom or generated using ID.unique()), a device identifier (usually a device token), and optionally specify which provider should send notifications to this target. The target is automatically linked to the current session and includes device information like brand and model.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    createPushTarget(targetId, identifier, providerId) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/push';\n        const payload = {};\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the currently logged in user&#039;s push notification target. You can modify the target&#039;s identifier (device token) and provider ID (token, email, phone etc.). The target must exist and belong to the current user. If you change the provider ID, notifications will be sent through the new messaging provider instead.\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Target>}\n     */\n    updatePushTarget(targetId, identifier) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload = {};\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a push notification target for the currently logged in user. After deletion, the device will no longer receive push notifications. The target must exist and belong to the current user.\n     *\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deletePushTarget(targetId) {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s email is valid for 15 minutes.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createEmailToken(userId, email, phrase) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/email';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not been registered, a new user will be created. When the user clicks the link in the email, the user is redirected back to the URL you provided with the secret key and userId values attached to the URL query string. Use the query string parameters to submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The link sent to the user&#039;s email address is valid for 1 hour.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createMagicURLToken(userId, email, url, phrase) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/magic-url';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed.\n\nIf authentication succeeds, `userId` and `secret` of a token will be appended to the success URL as query parameters. These can be used to create a new session using the [Create session](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void | string}\n     */\n    createOAuth2Token(provider, success, failure, scopes) {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && (window === null || window === void 0 ? void 0 : window.location)) {\n            window.location.href = uri.toString();\n            return;\n        }\n        else {\n            return uri.toString();\n        }\n    }\n    /**\n     * Sends the user an SMS with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s phone is valid for 15 minutes.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneToken(userId, phone) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        const apiPath = '/account/tokens/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to send a verification message to your user email address to confirm they are the valid owners of that address. Both the **userId** and **secret** arguments will be passed as query parameters to the URL you have provided to be attached to the verification email. The provided URL should redirect the user back to your app and allow you to complete the verification process by verifying both the **userId** and **secret** parameters. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification). The verification link sent to the user&#039;s email address is valid for 7 days.\n\nPlease note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md), the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createVerification(url) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/verification';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user email verification process. Use both the **userId** and **secret** parameters that were attached to your app URL to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updateVerification(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to send a verification SMS to the currently logged in user. This endpoint is meant for use after updating a user&#039;s phone number using the [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone) endpoint. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification). The verification code sent to the user&#039;s phone number is valid for 15 minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    createPhoneVerification() {\n        const apiPath = '/account/verification/phone';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to complete the user phone verification process. Use the **userId** and **secret** that were sent to your user&#039;s phone number to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    updatePhoneVerification(userId, secret) {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification/phone';\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n}\n\nclass Avatars {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * You can use this endpoint to show different browser icons to your users. The code argument receives the browser code as it appears in your user [GET /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions) endpoint. Use width, height and quality arguments to change the output settings.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     * @param {Browser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getBrowser(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * The credit card endpoint will return you the icon of the credit card provider you need. Use width, height and quality arguments to change the output settings.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getCreditCard(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote website URL.\n\nThis endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFavicon(url) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/favicon';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * You can use this endpoint to show different country flags icons to your users. The code argument receives the 2 letter country code. Use width, height and quality arguments to change the output settings. Country codes follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFlag(code, width, height, quality) {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to fetch a remote image URL and crop it to any image size you want. This endpoint is very useful if you need to crop and display remote images in your app or in case you want to make sure a 3rd party image is properly served using a TLS protocol.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 400x400px.\n\nThis endpoint does not follow HTTP redirects.\n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getImage(url, width, height) {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/image';\n        const payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Use this endpoint to show your user initials avatar icon on your website or app. By default, this route will try to print your logged-in user name or email initials. You can also overwrite the user name if you pass the &#039;name&#039; parameter. If no name is given and no user is logged, an empty avatar will be returned.\n\nYou can use the color and background params to change the avatar colors. By default, a random theme will be selected. The random theme will persist for the user&#039;s initials when reloading the same theme will always return for the same initials.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getInitials(name, width, height, background) {\n        const apiPath = '/avatars/initials';\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Converts a given plain text to a QR code image. You can use the query parameters to change the size and style of the resulting image.\n\n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getQR(text, size, margin, download) {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n        const apiPath = '/avatars/qr';\n        const payload = {};\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n}\n\nclass Databases {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DocumentList<Document>>}\n     */\n    listDocuments(databaseId, collectionId, queries) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    createDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload = {};\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    getDocument(databaseId, collectionId, documentId, queries) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create or update a Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {object} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    upsertDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    updateDocument(databaseId, collectionId, documentId, data, permissions) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteDocument(databaseId, collectionId, documentId) {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n}\n\nclass Functions {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the current user function execution logs. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ExecutionList>}\n     */\n    listExecutions(functionId, queries) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Trigger a function execution. The returned object will return you the current execution status. You can ping the `Get Execution` endpoint to get updates on the current execution status. Once this endpoint is called, your function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    createExecution(functionId, body, async, xpath, method, headers, scheduledAt) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload = {};\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    getExecution(functionId, executionId) {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n}\n\nclass Graphql {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    query(query) {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql';\n        const payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    mutation(query) {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql/mutation';\n        const payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n}\n\nclass Locale {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get the current user location based on IP. Returns an object with user country code, country name, continent name, continent code, ip address and suggested currency. You can use the locale header to get the data in a supported language.\n\n([IP Geolocation by DB-IP](https://db-ip.com))\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Locale>}\n     */\n    get() {\n        const apiPath = '/locale';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all locale codes in [ISO 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LocaleCodeList>}\n     */\n    listCodes() {\n        const apiPath = '/locale/codes';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all continents. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ContinentList>}\n     */\n    listContinents() {\n        const apiPath = '/locale/continents';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountries() {\n        const apiPath = '/locale/countries';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries that are currently members of the EU. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CountryList>}\n     */\n    listCountriesEU() {\n        const apiPath = '/locale/countries/eu';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all countries phone codes. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.PhoneList>}\n     */\n    listCountriesPhones() {\n        const apiPath = '/locale/countries/phones';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all currencies, including currency symbol, name, plural, and decimal digits for all major and minor currencies. You can use the locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CurrencyList>}\n     */\n    listCurrencies() {\n        const apiPath = '/locale/currencies';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * List of all languages classified by ISO 639-1 including 2-letter code, name in English, and name in the respective language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LanguageList>}\n     */\n    listLanguages() {\n        const apiPath = '/locale/languages';\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n}\n\nclass Messaging {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    createSubscriber(topicId, subscriberId, targetId) {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload = {};\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteSubscriber(topicId, subscriberId) {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n}\n\nclass Storage {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the user files. You can use the query params to filter your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FileList>}\n     */\n    listFiles(bucketId, queries, search) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.\n\nLarger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.\n\nWhen the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.\n\nIf you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.\n\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    createFile(bucketId, fileId, file, permissions, onProgress = (progress) => { }) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload = {};\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'multipart/form-data',\n        };\n        return this.client.chunkedUpload('post', uri, apiHeaders, payload, onProgress);\n    }\n    /**\n     * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    getFile(bucketId, fileId) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update a file by its unique ID. Only users with write permissions have access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    updateFile(bucketId, fileId, name, permissions) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteFile(bucketId, fileId) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileDownload(bucketId, fileId, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFilePreview(bucketId, fileId, width, height, gravity, quality, borderWidth, borderColor, borderRadius, opacity, rotation, background, output, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n    /**\n     * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} token\n     * @throws {AppwriteException}\n     * @returns {string}\n     */\n    getFileView(bucketId, fileId, token) {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload = {};\n        if (typeof token !== 'undefined') {\n            payload['token'] = token;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri.toString();\n    }\n}\n\nclass Teams {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Get a list of all the teams in which the current user is a member. You can use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TeamList<Preferences>>}\n     */\n    list(queries, search) {\n        const apiPath = '/teams';\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Create a new team. The user who creates the team will automatically be assigned as the owner of the team. Only the users with the owner role can invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    create(teamId, name, roles) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams';\n        const payload = {};\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    get(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the team&#039;s name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    updateName(teamId, name) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n    /**\n     * Delete a team using its ID. Only team members with the owner role can delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    delete(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to list a team&#039;s members using the team&#039;s ID. All team members have read access to this endpoint. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MembershipList>}\n     */\n    listMemberships(teamId, queries, search) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Invite a new member to join your team. Provide an ID for existing users, or invite unregistered users using an email or phone number. If initiated from a Client SDK, Appwrite will send an email or sms with a link to join the team to the invited user, and an account will be created for them if one doesn&#039;t exist. If initiated from a Server SDK, the new member will be added automatically to the team.\n\nYou only need to provide one of a user ID, email, or phone number. Appwrite will prioritize accepting the user ID &gt; email &gt; phone number if you provide more than one of these parameters.\n\nUse the `url` parameter to redirect the user from the invitation email to your app. After the user is redirected, use the [Update Team Membership Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus) endpoint to allow the user to accept the invitation to the team.\n\nPlease note that to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) Appwrite will accept the only redirect URLs under the domains you have added as a platform on the Appwrite Console.\n\n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    createMembership(teamId, roles, email, userId, phone, url, name) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('post', uri, apiHeaders, payload);\n    }\n    /**\n     * Get a team member by the membership unique id. All team members have read access for this resource. Hide sensitive attributes from the response by toggling membership privacy in the Console.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    getMembership(teamId, membershipId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Modify the roles of a team member. Only team members with the owner role have access to this endpoint. Learn more about [roles and permissions](https://appwrite.io/docs/permissions).\n\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembership(teamId, membershipId, roles) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * This endpoint allows a user to leave a team or for a team owner to delete the membership of any other team member. You can also use this endpoint to delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    deleteMembership(teamId, membershipId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('delete', uri, apiHeaders, payload);\n    }\n    /**\n     * Use this endpoint to allow a user to accept an invitation to join a team after being redirected back to your app from the invitation email received by the user.\n\nIf the request is successful, a session for the user is automatically created.\n\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    updateMembershipStatus(teamId, membershipId, userId, secret) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('patch', uri, apiHeaders, payload);\n    }\n    /**\n     * Get the team&#039;s shared preferences by its unique ID. If a preference doesn&#039;t need to be shared by all team members, prefer storing them in [user preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    getPrefs(teamId) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {};\n        return this.client.call('get', uri, apiHeaders, payload);\n    }\n    /**\n     * Update the team&#039;s preferences by its unique ID. The object you pass is stored as is and replaces any previous value. The maximum allowed prefs size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    updatePrefs(teamId, prefs) {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        const apiHeaders = {\n            'content-type': 'application/json',\n        };\n        return this.client.call('put', uri, apiHeaders, payload);\n    }\n}\n\n/**\n * Helper class to generate permission strings for resources.\n */\nclass Permission {\n}\n/**\n * Generate read permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.read = (role) => {\n    return `read(\"${role}\")`;\n};\n/**\n * Generate write permission string for the provided role.\n *\n * This is an alias of update, delete, and possibly create.\n * Don't use write in combination with update, delete, or create.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.write = (role) => {\n    return `write(\"${role}\")`;\n};\n/**\n * Generate create permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.create = (role) => {\n    return `create(\"${role}\")`;\n};\n/**\n * Generate update permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.update = (role) => {\n    return `update(\"${role}\")`;\n};\n/**\n * Generate delete permission string for the provided role.\n *\n * @param {string} role\n * @returns {string}\n */\nPermission.delete = (role) => {\n    return `delete(\"${role}\")`;\n};\n\n/**\n * Helper class to generate role strings for `Permission`.\n */\nclass Role {\n    /**\n     * Grants access to anyone.\n     *\n     * This includes authenticated and unauthenticated users.\n     *\n     * @returns {string}\n     */\n    static any() {\n        return 'any';\n    }\n    /**\n     * Grants access to a specific user by user ID.\n     *\n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id\n     * @param {string} status\n     * @returns {string}\n     */\n    static user(id, status = '') {\n        if (status === '') {\n            return `user:${id}`;\n        }\n        return `user:${id}/${status}`;\n    }\n    /**\n     * Grants access to any authenticated or anonymous user.\n     *\n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} status\n     * @returns {string}\n     */\n    static users(status = '') {\n        if (status === '') {\n            return 'users';\n        }\n        return `users/${status}`;\n    }\n    /**\n     * Grants access to any guest user without a session.\n     *\n     * Authenticated users don't have access to this role.\n     *\n     * @returns {string}\n     */\n    static guests() {\n        return 'guests';\n    }\n    /**\n     * Grants access to a team by team ID.\n     *\n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     *\n     * @param {string} id\n     * @param {string} role\n     * @returns {string}\n     */\n    static team(id, role = '') {\n        if (role === '') {\n            return `team:${id}`;\n        }\n        return `team:${id}/${role}`;\n    }\n    /**\n     * Grants access to a specific member of a team.\n     *\n     * When the member is removed from the team, they will\n     * no longer have access.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    static member(id) {\n        return `member:${id}`;\n    }\n    /**\n     * Grants access to a user with the specified label.\n     *\n     * @param {string} name\n     * @returns  {string}\n     */\n    static label(name) {\n        return `label:${name}`;\n    }\n}\n\nvar _a, _ID_hexTimestamp;\n/**\n * Helper class to generate ID strings for resources.\n */\nclass ID {\n    /**\n     * Uses the provided ID as the ID for the resource.\n     *\n     * @param {string} id\n     * @returns {string}\n     */\n    static custom(id) {\n        return id;\n    }\n    /**\n     * Have Appwrite generate a unique ID for you.\n     *\n     * @param {number} padding. Default is 7.\n     * @returns {string}\n     */\n    static unique(padding = 7) {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = __classPrivateFieldGet(ID, _a, \"m\", _ID_hexTimestamp).call(ID);\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n_a = ID, _ID_hexTimestamp = function _ID_hexTimestamp() {\n    const now = new Date();\n    const sec = Math.floor(now.getTime() / 1000);\n    const msec = now.getMilliseconds();\n    // Convert to hexadecimal\n    const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n    return hexTimestamp;\n};\n\nvar AuthenticatorType;\n(function (AuthenticatorType) {\n    AuthenticatorType[\"Totp\"] = \"totp\";\n})(AuthenticatorType || (AuthenticatorType = {}));\n\nvar AuthenticationFactor;\n(function (AuthenticationFactor) {\n    AuthenticationFactor[\"Email\"] = \"email\";\n    AuthenticationFactor[\"Phone\"] = \"phone\";\n    AuthenticationFactor[\"Totp\"] = \"totp\";\n    AuthenticationFactor[\"Recoverycode\"] = \"recoverycode\";\n})(AuthenticationFactor || (AuthenticationFactor = {}));\n\nvar OAuthProvider;\n(function (OAuthProvider) {\n    OAuthProvider[\"Amazon\"] = \"amazon\";\n    OAuthProvider[\"Apple\"] = \"apple\";\n    OAuthProvider[\"Auth0\"] = \"auth0\";\n    OAuthProvider[\"Authentik\"] = \"authentik\";\n    OAuthProvider[\"Autodesk\"] = \"autodesk\";\n    OAuthProvider[\"Bitbucket\"] = \"bitbucket\";\n    OAuthProvider[\"Bitly\"] = \"bitly\";\n    OAuthProvider[\"Box\"] = \"box\";\n    OAuthProvider[\"Dailymotion\"] = \"dailymotion\";\n    OAuthProvider[\"Discord\"] = \"discord\";\n    OAuthProvider[\"Disqus\"] = \"disqus\";\n    OAuthProvider[\"Dropbox\"] = \"dropbox\";\n    OAuthProvider[\"Etsy\"] = \"etsy\";\n    OAuthProvider[\"Facebook\"] = \"facebook\";\n    OAuthProvider[\"Figma\"] = \"figma\";\n    OAuthProvider[\"Github\"] = \"github\";\n    OAuthProvider[\"Gitlab\"] = \"gitlab\";\n    OAuthProvider[\"Google\"] = \"google\";\n    OAuthProvider[\"Linkedin\"] = \"linkedin\";\n    OAuthProvider[\"Microsoft\"] = \"microsoft\";\n    OAuthProvider[\"Notion\"] = \"notion\";\n    OAuthProvider[\"Oidc\"] = \"oidc\";\n    OAuthProvider[\"Okta\"] = \"okta\";\n    OAuthProvider[\"Paypal\"] = \"paypal\";\n    OAuthProvider[\"PaypalSandbox\"] = \"paypalSandbox\";\n    OAuthProvider[\"Podio\"] = \"podio\";\n    OAuthProvider[\"Salesforce\"] = \"salesforce\";\n    OAuthProvider[\"Slack\"] = \"slack\";\n    OAuthProvider[\"Spotify\"] = \"spotify\";\n    OAuthProvider[\"Stripe\"] = \"stripe\";\n    OAuthProvider[\"Tradeshift\"] = \"tradeshift\";\n    OAuthProvider[\"TradeshiftBox\"] = \"tradeshiftBox\";\n    OAuthProvider[\"Twitch\"] = \"twitch\";\n    OAuthProvider[\"Wordpress\"] = \"wordpress\";\n    OAuthProvider[\"Yahoo\"] = \"yahoo\";\n    OAuthProvider[\"Yammer\"] = \"yammer\";\n    OAuthProvider[\"Yandex\"] = \"yandex\";\n    OAuthProvider[\"Zoho\"] = \"zoho\";\n    OAuthProvider[\"Zoom\"] = \"zoom\";\n    OAuthProvider[\"Mock\"] = \"mock\";\n})(OAuthProvider || (OAuthProvider = {}));\n\nvar Browser;\n(function (Browser) {\n    Browser[\"AvantBrowser\"] = \"aa\";\n    Browser[\"AndroidWebViewBeta\"] = \"an\";\n    Browser[\"GoogleChrome\"] = \"ch\";\n    Browser[\"GoogleChromeIOS\"] = \"ci\";\n    Browser[\"GoogleChromeMobile\"] = \"cm\";\n    Browser[\"Chromium\"] = \"cr\";\n    Browser[\"MozillaFirefox\"] = \"ff\";\n    Browser[\"Safari\"] = \"sf\";\n    Browser[\"MobileSafari\"] = \"mf\";\n    Browser[\"MicrosoftEdge\"] = \"ps\";\n    Browser[\"MicrosoftEdgeIOS\"] = \"oi\";\n    Browser[\"OperaMini\"] = \"om\";\n    Browser[\"Opera\"] = \"op\";\n    Browser[\"OperaNext\"] = \"on\";\n})(Browser || (Browser = {}));\n\nvar CreditCard;\n(function (CreditCard) {\n    CreditCard[\"AmericanExpress\"] = \"amex\";\n    CreditCard[\"Argencard\"] = \"argencard\";\n    CreditCard[\"Cabal\"] = \"cabal\";\n    CreditCard[\"Cencosud\"] = \"cencosud\";\n    CreditCard[\"DinersClub\"] = \"diners\";\n    CreditCard[\"Discover\"] = \"discover\";\n    CreditCard[\"Elo\"] = \"elo\";\n    CreditCard[\"Hipercard\"] = \"hipercard\";\n    CreditCard[\"JCB\"] = \"jcb\";\n    CreditCard[\"Mastercard\"] = \"mastercard\";\n    CreditCard[\"Naranja\"] = \"naranja\";\n    CreditCard[\"TarjetaShopping\"] = \"targeta-shopping\";\n    CreditCard[\"UnionChinaPay\"] = \"union-china-pay\";\n    CreditCard[\"Visa\"] = \"visa\";\n    CreditCard[\"MIR\"] = \"mir\";\n    CreditCard[\"Maestro\"] = \"maestro\";\n    CreditCard[\"Rupay\"] = \"rupay\";\n})(CreditCard || (CreditCard = {}));\n\nvar Flag;\n(function (Flag) {\n    Flag[\"Afghanistan\"] = \"af\";\n    Flag[\"Angola\"] = \"ao\";\n    Flag[\"Albania\"] = \"al\";\n    Flag[\"Andorra\"] = \"ad\";\n    Flag[\"UnitedArabEmirates\"] = \"ae\";\n    Flag[\"Argentina\"] = \"ar\";\n    Flag[\"Armenia\"] = \"am\";\n    Flag[\"AntiguaAndBarbuda\"] = \"ag\";\n    Flag[\"Australia\"] = \"au\";\n    Flag[\"Austria\"] = \"at\";\n    Flag[\"Azerbaijan\"] = \"az\";\n    Flag[\"Burundi\"] = \"bi\";\n    Flag[\"Belgium\"] = \"be\";\n    Flag[\"Benin\"] = \"bj\";\n    Flag[\"BurkinaFaso\"] = \"bf\";\n    Flag[\"Bangladesh\"] = \"bd\";\n    Flag[\"Bulgaria\"] = \"bg\";\n    Flag[\"Bahrain\"] = \"bh\";\n    Flag[\"Bahamas\"] = \"bs\";\n    Flag[\"BosniaAndHerzegovina\"] = \"ba\";\n    Flag[\"Belarus\"] = \"by\";\n    Flag[\"Belize\"] = \"bz\";\n    Flag[\"Bolivia\"] = \"bo\";\n    Flag[\"Brazil\"] = \"br\";\n    Flag[\"Barbados\"] = \"bb\";\n    Flag[\"BruneiDarussalam\"] = \"bn\";\n    Flag[\"Bhutan\"] = \"bt\";\n    Flag[\"Botswana\"] = \"bw\";\n    Flag[\"CentralAfricanRepublic\"] = \"cf\";\n    Flag[\"Canada\"] = \"ca\";\n    Flag[\"Switzerland\"] = \"ch\";\n    Flag[\"Chile\"] = \"cl\";\n    Flag[\"China\"] = \"cn\";\n    Flag[\"CoteDIvoire\"] = \"ci\";\n    Flag[\"Cameroon\"] = \"cm\";\n    Flag[\"DemocraticRepublicOfTheCongo\"] = \"cd\";\n    Flag[\"RepublicOfTheCongo\"] = \"cg\";\n    Flag[\"Colombia\"] = \"co\";\n    Flag[\"Comoros\"] = \"km\";\n    Flag[\"CapeVerde\"] = \"cv\";\n    Flag[\"CostaRica\"] = \"cr\";\n    Flag[\"Cuba\"] = \"cu\";\n    Flag[\"Cyprus\"] = \"cy\";\n    Flag[\"CzechRepublic\"] = \"cz\";\n    Flag[\"Germany\"] = \"de\";\n    Flag[\"Djibouti\"] = \"dj\";\n    Flag[\"Dominica\"] = \"dm\";\n    Flag[\"Denmark\"] = \"dk\";\n    Flag[\"DominicanRepublic\"] = \"do\";\n    Flag[\"Algeria\"] = \"dz\";\n    Flag[\"Ecuador\"] = \"ec\";\n    Flag[\"Egypt\"] = \"eg\";\n    Flag[\"Eritrea\"] = \"er\";\n    Flag[\"Spain\"] = \"es\";\n    Flag[\"Estonia\"] = \"ee\";\n    Flag[\"Ethiopia\"] = \"et\";\n    Flag[\"Finland\"] = \"fi\";\n    Flag[\"Fiji\"] = \"fj\";\n    Flag[\"France\"] = \"fr\";\n    Flag[\"MicronesiaFederatedStatesOf\"] = \"fm\";\n    Flag[\"Gabon\"] = \"ga\";\n    Flag[\"UnitedKingdom\"] = \"gb\";\n    Flag[\"Georgia\"] = \"ge\";\n    Flag[\"Ghana\"] = \"gh\";\n    Flag[\"Guinea\"] = \"gn\";\n    Flag[\"Gambia\"] = \"gm\";\n    Flag[\"GuineaBissau\"] = \"gw\";\n    Flag[\"EquatorialGuinea\"] = \"gq\";\n    Flag[\"Greece\"] = \"gr\";\n    Flag[\"Grenada\"] = \"gd\";\n    Flag[\"Guatemala\"] = \"gt\";\n    Flag[\"Guyana\"] = \"gy\";\n    Flag[\"Honduras\"] = \"hn\";\n    Flag[\"Croatia\"] = \"hr\";\n    Flag[\"Haiti\"] = \"ht\";\n    Flag[\"Hungary\"] = \"hu\";\n    Flag[\"Indonesia\"] = \"id\";\n    Flag[\"India\"] = \"in\";\n    Flag[\"Ireland\"] = \"ie\";\n    Flag[\"IranIslamicRepublicOf\"] = \"ir\";\n    Flag[\"Iraq\"] = \"iq\";\n    Flag[\"Iceland\"] = \"is\";\n    Flag[\"Israel\"] = \"il\";\n    Flag[\"Italy\"] = \"it\";\n    Flag[\"Jamaica\"] = \"jm\";\n    Flag[\"Jordan\"] = \"jo\";\n    Flag[\"Japan\"] = \"jp\";\n    Flag[\"Kazakhstan\"] = \"kz\";\n    Flag[\"Kenya\"] = \"ke\";\n    Flag[\"Kyrgyzstan\"] = \"kg\";\n    Flag[\"Cambodia\"] = \"kh\";\n    Flag[\"Kiribati\"] = \"ki\";\n    Flag[\"SaintKittsAndNevis\"] = \"kn\";\n    Flag[\"SouthKorea\"] = \"kr\";\n    Flag[\"Kuwait\"] = \"kw\";\n    Flag[\"LaoPeopleSDemocraticRepublic\"] = \"la\";\n    Flag[\"Lebanon\"] = \"lb\";\n    Flag[\"Liberia\"] = \"lr\";\n    Flag[\"Libya\"] = \"ly\";\n    Flag[\"SaintLucia\"] = \"lc\";\n    Flag[\"Liechtenstein\"] = \"li\";\n    Flag[\"SriLanka\"] = \"lk\";\n    Flag[\"Lesotho\"] = \"ls\";\n    Flag[\"Lithuania\"] = \"lt\";\n    Flag[\"Luxembourg\"] = \"lu\";\n    Flag[\"Latvia\"] = \"lv\";\n    Flag[\"Morocco\"] = \"ma\";\n    Flag[\"Monaco\"] = \"mc\";\n    Flag[\"Moldova\"] = \"md\";\n    Flag[\"Madagascar\"] = \"mg\";\n    Flag[\"Maldives\"] = \"mv\";\n    Flag[\"Mexico\"] = \"mx\";\n    Flag[\"MarshallIslands\"] = \"mh\";\n    Flag[\"NorthMacedonia\"] = \"mk\";\n    Flag[\"Mali\"] = \"ml\";\n    Flag[\"Malta\"] = \"mt\";\n    Flag[\"Myanmar\"] = \"mm\";\n    Flag[\"Montenegro\"] = \"me\";\n    Flag[\"Mongolia\"] = \"mn\";\n    Flag[\"Mozambique\"] = \"mz\";\n    Flag[\"Mauritania\"] = \"mr\";\n    Flag[\"Mauritius\"] = \"mu\";\n    Flag[\"Malawi\"] = \"mw\";\n    Flag[\"Malaysia\"] = \"my\";\n    Flag[\"Namibia\"] = \"na\";\n    Flag[\"Niger\"] = \"ne\";\n    Flag[\"Nigeria\"] = \"ng\";\n    Flag[\"Nicaragua\"] = \"ni\";\n    Flag[\"Netherlands\"] = \"nl\";\n    Flag[\"Norway\"] = \"no\";\n    Flag[\"Nepal\"] = \"np\";\n    Flag[\"Nauru\"] = \"nr\";\n    Flag[\"NewZealand\"] = \"nz\";\n    Flag[\"Oman\"] = \"om\";\n    Flag[\"Pakistan\"] = \"pk\";\n    Flag[\"Panama\"] = \"pa\";\n    Flag[\"Peru\"] = \"pe\";\n    Flag[\"Philippines\"] = \"ph\";\n    Flag[\"Palau\"] = \"pw\";\n    Flag[\"PapuaNewGuinea\"] = \"pg\";\n    Flag[\"Poland\"] = \"pl\";\n    Flag[\"FrenchPolynesia\"] = \"pf\";\n    Flag[\"NorthKorea\"] = \"kp\";\n    Flag[\"Portugal\"] = \"pt\";\n    Flag[\"Paraguay\"] = \"py\";\n    Flag[\"Qatar\"] = \"qa\";\n    Flag[\"Romania\"] = \"ro\";\n    Flag[\"Russia\"] = \"ru\";\n    Flag[\"Rwanda\"] = \"rw\";\n    Flag[\"SaudiArabia\"] = \"sa\";\n    Flag[\"Sudan\"] = \"sd\";\n    Flag[\"Senegal\"] = \"sn\";\n    Flag[\"Singapore\"] = \"sg\";\n    Flag[\"SolomonIslands\"] = \"sb\";\n    Flag[\"SierraLeone\"] = \"sl\";\n    Flag[\"ElSalvador\"] = \"sv\";\n    Flag[\"SanMarino\"] = \"sm\";\n    Flag[\"Somalia\"] = \"so\";\n    Flag[\"Serbia\"] = \"rs\";\n    Flag[\"SouthSudan\"] = \"ss\";\n    Flag[\"SaoTomeAndPrincipe\"] = \"st\";\n    Flag[\"Suriname\"] = \"sr\";\n    Flag[\"Slovakia\"] = \"sk\";\n    Flag[\"Slovenia\"] = \"si\";\n    Flag[\"Sweden\"] = \"se\";\n    Flag[\"Eswatini\"] = \"sz\";\n    Flag[\"Seychelles\"] = \"sc\";\n    Flag[\"Syria\"] = \"sy\";\n    Flag[\"Chad\"] = \"td\";\n    Flag[\"Togo\"] = \"tg\";\n    Flag[\"Thailand\"] = \"th\";\n    Flag[\"Tajikistan\"] = \"tj\";\n    Flag[\"Turkmenistan\"] = \"tm\";\n    Flag[\"TimorLeste\"] = \"tl\";\n    Flag[\"Tonga\"] = \"to\";\n    Flag[\"TrinidadAndTobago\"] = \"tt\";\n    Flag[\"Tunisia\"] = \"tn\";\n    Flag[\"Turkey\"] = \"tr\";\n    Flag[\"Tuvalu\"] = \"tv\";\n    Flag[\"Tanzania\"] = \"tz\";\n    Flag[\"Uganda\"] = \"ug\";\n    Flag[\"Ukraine\"] = \"ua\";\n    Flag[\"Uruguay\"] = \"uy\";\n    Flag[\"UnitedStates\"] = \"us\";\n    Flag[\"Uzbekistan\"] = \"uz\";\n    Flag[\"VaticanCity\"] = \"va\";\n    Flag[\"SaintVincentAndTheGrenadines\"] = \"vc\";\n    Flag[\"Venezuela\"] = \"ve\";\n    Flag[\"Vietnam\"] = \"vn\";\n    Flag[\"Vanuatu\"] = \"vu\";\n    Flag[\"Samoa\"] = \"ws\";\n    Flag[\"Yemen\"] = \"ye\";\n    Flag[\"SouthAfrica\"] = \"za\";\n    Flag[\"Zambia\"] = \"zm\";\n    Flag[\"Zimbabwe\"] = \"zw\";\n})(Flag || (Flag = {}));\n\nvar ExecutionMethod;\n(function (ExecutionMethod) {\n    ExecutionMethod[\"GET\"] = \"GET\";\n    ExecutionMethod[\"POST\"] = \"POST\";\n    ExecutionMethod[\"PUT\"] = \"PUT\";\n    ExecutionMethod[\"PATCH\"] = \"PATCH\";\n    ExecutionMethod[\"DELETE\"] = \"DELETE\";\n    ExecutionMethod[\"OPTIONS\"] = \"OPTIONS\";\n})(ExecutionMethod || (ExecutionMethod = {}));\n\nvar ImageGravity;\n(function (ImageGravity) {\n    ImageGravity[\"Center\"] = \"center\";\n    ImageGravity[\"Topleft\"] = \"top-left\";\n    ImageGravity[\"Top\"] = \"top\";\n    ImageGravity[\"Topright\"] = \"top-right\";\n    ImageGravity[\"Left\"] = \"left\";\n    ImageGravity[\"Right\"] = \"right\";\n    ImageGravity[\"Bottomleft\"] = \"bottom-left\";\n    ImageGravity[\"Bottom\"] = \"bottom\";\n    ImageGravity[\"Bottomright\"] = \"bottom-right\";\n})(ImageGravity || (ImageGravity = {}));\n\nvar ImageFormat;\n(function (ImageFormat) {\n    ImageFormat[\"Jpg\"] = \"jpg\";\n    ImageFormat[\"Jpeg\"] = \"jpeg\";\n    ImageFormat[\"Png\"] = \"png\";\n    ImageFormat[\"Webp\"] = \"webp\";\n    ImageFormat[\"Heic\"] = \"heic\";\n    ImageFormat[\"Avif\"] = \"avif\";\n})(ImageFormat || (ImageFormat = {}));\n\n\n//# sourceMappingURL=sdk.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXBwd3JpdGUvZGlzdC9lc20vc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxpQkFBaUI7QUFDaEMsZUFBZSxZQUFZO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsWUFBWTtBQUN2QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFlBQVk7QUFDdkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxZQUFZO0FBQ3ZCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsWUFBWTtBQUN2QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFlBQVk7QUFDdkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxZQUFZO0FBQ3ZCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsaUJBQWlCO0FBQzVCLFdBQVcsaUJBQWlCO0FBQzVCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxtQkFBbUI7QUFDOUIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1HQUFtRyxnQkFBZ0I7QUFDbkg7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtJQUErSTtBQUMvSSxnSEFBZ0gsb0JBQW9CO0FBQ3BJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxpQkFBaUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQsaUJBQWlCLFlBQVk7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxhQUFhO0FBQ3pEO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELElBQUk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLDJDQUEyQyxzQkFBc0I7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRDtBQUNyRDtBQUNBLHFDQUFxQztBQUNyQztBQUNBLG9EQUFvRCxNQUFNLEdBQUcsUUFBUSxHQUFHLFVBQVU7QUFDbEY7QUFDQSw0REFBNEQsc0JBQXNCLG9DQUFvQztBQUN0SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0Esa0NBQWtDLGFBQWE7QUFDL0M7QUFDQTtBQUNBLG9CQUFvQixlQUFlO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUZBQXlGLHFCQUFxQjtBQUM5RztBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVEO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQzs7QUFFdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsV0FBVyxZQUFZLFdBQVc7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsU0FBUztBQUN4QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBbUI7QUFDbEMsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELEtBQUssWUFBWSxLQUFLO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBbUI7QUFDbEMsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCxLQUFLLFlBQVksS0FBSztBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQW1CO0FBQ2xDLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELEtBQUssWUFBWSxLQUFLO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzQkFBc0I7QUFDckMsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStEO0FBQy9EO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzQkFBc0I7QUFDckMsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd2VBQXdlO0FBQ3hlO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdPQUF3Tzs7QUFFeE87O0FBRUE7O0FBRUE7QUFDQSxlQUFlLGVBQWU7QUFDOUIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELFNBQVMsWUFBWSxTQUFTO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCw4Q0FBOEMsY0FBYztBQUNsSDtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsVUFBVSxZQUFZLFVBQVU7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtEO0FBQ2xEO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxVQUFVLFlBQVksVUFBVTtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLGNBQWMsc0dBQXNHO0FBQ3RKO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFVBQVUsWUFBWSxVQUFVO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCwyREFBMkQ7QUFDM0c7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLGlCQUFpQixTQUFTO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsaUJBQWlCLFNBQVM7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJZQUEyWTs7QUFFM1k7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxTQUFTO0FBQ3hCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhpQkFBOGlCOztBQUU5aUI7O0FBRUE7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFNBQVM7QUFDeEIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd09BQXdPOztBQUV4Tzs7QUFFQTtBQUNBO0FBQ0EsZUFBZSxlQUFlO0FBQzlCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxTQUFTLFlBQVksU0FBUztBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5WUFBeVk7O0FBRXpZO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9vQkFBb29COztBQUVwb0I7O0FBRUE7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0lBQStJLGlVQUFpVTtBQUNoZDtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZJQUE2STtBQUM3STtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZUFBZSxTQUFTO0FBQ3hCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxLQUFLLFlBQVksS0FBSztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLGVBQWUsWUFBWTtBQUMzQixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsS0FBSyxZQUFZLEtBQUs7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsZUFBZSxNQUFNO0FBQ3JCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxLQUFLLFlBQVksS0FBSztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3T0FBd08sV0FBVzs7QUFFblAsc0tBQXNLOztBQUV0Szs7QUFFQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxTQUFTO0FBQ3hCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkM7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsc0JBQXNCLFdBQVcseUJBQXlCLGFBQWE7QUFDbEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsdUNBQXVDO0FBQ3RELGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyxjQUFjLGFBQWEsc0JBQXNCLFdBQVcseUJBQXlCLGFBQWE7QUFDbEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLFlBQVksV0FBVyxZQUFZLFdBQVcseUJBQXlCLGFBQWEsMkJBQTJCLFdBQVc7QUFDck07QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLFlBQVksV0FBVyxZQUFZLFdBQVcseUJBQXlCLGFBQWEsMkJBQTJCLFdBQVc7QUFDck07QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLGdEQUFnRDtBQUMvRCxlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsY0FBYyxhQUFhLFlBQVksV0FBVyxZQUFZLFdBQVcseUJBQXlCLGFBQWEsMkJBQTJCLFdBQVc7QUFDck07QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLGNBQWMsYUFBYSxZQUFZLFdBQVcsWUFBWSxXQUFXLHlCQUF5QixhQUFhLDJCQUEyQixXQUFXO0FBQ3JNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxXQUFXLHVCQUF1QixXQUFXO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsU0FBUztBQUN4QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxpQkFBaUI7QUFDaEMsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsV0FBVyx1QkFBdUIsV0FBVztBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFdBQVcsYUFBYSxZQUFZLFlBQVksV0FBVyx5QkFBeUIsWUFBWTtBQUNySTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFFBQVEsd0JBQXdCLFFBQVE7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFFBQVEsY0FBYyxhQUFhLFlBQVksUUFBUSxzQkFBc0IsYUFBYTtBQUN0STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsa0JBQWtCLFNBQVM7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsdUlBQXVJOztBQUV2SSxZQUFZOztBQUVaO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLE1BQU07QUFDckIsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQSxrRkFBa0Y7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsa0JBQWtCLFNBQVM7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLFFBQVEsT0FBTyxZQUFZLFNBQVMsdUJBQXVCLE9BQU87QUFDdEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxRQUFRLE9BQU8sWUFBWSxTQUFTLHVCQUF1QixPQUFPO0FBQ3RIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxTQUFTLFFBQVEsT0FBTyxZQUFZLFNBQVMsdUJBQXVCLE9BQU87QUFDdEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNGQUFzRixzQ0FBc0M7QUFDNUg7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxRQUFRLE9BQU8scUJBQXFCLFNBQVMsdUJBQXVCLE9BQU87QUFDL0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLGNBQWM7QUFDN0IsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsYUFBYTtBQUM1QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFNBQVMsUUFBUSxPQUFPLG9CQUFvQixTQUFTLHVCQUF1QixPQUFPO0FBQzlIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVIQUF1SCxzQ0FBc0M7QUFDN0o7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUyxRQUFRLE9BQU8saUJBQWlCLFNBQVMsdUJBQXVCLE9BQU87QUFDM0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxVQUFVO0FBQ3pCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLFlBQVksT0FBTztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLFlBQVksT0FBTztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCLFVBQVU7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLFlBQVksT0FBTztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLDhCQUE4QjtBQUMzRTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLHdCQUF3QixPQUFPO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRUQUE0VDs7QUFFNVQsdUhBQXVILFdBQVc7O0FBRWxJOztBQUVBOztBQUVBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxPQUFPLHdCQUF3QixPQUFPO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxjQUFjLGFBQWEsWUFBWSxPQUFPLHFCQUFxQixhQUFhO0FBQ3hIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFVBQVU7QUFDekIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sY0FBYyxhQUFhLFlBQVksT0FBTyxxQkFBcUIsYUFBYTtBQUN4SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxjQUFjLGFBQWEsWUFBWSxPQUFPLHFCQUFxQixhQUFhO0FBQ3hIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxjQUFjLGFBQWEsbUJBQW1CLE9BQU8scUJBQXFCLGFBQWE7QUFDL0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixrRUFBa0U7QUFDM0Y7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE9BQU8sa0JBQWtCLE9BQU87QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsT0FBTyxrQkFBa0IsT0FBTztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxvQkFBb0IsS0FBSztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBLHFCQUFxQixLQUFLO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0Esc0JBQXNCLEtBQUs7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxzQkFBc0IsS0FBSztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBLHNCQUFzQixLQUFLO0FBQzNCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixHQUFHO0FBQzlCO0FBQ0EsdUJBQXVCLEdBQUcsR0FBRyxPQUFPO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLEdBQUc7QUFDOUI7QUFDQSx1QkFBdUIsR0FBRyxHQUFHLEtBQUs7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSx5QkFBeUIsR0FBRztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLHdCQUF3QixLQUFLO0FBQzdCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsYUFBYTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOENBQThDOztBQUUvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9EQUFvRDs7QUFFckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzQ0FBc0M7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQkFBMEI7O0FBRTNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7O0FBRWpDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9CQUFvQjs7QUFFckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMENBQTBDOztBQUUzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGtDQUFrQzs7QUFFa1A7QUFDclIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGF3YXovRGV2ZWxvcGVyL3N4ZS9zeGVfY2xpZW50L25vZGVfbW9kdWxlcy9hcHB3cml0ZS9kaXN0L2VzbS9zZGsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKlxyXG5Db3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cclxuXHJcblBlcm1pc3Npb24gdG8gdXNlLCBjb3B5LCBtb2RpZnksIGFuZC9vciBkaXN0cmlidXRlIHRoaXMgc29mdHdhcmUgZm9yIGFueVxyXG5wdXJwb3NlIHdpdGggb3Igd2l0aG91dCBmZWUgaXMgaGVyZWJ5IGdyYW50ZWQuXHJcblxyXG5USEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiIEFORCBUSEUgQVVUSE9SIERJU0NMQUlNUyBBTEwgV0FSUkFOVElFUyBXSVRIXHJcblJFR0FSRCBUTyBUSElTIFNPRlRXQVJFIElOQ0xVRElORyBBTEwgSU1QTElFRCBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWVxyXG5BTkQgRklUTkVTUy4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUiBCRSBMSUFCTEUgRk9SIEFOWSBTUEVDSUFMLCBESVJFQ1QsXHJcbklORElSRUNULCBPUiBDT05TRVFVRU5USUFMIERBTUFHRVMgT1IgQU5ZIERBTUFHRVMgV0hBVFNPRVZFUiBSRVNVTFRJTkcgRlJPTVxyXG5MT1NTIE9GIFVTRSwgREFUQSBPUiBQUk9GSVRTLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgTkVHTElHRU5DRSBPUlxyXG5PVEhFUiBUT1JUSU9VUyBBQ1RJT04sIEFSSVNJTkcgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgVVNFIE9SXHJcblBFUkZPUk1BTkNFIE9GIFRISVMgU09GVFdBUkUuXHJcbioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqICovXHJcblxyXG5mdW5jdGlvbiBfX2F3YWl0ZXIodGhpc0FyZywgX2FyZ3VtZW50cywgUCwgZ2VuZXJhdG9yKSB7XHJcbiAgICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cclxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xyXG4gICAgICAgIGZ1bmN0aW9uIGZ1bGZpbGxlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvci5uZXh0KHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cclxuICAgICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cclxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxyXG4gICAgICAgIHN0ZXAoKGdlbmVyYXRvciA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSkubmV4dCgpKTtcclxuICAgIH0pO1xyXG59XHJcblxyXG5mdW5jdGlvbiBfX2NsYXNzUHJpdmF0ZUZpZWxkR2V0KHJlY2VpdmVyLCBzdGF0ZSwga2luZCwgZikge1xyXG4gICAgaWYgKGtpbmQgPT09IFwiYVwiICYmICFmKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiUHJpdmF0ZSBhY2Nlc3NvciB3YXMgZGVmaW5lZCB3aXRob3V0IGEgZ2V0dGVyXCIpO1xyXG4gICAgaWYgKHR5cGVvZiBzdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gcmVjZWl2ZXIgIT09IHN0YXRlIHx8ICFmIDogIXN0YXRlLmhhcyhyZWNlaXZlcikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgcmVhZCBwcml2YXRlIG1lbWJlciBmcm9tIGFuIG9iamVjdCB3aG9zZSBjbGFzcyBkaWQgbm90IGRlY2xhcmUgaXRcIik7XHJcbiAgICByZXR1cm4ga2luZCA9PT0gXCJtXCIgPyBmIDoga2luZCA9PT0gXCJhXCIgPyBmLmNhbGwocmVjZWl2ZXIpIDogZiA/IGYudmFsdWUgOiBzdGF0ZS5nZXQocmVjZWl2ZXIpO1xyXG59XG5cbi8qKlxuICogSGVscGVyIGNsYXNzIHRvIGdlbmVyYXRlIHF1ZXJ5IHN0cmluZ3MuXG4gKi9cbmNsYXNzIFF1ZXJ5IHtcbiAgICAvKipcbiAgICAgKiBDb25zdHJ1Y3RvciBmb3IgUXVlcnkgY2xhc3MuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWV0aG9kXG4gICAgICogQHBhcmFtIHtBdHRyaWJ1dGVzVHlwZXN9IGF0dHJpYnV0ZVxuICAgICAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVzXG4gICAgICovXG4gICAgY29uc3RydWN0b3IobWV0aG9kLCBhdHRyaWJ1dGUsIHZhbHVlcykge1xuICAgICAgICB0aGlzLm1ldGhvZCA9IG1ldGhvZDtcbiAgICAgICAgdGhpcy5hdHRyaWJ1dGUgPSBhdHRyaWJ1dGU7XG4gICAgICAgIGlmICh2YWx1ZXMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWVzKSkge1xuICAgICAgICAgICAgICAgIHRoaXMudmFsdWVzID0gdmFsdWVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy52YWx1ZXMgPSBbdmFsdWVzXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb252ZXJ0IHRoZSBxdWVyeSBvYmplY3QgdG8gYSBKU09OIHN0cmluZy5cbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgdG9TdHJpbmcoKSB7XG4gICAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICBtZXRob2Q6IHRoaXMubWV0aG9kLFxuICAgICAgICAgICAgYXR0cmlidXRlOiB0aGlzLmF0dHJpYnV0ZSxcbiAgICAgICAgICAgIHZhbHVlczogdGhpcy52YWx1ZXMsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgZXF1YWwgdG8gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuZXF1YWwgPSAoYXR0cmlidXRlLCB2YWx1ZSkgPT4gbmV3IFF1ZXJ5KFwiZXF1YWxcIiwgYXR0cmlidXRlLCB2YWx1ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgbm90IGVxdWFsIHRvIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7UXVlcnlUeXBlc30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm5vdEVxdWFsID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcIm5vdEVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIGxlc3MgdGhhbiB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge1F1ZXJ5VHlwZXN9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5sZXNzVGhhbiA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJsZXNzVGhhblwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBsZXNzIHRoYW4gb3IgZXF1YWwgdG8gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkubGVzc1RoYW5FcXVhbCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJsZXNzVGhhbkVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIGdyZWF0ZXIgdGhhbiB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge1F1ZXJ5VHlwZXN9IHZhbHVlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5ncmVhdGVyVGhhbiA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJncmVhdGVyVGhhblwiLCBhdHRyaWJ1dGUsIHZhbHVlKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIHdoZXJlIGF0dHJpYnV0ZSBpcyBncmVhdGVyIHRoYW4gb3IgZXF1YWwgdG8gdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtRdWVyeVR5cGVzfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuZ3JlYXRlclRoYW5FcXVhbCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJncmVhdGVyVGhhbkVxdWFsXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGlzIG51bGwuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuaXNOdWxsID0gKGF0dHJpYnV0ZSkgPT4gbmV3IFF1ZXJ5KFwiaXNOdWxsXCIsIGF0dHJpYnV0ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgbm90IG51bGwuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuaXNOb3ROdWxsID0gKGF0dHJpYnV0ZSkgPT4gbmV3IFF1ZXJ5KFwiaXNOb3ROdWxsXCIsIGF0dHJpYnV0ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogRmlsdGVyIHJlc291cmNlcyB3aGVyZSBhdHRyaWJ1dGUgaXMgYmV0d2VlbiBzdGFydCBhbmQgZW5kIChpbmNsdXNpdmUpLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7c3RyaW5nIHwgbnVtYmVyfSBzdGFydFxuICogQHBhcmFtIHtzdHJpbmcgfCBudW1iZXJ9IGVuZFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuYmV0d2VlbiA9IChhdHRyaWJ1dGUsIHN0YXJ0LCBlbmQpID0+IG5ldyBRdWVyeShcImJldHdlZW5cIiwgYXR0cmlidXRlLCBbc3RhcnQsIGVuZF0pLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIHN0YXJ0cyB3aXRoIHZhbHVlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUXVlcnkuc3RhcnRzV2l0aCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJzdGFydHNXaXRoXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGVuZHMgd2l0aCB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmVuZHNXaXRoID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcImVuZHNXaXRoXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFNwZWNpZnkgd2hpY2ggYXR0cmlidXRlcyBzaG91bGQgYmUgcmV0dXJuZWQgYnkgdGhlIEFQSSBjYWxsLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nW119IGF0dHJpYnV0ZXNcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LnNlbGVjdCA9IChhdHRyaWJ1dGVzKSA9PiBuZXcgUXVlcnkoXCJzZWxlY3RcIiwgdW5kZWZpbmVkLCBhdHRyaWJ1dGVzKS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIGJ5IHNlYXJjaGluZyBhdHRyaWJ1dGUgZm9yIHZhbHVlLlxuICogQSBmdWxsdGV4dCBpbmRleCBvbiBhdHRyaWJ1dGUgaXMgcmVxdWlyZWQgZm9yIHRoaXMgcXVlcnkgdG8gd29yay5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LnNlYXJjaCA9IChhdHRyaWJ1dGUsIHZhbHVlKSA9PiBuZXcgUXVlcnkoXCJzZWFyY2hcIiwgYXR0cmlidXRlLCB2YWx1ZSkudG9TdHJpbmcoKTtcbi8qKlxuICogU29ydCByZXN1bHRzIGJ5IGF0dHJpYnV0ZSBkZXNjZW5kaW5nLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm9yZGVyRGVzYyA9IChhdHRyaWJ1dGUpID0+IG5ldyBRdWVyeShcIm9yZGVyRGVzY1wiLCBhdHRyaWJ1dGUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFNvcnQgcmVzdWx0cyBieSBhdHRyaWJ1dGUgYXNjZW5kaW5nLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm9yZGVyQXNjID0gKGF0dHJpYnV0ZSkgPT4gbmV3IFF1ZXJ5KFwib3JkZXJBc2NcIiwgYXR0cmlidXRlKS50b1N0cmluZygpO1xuLyoqXG4gKiBSZXR1cm4gcmVzdWx0cyBhZnRlciBkb2N1bWVudElkLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5jdXJzb3JBZnRlciA9IChkb2N1bWVudElkKSA9PiBuZXcgUXVlcnkoXCJjdXJzb3JBZnRlclwiLCB1bmRlZmluZWQsIGRvY3VtZW50SWQpLnRvU3RyaW5nKCk7XG4vKipcbiAqIFJldHVybiByZXN1bHRzIGJlZm9yZSBkb2N1bWVudElkLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5jdXJzb3JCZWZvcmUgPSAoZG9jdW1lbnRJZCkgPT4gbmV3IFF1ZXJ5KFwiY3Vyc29yQmVmb3JlXCIsIHVuZGVmaW5lZCwgZG9jdW1lbnRJZCkudG9TdHJpbmcoKTtcbi8qKlxuICogUmV0dXJuIG9ubHkgbGltaXQgcmVzdWx0cy5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gbGltaXRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmxpbWl0ID0gKGxpbWl0KSA9PiBuZXcgUXVlcnkoXCJsaW1pdFwiLCB1bmRlZmluZWQsIGxpbWl0KS50b1N0cmluZygpO1xuLyoqXG4gKiBGaWx0ZXIgcmVzb3VyY2VzIGJ5IHNraXBwaW5nIHRoZSBmaXJzdCBvZmZzZXQgcmVzdWx0cy5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gb2Zmc2V0XG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5vZmZzZXQgPSAob2Zmc2V0KSA9PiBuZXcgUXVlcnkoXCJvZmZzZXRcIiwgdW5kZWZpbmVkLCBvZmZzZXQpLnRvU3RyaW5nKCk7XG4vKipcbiAqIEZpbHRlciByZXNvdXJjZXMgd2hlcmUgYXR0cmlidXRlIGNvbnRhaW5zIHRoZSBzcGVjaWZpZWQgdmFsdWUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogQHBhcmFtIHtzdHJpbmcgfCBzdHJpbmdbXX0gdmFsdWVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5LmNvbnRhaW5zID0gKGF0dHJpYnV0ZSwgdmFsdWUpID0+IG5ldyBRdWVyeShcImNvbnRhaW5zXCIsIGF0dHJpYnV0ZSwgdmFsdWUpLnRvU3RyaW5nKCk7XG4vKipcbiAqIENvbWJpbmUgbXVsdGlwbGUgcXVlcmllcyB1c2luZyBsb2dpY2FsIE9SIG9wZXJhdG9yLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblF1ZXJ5Lm9yID0gKHF1ZXJpZXMpID0+IG5ldyBRdWVyeShcIm9yXCIsIHVuZGVmaW5lZCwgcXVlcmllcy5tYXAoKHF1ZXJ5KSA9PiBKU09OLnBhcnNlKHF1ZXJ5KSkpLnRvU3RyaW5nKCk7XG4vKipcbiAqIENvbWJpbmUgbXVsdGlwbGUgcXVlcmllcyB1c2luZyBsb2dpY2FsIEFORCBvcGVyYXRvci5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5RdWVyeS5hbmQgPSAocXVlcmllcykgPT4gbmV3IFF1ZXJ5KFwiYW5kXCIsIHVuZGVmaW5lZCwgcXVlcmllcy5tYXAoKHF1ZXJ5KSA9PiBKU09OLnBhcnNlKHF1ZXJ5KSkpLnRvU3RyaW5nKCk7XG5cbi8qKlxuICogRXhjZXB0aW9uIHRocm93biBieSB0aGUgIHBhY2thZ2VcbiAqL1xuY2xhc3MgQXBwd3JpdGVFeGNlcHRpb24gZXh0ZW5kcyBFcnJvciB7XG4gICAgLyoqXG4gICAgICogSW5pdGlhbGl6ZXMgYSBBcHB3cml0ZSBFeGNlcHRpb24uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWVzc2FnZSAtIFRoZSBlcnJvciBtZXNzYWdlLlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlIC0gVGhlIGVycm9yIGNvZGUuIERlZmF1bHQgaXMgMC5cbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdHlwZSAtIFRoZSBlcnJvciB0eXBlLiBEZWZhdWx0IGlzIGFuIGVtcHR5IHN0cmluZy5cbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcmVzcG9uc2UgLSBUaGUgcmVzcG9uc2Ugc3RyaW5nLiBEZWZhdWx0IGlzIGFuIGVtcHR5IHN0cmluZy5cbiAgICAgKi9cbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBjb2RlID0gMCwgdHlwZSA9ICcnLCByZXNwb25zZSA9ICcnKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSAnQXBwd3JpdGVFeGNlcHRpb24nO1xuICAgICAgICB0aGlzLm1lc3NhZ2UgPSBtZXNzYWdlO1xuICAgICAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICAgICAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICAgICAgICB0aGlzLnJlc3BvbnNlID0gcmVzcG9uc2U7XG4gICAgfVxufVxuLyoqXG4gKiBDbGllbnQgdGhhdCBoYW5kbGVzIHJlcXVlc3RzIHRvIEFwcHdyaXRlXG4gKi9cbmNsYXNzIENsaWVudCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBIb2xkcyBjb25maWd1cmF0aW9uIHN1Y2ggYXMgcHJvamVjdC5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuY29uZmlnID0ge1xuICAgICAgICAgICAgZW5kcG9pbnQ6ICdodHRwczovL2Nsb3VkLmFwcHdyaXRlLmlvL3YxJyxcbiAgICAgICAgICAgIGVuZHBvaW50UmVhbHRpbWU6ICcnLFxuICAgICAgICAgICAgcHJvamVjdDogJycsXG4gICAgICAgICAgICBqd3Q6ICcnLFxuICAgICAgICAgICAgbG9jYWxlOiAnJyxcbiAgICAgICAgICAgIHNlc3Npb246ICcnLFxuICAgICAgICAgICAgZGV2a2V5OiAnJyxcbiAgICAgICAgfTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIEN1c3RvbSBoZWFkZXJzIGZvciBBUEkgcmVxdWVzdHMuXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmhlYWRlcnMgPSB7XG4gICAgICAgICAgICAneC1zZGstbmFtZSc6ICdXZWInLFxuICAgICAgICAgICAgJ3gtc2RrLXBsYXRmb3JtJzogJ2NsaWVudCcsXG4gICAgICAgICAgICAneC1zZGstbGFuZ3VhZ2UnOiAnd2ViJyxcbiAgICAgICAgICAgICd4LXNkay12ZXJzaW9uJzogJzE4LjEuMScsXG4gICAgICAgICAgICAnWC1BcHB3cml0ZS1SZXNwb25zZS1Gb3JtYXQnOiAnMS43LjAnLFxuICAgICAgICB9O1xuICAgICAgICB0aGlzLnJlYWx0aW1lID0ge1xuICAgICAgICAgICAgc29ja2V0OiB1bmRlZmluZWQsXG4gICAgICAgICAgICB0aW1lb3V0OiB1bmRlZmluZWQsXG4gICAgICAgICAgICBoZWFydGJlYXQ6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHVybDogJycsXG4gICAgICAgICAgICBjaGFubmVsczogbmV3IFNldCgpLFxuICAgICAgICAgICAgc3Vic2NyaXB0aW9uczogbmV3IE1hcCgpLFxuICAgICAgICAgICAgc3Vic2NyaXB0aW9uc0NvdW50ZXI6IDAsXG4gICAgICAgICAgICByZWNvbm5lY3Q6IHRydWUsXG4gICAgICAgICAgICByZWNvbm5lY3RBdHRlbXB0czogMCxcbiAgICAgICAgICAgIGxhc3RNZXNzYWdlOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBjb25uZWN0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVhbHRpbWUudGltZW91dCk7XG4gICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS50aW1lb3V0ID0gd2luZG93ID09PSBudWxsIHx8IHdpbmRvdyA9PT0gdm9pZCAwID8gdm9pZCAwIDogd2luZG93LnNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNyZWF0ZVNvY2tldCgpO1xuICAgICAgICAgICAgICAgIH0sIDUwKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXRUaW1lb3V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgc3dpdGNoICh0cnVlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cyA8IDU6XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gMTAwMDtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdEF0dGVtcHRzIDwgMTU6XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gNTAwMDtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdEF0dGVtcHRzIDwgMTAwOlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDEwMDAwO1xuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDYwMDAwO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjcmVhdGVIZWFydGJlYXQ6ICgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5yZWFsdGltZS5oZWFydGJlYXQpIHtcbiAgICAgICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVhbHRpbWUuaGVhcnRiZWF0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5oZWFydGJlYXQgPSB3aW5kb3cgPT09IG51bGwgfHwgd2luZG93ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB3aW5kb3cuc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICAgICAgICAgIChfYSA9IHRoaXMucmVhbHRpbWUuc29ja2V0KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc2VuZChKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAncGluZydcbiAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIH0sIDIwMDAwKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjcmVhdGVTb2NrZXQ6ICgpID0+IHtcbiAgICAgICAgICAgICAgICB2YXIgX2EsIF9iLCBfYztcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5yZWFsdGltZS5jaGFubmVscy5zaXplIDwgMSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAoX2EgPSB0aGlzLnJlYWx0aW1lLnNvY2tldCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNsb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhbm5lbHMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgICAgICAgICAgICAgY2hhbm5lbHMuc2V0KCdwcm9qZWN0JywgdGhpcy5jb25maWcucHJvamVjdCk7XG4gICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5jaGFubmVscy5mb3JFYWNoKGNoYW5uZWwgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjaGFubmVscy5hcHBlbmQoJ2NoYW5uZWxzW10nLCBjaGFubmVsKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCB1cmwgPSB0aGlzLmNvbmZpZy5lbmRwb2ludFJlYWx0aW1lICsgJy9yZWFsdGltZT8nICsgY2hhbm5lbHMudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICBpZiAodXJsICE9PSB0aGlzLnJlYWx0aW1lLnVybCB8fCAvLyBDaGVjayBpZiBVUkwgaXMgcHJlc2VudFxuICAgICAgICAgICAgICAgICAgICAhdGhpcy5yZWFsdGltZS5zb2NrZXQgfHwgLy8gQ2hlY2sgaWYgV2ViU29ja2V0IGhhcyBub3QgYmVlbiBjcmVhdGVkXG4gICAgICAgICAgICAgICAgICAgICgoX2IgPSB0aGlzLnJlYWx0aW1lLnNvY2tldCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnJlYWR5U3RhdGUpID4gV2ViU29ja2V0Lk9QRU4gLy8gQ2hlY2sgaWYgV2ViU29ja2V0IGlzIENMT1NJTkcgKDMpIG9yIENMT1NFRCAoNClcbiAgICAgICAgICAgICAgICApIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMucmVhbHRpbWUuc29ja2V0ICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAoKF9jID0gdGhpcy5yZWFsdGltZS5zb2NrZXQpID09PSBudWxsIHx8IF9jID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYy5yZWFkeVN0YXRlKSA8IFdlYlNvY2tldC5DTE9TSU5HIC8vIENsb3NlIFdlYlNvY2tldCBpZiBpdCBpcyBDT05ORUNUSU5HICgwKSBvciBPUEVOICgxKVxuICAgICAgICAgICAgICAgICAgICApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUucmVjb25uZWN0ID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnNvY2tldC5jbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUudXJsID0gdXJsO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnNvY2tldCA9IG5ldyBXZWJTb2NrZXQodXJsKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQuYWRkRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIHRoaXMucmVhbHRpbWUub25NZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5zb2NrZXQuYWRkRXZlbnRMaXN0ZW5lcignb3BlbicsIF9ldmVudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnJlY29ubmVjdEF0dGVtcHRzID0gMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY3JlYXRlSGVhcnRiZWF0KCk7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlYWx0aW1lLnNvY2tldC5hZGRFdmVudExpc3RlbmVyKCdjbG9zZScsIGV2ZW50ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCF0aGlzLnJlYWx0aW1lLnJlY29ubmVjdCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICgoKF9iID0gKF9hID0gdGhpcy5yZWFsdGltZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmxhc3RNZXNzYWdlKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IudHlwZSkgPT09ICdlcnJvcicgJiYgLy8gQ2hlY2sgaWYgbGFzdCBtZXNzYWdlIHdhcyBvZiB0eXBlIGVycm9yXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICgoX2MgPSB0aGlzLnJlYWx0aW1lKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MubGFzdE1lc3NhZ2UuZGF0YSkuY29kZSA9PT0gMTAwOCAvLyBDaGVjayBmb3IgcG9saWN5IHZpb2xhdGlvbiAxMDA4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUucmVjb25uZWN0ID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0aW1lb3V0ID0gdGhpcy5yZWFsdGltZS5nZXRUaW1lb3V0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGBSZWFsdGltZSBnb3QgZGlzY29ubmVjdGVkLiBSZWNvbm5lY3Qgd2lsbCBiZSBhdHRlbXB0ZWQgaW4gJHt0aW1lb3V0IC8gMTAwMH0gc2Vjb25kcy5gLCBldmVudC5yZWFzb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5yZWNvbm5lY3RBdHRlbXB0cysrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY3JlYXRlU29ja2V0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9LCB0aW1lb3V0KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uTWVzc2FnZTogKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gSlNPTi5wYXJzZShldmVudC5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5sYXN0TWVzc2FnZSA9IG1lc3NhZ2U7XG4gICAgICAgICAgICAgICAgICAgIHN3aXRjaCAobWVzc2FnZS50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdjb25uZWN0ZWQnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvb2tpZSA9IEpTT04ucGFyc2UoKF9hID0gd2luZG93LmxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjb29raWVGYWxsYmFjaycpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAne30nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzZXNzaW9uID0gY29va2llID09PSBudWxsIHx8IGNvb2tpZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29va2llW2BhX3Nlc3Npb25fJHt0aGlzLmNvbmZpZy5wcm9qZWN0fWBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VEYXRhID0gbWVzc2FnZS5kYXRhO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzZXNzaW9uICYmICFtZXNzYWdlRGF0YS51c2VyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChfYiA9IHRoaXMucmVhbHRpbWUuc29ja2V0KSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Iuc2VuZChKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnYXV0aGVudGljYXRpb24nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ2V2ZW50JzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IG1lc3NhZ2UuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YSA9PT0gbnVsbCB8fCBkYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkYXRhLmNoYW5uZWxzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzU3Vic2NyaWJlZCA9IGRhdGEuY2hhbm5lbHMuc29tZShjaGFubmVsID0+IHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuaGFzKGNoYW5uZWwpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpc1N1YnNjcmliZWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucy5mb3JFYWNoKHN1YnNjcmlwdGlvbiA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YS5jaGFubmVscy5zb21lKGNoYW5uZWwgPT4gc3Vic2NyaXB0aW9uLmNoYW5uZWxzLmluY2x1ZGVzKGNoYW5uZWwpKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gc3Vic2NyaXB0aW9uLmNhbGxiYWNrKGRhdGEpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAncG9uZyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7IC8vIEhhbmRsZSBwb25nIHJlc3BvbnNlIGlmIG5lZWRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG1lc3NhZ2UuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY2xlYW5VcDogY2hhbm5lbHMgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY2hhbm5lbHMuZm9yRWFjaChjaGFubmVsID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNoYW5uZWxzLmluY2x1ZGVzKGNoYW5uZWwpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgZm91bmQgPSBBcnJheS5mcm9tKHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucykuc29tZSgoW19rZXksIHN1YnNjcmlwdGlvbl0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc3Vic2NyaXB0aW9uLmNoYW5uZWxzLmluY2x1ZGVzKGNoYW5uZWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWZvdW5kKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFsdGltZS5jaGFubmVscy5kZWxldGUoY2hhbm5lbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IEVuZHBvaW50XG4gICAgICpcbiAgICAgKiBZb3VyIHByb2plY3QgZW5kcG9pbnRcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbmRwb2ludFxuICAgICAqXG4gICAgICogQHJldHVybnMge3RoaXN9XG4gICAgICovXG4gICAgc2V0RW5kcG9pbnQoZW5kcG9pbnQpIHtcbiAgICAgICAgaWYgKCFlbmRwb2ludC5zdGFydHNXaXRoKCdodHRwOi8vJykgJiYgIWVuZHBvaW50LnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignSW52YWxpZCBlbmRwb2ludCBVUkw6ICcgKyBlbmRwb2ludCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jb25maWcuZW5kcG9pbnQgPSBlbmRwb2ludDtcbiAgICAgICAgdGhpcy5jb25maWcuZW5kcG9pbnRSZWFsdGltZSA9IGVuZHBvaW50LnJlcGxhY2UoJ2h0dHBzOi8vJywgJ3dzczovLycpLnJlcGxhY2UoJ2h0dHA6Ly8nLCAnd3M6Ly8nKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBSZWFsdGltZSBFbmRwb2ludFxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVuZHBvaW50UmVhbHRpbWVcbiAgICAgKlxuICAgICAqIEByZXR1cm5zIHt0aGlzfVxuICAgICAqL1xuICAgIHNldEVuZHBvaW50UmVhbHRpbWUoZW5kcG9pbnRSZWFsdGltZSkge1xuICAgICAgICBpZiAoIWVuZHBvaW50UmVhbHRpbWUuc3RhcnRzV2l0aCgnd3M6Ly8nKSAmJiAhZW5kcG9pbnRSZWFsdGltZS5zdGFydHNXaXRoKCd3c3M6Ly8nKSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdJbnZhbGlkIHJlYWx0aW1lIGVuZHBvaW50IFVSTDogJyArIGVuZHBvaW50UmVhbHRpbWUpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY29uZmlnLmVuZHBvaW50UmVhbHRpbWUgPSBlbmRwb2ludFJlYWx0aW1lO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IFByb2plY3RcbiAgICAgKlxuICAgICAqIFlvdXIgcHJvamVjdCBJRFxuICAgICAqXG4gICAgICogQHBhcmFtIHZhbHVlIHN0cmluZ1xuICAgICAqXG4gICAgICogQHJldHVybiB7dGhpc31cbiAgICAgKi9cbiAgICBzZXRQcm9qZWN0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1Qcm9qZWN0J10gPSB2YWx1ZTtcbiAgICAgICAgdGhpcy5jb25maWcucHJvamVjdCA9IHZhbHVlO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IEpXVFxuICAgICAqXG4gICAgICogWW91ciBzZWNyZXQgSlNPTiBXZWIgVG9rZW5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB2YWx1ZSBzdHJpbmdcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3RoaXN9XG4gICAgICovXG4gICAgc2V0SldUKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1KV1QnXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5qd3QgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBMb2NhbGVcbiAgICAgKlxuICAgICAqIEBwYXJhbSB2YWx1ZSBzdHJpbmdcbiAgICAgKlxuICAgICAqIEByZXR1cm4ge3RoaXN9XG4gICAgICovXG4gICAgc2V0TG9jYWxlKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuaGVhZGVyc1snWC1BcHB3cml0ZS1Mb2NhbGUnXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5sb2NhbGUgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBTZXNzaW9uXG4gICAgICpcbiAgICAgKiBUaGUgdXNlciBzZXNzaW9uIHRvIGF1dGhlbnRpY2F0ZSB3aXRoXG4gICAgICpcbiAgICAgKiBAcGFyYW0gdmFsdWUgc3RyaW5nXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHt0aGlzfVxuICAgICAqL1xuICAgIHNldFNlc3Npb24odmFsdWUpIHtcbiAgICAgICAgdGhpcy5oZWFkZXJzWydYLUFwcHdyaXRlLVNlc3Npb24nXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5zZXNzaW9uID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXQgRGV2S2V5XG4gICAgICpcbiAgICAgKiBZb3VyIHNlY3JldCBkZXYgQVBJIGtleVxuICAgICAqXG4gICAgICogQHBhcmFtIHZhbHVlIHN0cmluZ1xuICAgICAqXG4gICAgICogQHJldHVybiB7dGhpc31cbiAgICAgKi9cbiAgICBzZXREZXZLZXkodmFsdWUpIHtcbiAgICAgICAgdGhpcy5oZWFkZXJzWydYLUFwcHdyaXRlLURldi1LZXknXSA9IHZhbHVlO1xuICAgICAgICB0aGlzLmNvbmZpZy5kZXZrZXkgPSB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFN1YnNjcmliZXMgdG8gQXBwd3JpdGUgZXZlbnRzIGFuZCBwYXNzZXMgeW91IHRoZSBwYXlsb2FkIGluIHJlYWx0aW1lLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd8c3RyaW5nW119IGNoYW5uZWxzXG4gICAgICogQ2hhbm5lbCB0byBzdWJzY3JpYmUgLSBwYXNzIGEgc2luZ2xlIGNoYW5uZWwgYXMgYSBzdHJpbmcgb3IgbXVsdGlwbGUgd2l0aCBhbiBhcnJheSBvZiBzdHJpbmdzLlxuICAgICAqXG4gICAgICogUG9zc2libGUgY2hhbm5lbHMgYXJlOlxuICAgICAqIC0gYWNjb3VudFxuICAgICAqIC0gY29sbGVjdGlvbnNcbiAgICAgKiAtIGNvbGxlY3Rpb25zLltJRF1cbiAgICAgKiAtIGNvbGxlY3Rpb25zLltJRF0uZG9jdW1lbnRzXG4gICAgICogLSBkb2N1bWVudHNcbiAgICAgKiAtIGRvY3VtZW50cy5bSURdXG4gICAgICogLSBmaWxlc1xuICAgICAqIC0gZmlsZXMuW0lEXVxuICAgICAqIC0gZXhlY3V0aW9uc1xuICAgICAqIC0gZXhlY3V0aW9ucy5bSURdXG4gICAgICogLSBmdW5jdGlvbnMuW0lEXVxuICAgICAqIC0gdGVhbXNcbiAgICAgKiAtIHRlYW1zLltJRF1cbiAgICAgKiAtIG1lbWJlcnNoaXBzXG4gICAgICogLSBtZW1iZXJzaGlwcy5bSURdXG4gICAgICogQHBhcmFtIHsocGF5bG9hZDogUmVhbHRpbWVNZXNzYWdlKSA9PiB2b2lkfSBjYWxsYmFjayBJcyBjYWxsZWQgb24gZXZlcnkgcmVhbHRpbWUgdXBkYXRlLlxuICAgICAqIEByZXR1cm5zIHsoKSA9PiB2b2lkfSBVbnN1YnNjcmliZXMgZnJvbSBldmVudHMuXG4gICAgICovXG4gICAgc3Vic2NyaWJlKGNoYW5uZWxzLCBjYWxsYmFjaykge1xuICAgICAgICBsZXQgY2hhbm5lbEFycmF5ID0gdHlwZW9mIGNoYW5uZWxzID09PSAnc3RyaW5nJyA/IFtjaGFubmVsc10gOiBjaGFubmVscztcbiAgICAgICAgY2hhbm5lbEFycmF5LmZvckVhY2goY2hhbm5lbCA9PiB0aGlzLnJlYWx0aW1lLmNoYW5uZWxzLmFkZChjaGFubmVsKSk7XG4gICAgICAgIGNvbnN0IGNvdW50ZXIgPSB0aGlzLnJlYWx0aW1lLnN1YnNjcmlwdGlvbnNDb3VudGVyKys7XG4gICAgICAgIHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucy5zZXQoY291bnRlciwge1xuICAgICAgICAgICAgY2hhbm5lbHM6IGNoYW5uZWxBcnJheSxcbiAgICAgICAgICAgIGNhbGxiYWNrXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLnJlYWx0aW1lLmNvbm5lY3QoKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuc3Vic2NyaXB0aW9ucy5kZWxldGUoY291bnRlcik7XG4gICAgICAgICAgICB0aGlzLnJlYWx0aW1lLmNsZWFuVXAoY2hhbm5lbEFycmF5KTtcbiAgICAgICAgICAgIHRoaXMucmVhbHRpbWUuY29ubmVjdCgpO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBwcmVwYXJlUmVxdWVzdChtZXRob2QsIHVybCwgaGVhZGVycyA9IHt9LCBwYXJhbXMgPSB7fSkge1xuICAgICAgICBtZXRob2QgPSBtZXRob2QudG9VcHBlckNhc2UoKTtcbiAgICAgICAgaGVhZGVycyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMuaGVhZGVycywgaGVhZGVycyk7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYWxTdG9yYWdlKSB7XG4gICAgICAgICAgICBjb25zdCBjb29raWVGYWxsYmFjayA9IHdpbmRvdy5sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY29va2llRmFsbGJhY2snKTtcbiAgICAgICAgICAgIGlmIChjb29raWVGYWxsYmFjaykge1xuICAgICAgICAgICAgICAgIGhlYWRlcnNbJ1gtRmFsbGJhY2stQ29va2llcyddID0gY29va2llRmFsbGJhY2s7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgbGV0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICB9O1xuICAgICAgICBpZiAoaGVhZGVyc1snWC1BcHB3cml0ZS1EZXYtS2V5J10gPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgb3B0aW9ucy5jcmVkZW50aWFscyA9ICdpbmNsdWRlJztcbiAgICAgICAgfVxuICAgICAgICBpZiAobWV0aG9kID09PSAnR0VUJykge1xuICAgICAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoQ2xpZW50LmZsYXR0ZW4ocGFyYW1zKSkpIHtcbiAgICAgICAgICAgICAgICB1cmwuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN3aXRjaCAoaGVhZGVyc1snY29udGVudC10eXBlJ10pIHtcbiAgICAgICAgICAgICAgICBjYXNlICdhcHBsaWNhdGlvbi9qc29uJzpcbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5ib2R5ID0gSlNPTi5zdHJpbmdpZnkocGFyYW1zKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAnbXVsdGlwYXJ0L2Zvcm0tZGF0YSc6XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHBhcmFtcykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIEZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCB2YWx1ZSwgdmFsdWUubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgbmVzdGVkVmFsdWUgb2YgdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKGAke2tleX1bXWAsIG5lc3RlZFZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5ib2R5ID0gZm9ybURhdGE7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzWydjb250ZW50LXR5cGUnXTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHsgdXJpOiB1cmwudG9TdHJpbmcoKSwgb3B0aW9ucyB9O1xuICAgIH1cbiAgICBjaHVua2VkVXBsb2FkKG1ldGhvZCwgdXJsLCBoZWFkZXJzID0ge30sIG9yaWdpbmFsUGF5bG9hZCA9IHt9LCBvblByb2dyZXNzKSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBjb25zdCBmaWxlID0gT2JqZWN0LnZhbHVlcyhvcmlnaW5hbFBheWxvYWQpLmZpbmQoKHZhbHVlKSA9PiB2YWx1ZSBpbnN0YW5jZW9mIEZpbGUpO1xuICAgICAgICAgICAgaWYgKCFmaWxlKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGaWxlIG5vdCBmb3VuZCBpbiBwYXlsb2FkJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZmlsZS5zaXplIDw9IENsaWVudC5DSFVOS19TSVpFKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHlpZWxkIHRoaXMuY2FsbChtZXRob2QsIHVybCwgaGVhZGVycywgb3JpZ2luYWxQYXlsb2FkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBzdGFydCA9IDA7XG4gICAgICAgICAgICBsZXQgcmVzcG9uc2UgPSBudWxsO1xuICAgICAgICAgICAgd2hpbGUgKHN0YXJ0IDwgZmlsZS5zaXplKSB7XG4gICAgICAgICAgICAgICAgbGV0IGVuZCA9IHN0YXJ0ICsgQ2xpZW50LkNIVU5LX1NJWkU7IC8vIFByZXBhcmUgZW5kIGZvciB0aGUgbmV4dCBjaHVua1xuICAgICAgICAgICAgICAgIGlmIChlbmQgPj0gZmlsZS5zaXplKSB7XG4gICAgICAgICAgICAgICAgICAgIGVuZCA9IGZpbGUuc2l6ZTsgLy8gQWRqdXN0IGZvciB0aGUgbGFzdCBjaHVuayB0byBpbmNsdWRlIHRoZSBsYXN0IGJ5dGVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaGVhZGVyc1snY29udGVudC1yYW5nZSddID0gYGJ5dGVzICR7c3RhcnR9LSR7ZW5kIC0gMX0vJHtmaWxlLnNpemV9YDtcbiAgICAgICAgICAgICAgICBjb25zdCBjaHVuayA9IGZpbGUuc2xpY2Uoc3RhcnQsIGVuZCk7XG4gICAgICAgICAgICAgICAgbGV0IHBheWxvYWQgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIG9yaWdpbmFsUGF5bG9hZCksIHsgZmlsZTogbmV3IEZpbGUoW2NodW5rXSwgZmlsZS5uYW1lKSB9KTtcbiAgICAgICAgICAgICAgICByZXNwb25zZSA9IHlpZWxkIHRoaXMuY2FsbChtZXRob2QsIHVybCwgaGVhZGVycywgcGF5bG9hZCk7XG4gICAgICAgICAgICAgICAgaWYgKG9uUHJvZ3Jlc3MgJiYgdHlwZW9mIG9uUHJvZ3Jlc3MgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgb25Qcm9ncmVzcyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAkaWQ6IHJlc3BvbnNlLiRpZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2dyZXNzOiBNYXRoLnJvdW5kKChlbmQgLyBmaWxlLnNpemUpICogMTAwKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemVVcGxvYWRlZDogZW5kLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2h1bmtzVG90YWw6IE1hdGguY2VpbChmaWxlLnNpemUgLyBDbGllbnQuQ0hVTktfU0laRSksXG4gICAgICAgICAgICAgICAgICAgICAgICBjaHVua3NVcGxvYWRlZDogTWF0aC5jZWlsKGVuZCAvIENsaWVudC5DSFVOS19TSVpFKVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLiRpZCkge1xuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzWyd4LWFwcHdyaXRlLWlkJ10gPSByZXNwb25zZS4kaWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHN0YXJ0ID0gZW5kO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgcGluZygpIHtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNhbGwoJ0dFVCcsIG5ldyBVUkwodGhpcy5jb25maWcuZW5kcG9pbnQgKyAnL3BpbmcnKSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBjYWxsKG1ldGhvZCwgdXJsLCBoZWFkZXJzID0ge30sIHBhcmFtcyA9IHt9LCByZXNwb25zZVR5cGUgPSAnanNvbicpIHtcbiAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgdXJpLCBvcHRpb25zIH0gPSB0aGlzLnByZXBhcmVSZXF1ZXN0KG1ldGhvZCwgdXJsLCBoZWFkZXJzLCBwYXJhbXMpO1xuICAgICAgICAgICAgbGV0IGRhdGEgPSBudWxsO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSB5aWVsZCBmZXRjaCh1cmksIG9wdGlvbnMpO1xuICAgICAgICAgICAgLy8gdHlwZSBvcGFxdWU6IE5vLUNPUlMsIGRpZmZlcmVudC1vcmlnaW4gcmVzcG9uc2UgKENPUlMtaXNzdWUpXG4gICAgICAgICAgICBpZiAocmVzcG9uc2UudHlwZSA9PT0gJ29wYXF1ZScpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oYEludmFsaWQgT3JpZ2luLiBSZWdpc3RlciB5b3VyIG5ldyBjbGllbnQgKCR7d2luZG93LmxvY2F0aW9uLmhvc3R9KSBhcyBhIG5ldyBXZWIgcGxhdGZvcm0gb24geW91ciBwcm9qZWN0IGNvbnNvbGUgZGFzaGJvYXJkYCwgNDAzLCBcImZvcmJpZGRlblwiLCBcIlwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHdhcm5pbmdzID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ3gtYXBwd3JpdGUtd2FybmluZycpO1xuICAgICAgICAgICAgaWYgKHdhcm5pbmdzKSB7XG4gICAgICAgICAgICAgICAgd2FybmluZ3Muc3BsaXQoJzsnKS5mb3JFYWNoKCh3YXJuaW5nKSA9PiBjb25zb2xlLndhcm4oJ1dhcm5pbmc6ICcgKyB3YXJuaW5nKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKF9hID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ2NvbnRlbnQtdHlwZScpKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuaW5jbHVkZXMoJ2FwcGxpY2F0aW9uL2pzb24nKSkge1xuICAgICAgICAgICAgICAgIGRhdGEgPSB5aWVsZCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChyZXNwb25zZVR5cGUgPT09ICdhcnJheUJ1ZmZlcicpIHtcbiAgICAgICAgICAgICAgICBkYXRhID0geWllbGQgcmVzcG9uc2UuYXJyYXlCdWZmZXIoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGRhdGEgPSB7XG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHlpZWxkIHJlc3BvbnNlLnRleHQoKVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoNDAwIDw9IHJlc3BvbnNlLnN0YXR1cykge1xuICAgICAgICAgICAgICAgIGxldCByZXNwb25zZVRleHQgPSAnJztcbiAgICAgICAgICAgICAgICBpZiAoKChfYiA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCdjb250ZW50LXR5cGUnKSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmluY2x1ZGVzKCdhcHBsaWNhdGlvbi9qc29uJykpIHx8IHJlc3BvbnNlVHlwZSA9PT0gJ2FycmF5QnVmZmVyJykge1xuICAgICAgICAgICAgICAgICAgICByZXNwb25zZVRleHQgPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlVGV4dCA9IGRhdGEgPT09IG51bGwgfHwgZGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogZGF0YS5tZXNzYWdlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oZGF0YSA9PT0gbnVsbCB8fCBkYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkYXRhLm1lc3NhZ2UsIHJlc3BvbnNlLnN0YXR1cywgZGF0YSA9PT0gbnVsbCB8fCBkYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBkYXRhLnR5cGUsIHJlc3BvbnNlVGV4dCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBjb29raWVGYWxsYmFjayA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCdYLUZhbGxiYWNrLUNvb2tpZXMnKTtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYWxTdG9yYWdlICYmIGNvb2tpZUZhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgd2luZG93LmNvbnNvbGUud2FybignQXBwd3JpdGUgaXMgdXNpbmcgbG9jYWxTdG9yYWdlIGZvciBzZXNzaW9uIG1hbmFnZW1lbnQuIEluY3JlYXNlIHlvdXIgc2VjdXJpdHkgYnkgYWRkaW5nIGEgY3VzdG9tIGRvbWFpbiBhcyB5b3VyIEFQSSBlbmRwb2ludC4nKTtcbiAgICAgICAgICAgICAgICB3aW5kb3cubG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2Nvb2tpZUZhbGxiYWNrJywgY29va2llRmFsbGJhY2spO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBzdGF0aWMgZmxhdHRlbihkYXRhLCBwcmVmaXggPSAnJykge1xuICAgICAgICBsZXQgb3V0cHV0ID0ge307XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKGRhdGEpKSB7XG4gICAgICAgICAgICBsZXQgZmluYWxLZXkgPSBwcmVmaXggPyBwcmVmaXggKyAnWycgKyBrZXkgKyAnXScgOiBrZXk7XG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBvdXRwdXQgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIG91dHB1dCksIENsaWVudC5mbGF0dGVuKHZhbHVlLCBmaW5hbEtleSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgb3V0cHV0W2ZpbmFsS2V5XSA9IHZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvdXRwdXQ7XG4gICAgfVxufVxuQ2xpZW50LkNIVU5LX1NJWkUgPSAxMDI0ICogMTAyNCAqIDU7XG5cbmNsYXNzIFNlcnZpY2Uge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgc3RhdGljIGZsYXR0ZW4oZGF0YSwgcHJlZml4ID0gJycpIHtcbiAgICAgICAgbGV0IG91dHB1dCA9IHt9O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhkYXRhKSkge1xuICAgICAgICAgICAgbGV0IGZpbmFsS2V5ID0gcHJlZml4ID8gcHJlZml4ICsgJ1snICsga2V5ICsgJ10nIDoga2V5O1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgb3V0cHV0ID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBvdXRwdXQpLCBTZXJ2aWNlLmZsYXR0ZW4odmFsdWUsIGZpbmFsS2V5KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBvdXRwdXRbZmluYWxLZXldID0gdmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG91dHB1dDtcbiAgICB9XG59XG4vKipcbiAqIFRoZSBzaXplIGZvciBjaHVua2VkIHVwbG9hZHMgaW4gYnl0ZXMuXG4gKi9cblNlcnZpY2UuQ0hVTktfU0laRSA9IDUgKiAxMDI0ICogMTAyNDsgLy8gNU1CXG5cbmNsYXNzIEFjY291bnQge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIGdldCgpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gYWxsb3cgYSBuZXcgdXNlciB0byByZWdpc3RlciBhIG5ldyBhY2NvdW50IGluIHlvdXIgcHJvamVjdC4gQWZ0ZXIgdGhlIHVzZXIgcmVnaXN0cmF0aW9uIGNvbXBsZXRlcyBzdWNjZXNzZnVsbHksIHlvdSBjYW4gdXNlIHRoZSBbL2FjY291bnQvdmVyZmljYXRpb25dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVWZXJpZmljYXRpb24pIHJvdXRlIHRvIHN0YXJ0IHZlcmlmeWluZyB0aGUgdXNlciBlbWFpbCBhZGRyZXNzLiBUbyBhbGxvdyB0aGUgbmV3IHVzZXIgdG8gbG9naW4gdG8gdGhlaXIgbmV3IGFjY291bnQsIHlvdSBuZWVkIHRvIGNyZWF0ZSBhIG5ldyBbYWNjb3VudCBzZXNzaW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlRW1haWxTZXNzaW9uKS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZW1haWxcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGFzc3dvcmRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgY3JlYXRlKHVzZXJJZCwgZW1haWwsIHBhc3N3b3JkLCBuYW1lKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwYXNzd29yZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGVtYWlsICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZW1haWwnXSA9IGVtYWlsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwYXNzd29yZCddID0gcGFzc3dvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbmFtZSddID0gbmFtZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVcGRhdGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyIGFjY291bnQgZW1haWwgYWRkcmVzcy4gQWZ0ZXIgY2hhbmdpbmcgdXNlciBhZGRyZXNzLCB0aGUgdXNlciBjb25maXJtYXRpb24gc3RhdHVzIHdpbGwgZ2V0IHJlc2V0LiBBIG5ldyBjb25maXJtYXRpb24gZW1haWwgaXMgbm90IHNlbnQgYXV0b21hdGljYWxseSBob3dldmVyIHlvdSBjYW4gdXNlIHRoZSBzZW5kIGNvbmZpcm1hdGlvbiBlbWFpbCBlbmRwb2ludCBhZ2FpbiB0byBzZW5kIHRoZSBjb25maXJtYXRpb24gZW1haWwuIEZvciBzZWN1cml0eSBtZWFzdXJlcywgdXNlciBwYXNzd29yZCBpcyByZXF1aXJlZCB0byBjb21wbGV0ZSB0aGlzIHJlcXVlc3QuXG5UaGlzIGVuZHBvaW50IGNhbiBhbHNvIGJlIHVzZWQgdG8gY29udmVydCBhbiBhbm9ueW1vdXMgYWNjb3VudCB0byBhIG5vcm1hbCBvbmUsIGJ5IHBhc3NpbmcgYW4gZW1haWwgYWRkcmVzcyBhbmQgYSBuZXcgcGFzc3dvcmQuXG5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlRW1haWwoZW1haWwsIHBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwYXNzd29yZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9lbWFpbCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIGxpc3Qgb2YgaWRlbnRpdGllcyBmb3IgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuSWRlbnRpdHlMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0SWRlbnRpdGllcyhxdWVyaWVzKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvaWRlbnRpdGllcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYW4gaWRlbnRpdHkgYnkgaXRzIHVuaXF1ZSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpZGVudGl0eUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZUlkZW50aXR5KGlkZW50aXR5SWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBpZGVudGl0eUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJpZGVudGl0eUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L2lkZW50aXRpZXMve2lkZW50aXR5SWR9Jy5yZXBsYWNlKCd7aWRlbnRpdHlJZH0nLCBpZGVudGl0eUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNyZWF0ZSBhIEpTT04gV2ViIFRva2VuLiBZb3UgY2FuIHVzZSB0aGUgcmVzdWx0aW5nIEpXVCB0byBhdXRoZW50aWNhdGUgb24gYmVoYWxmIG9mIHRoZSBjdXJyZW50IHVzZXIgd2hlbiB3b3JraW5nIHdpdGggdGhlIEFwcHdyaXRlIHNlcnZlci1zaWRlIEFQSSBhbmQgU0RLcy4gVGhlIEpXVCBzZWNyZXQgaXMgdmFsaWQgZm9yIDE1IG1pbnV0ZXMgZnJvbSBpdHMgY3JlYXRpb24gYW5kIHdpbGwgYmUgaW52YWxpZCBpZiB0aGUgdXNlciB3aWxsIGxvZ291dCBpbiB0aGF0IHRpbWUgZnJhbWUuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuSnd0Pn1cbiAgICAgKi9cbiAgICBjcmVhdGVKV1QoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvand0cyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBsaXN0IG9mIGxhdGVzdCBzZWN1cml0eSBhY3Rpdml0eSBsb2dzIGZvciB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLiBFYWNoIGxvZyByZXR1cm5zIHVzZXIgSVAgYWRkcmVzcywgbG9jYXRpb24gYW5kIGRhdGUgYW5kIHRpbWUgb2YgbG9nLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Mb2dMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0TG9ncyhxdWVyaWVzKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbG9ncyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFbmFibGUgb3IgZGlzYWJsZSBNRkEgb24gYW4gYWNjb3VudC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gbWZhXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVNRkEobWZhKSB7XG4gICAgICAgIGlmICh0eXBlb2YgbWZhID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJtZmFcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG1mYSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ21mYSddID0gbWZhO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBZGQgYW4gYXV0aGVudGljYXRvciBhcHAgdG8gYmUgdXNlZCBhcyBhbiBNRkEgZmFjdG9yLiBWZXJpZnkgdGhlIGF1dGhlbnRpY2F0b3IgdXNpbmcgdGhlIFt2ZXJpZnkgYXV0aGVudGljYXRvcl0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlTWZhQXV0aGVudGljYXRvcikgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtBdXRoZW50aWNhdG9yVHlwZX0gdHlwZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZmFUeXBlPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNZmFBdXRoZW50aWNhdG9yKHR5cGUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0eXBlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0eXBlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L21mYS9hdXRoZW50aWNhdG9ycy97dHlwZX0nLnJlcGxhY2UoJ3t0eXBlfScsIHR5cGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFZlcmlmeSBhbiBhdXRoZW50aWNhdG9yIGFwcCBhZnRlciBhZGRpbmcgaXQgdXNpbmcgdGhlIFthZGQgYXV0aGVudGljYXRvcl0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhQXV0aGVudGljYXRvcikgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtBdXRoZW50aWNhdG9yVHlwZX0gdHlwZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBvdHBcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1mYUF1dGhlbnRpY2F0b3IodHlwZSwgb3RwKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidHlwZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvdHAgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm90cFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvYXV0aGVudGljYXRvcnMve3R5cGV9Jy5yZXBsYWNlKCd7dHlwZX0nLCB0eXBlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG90cCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ290cCddID0gb3RwO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGFuIGF1dGhlbnRpY2F0b3IgZm9yIGEgdXNlciBieSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7QXV0aGVudGljYXRvclR5cGV9IHR5cGVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlTWZhQXV0aGVudGljYXRvcih0eXBlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidHlwZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvYXV0aGVudGljYXRvcnMve3R5cGV9Jy5yZXBsYWNlKCd7dHlwZX0nLCB0eXBlKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEJlZ2luIHRoZSBwcm9jZXNzIG9mIE1GQSB2ZXJpZmljYXRpb24gYWZ0ZXIgc2lnbi1pbi4gRmluaXNoIHRoZSBmbG93IHdpdGggW3VwZGF0ZU1mYUNoYWxsZW5nZV0oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlTWZhQ2hhbGxlbmdlKSBtZXRob2QuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0F1dGhlbnRpY2F0aW9uRmFjdG9yfSBmYWN0b3JcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWZhQ2hhbGxlbmdlPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNZmFDaGFsbGVuZ2UoZmFjdG9yKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZmFjdG9yID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmYWN0b3JcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL2NoYWxsZW5nZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBmYWN0b3IgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydmYWN0b3InXSA9IGZhY3RvcjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb21wbGV0ZSB0aGUgTUZBIGNoYWxsZW5nZSBieSBwcm92aWRpbmcgdGhlIG9uZS10aW1lIHBhc3N3b3JkLiBGaW5pc2ggdGhlIHByb2Nlc3Mgb2YgTUZBIHZlcmlmaWNhdGlvbiBieSBwcm92aWRpbmcgdGhlIG9uZS10aW1lIHBhc3N3b3JkLiBUbyBiZWdpbiB0aGUgZmxvdywgdXNlIFtjcmVhdGVNZmFDaGFsbGVuZ2VdKC9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZU1mYUNoYWxsZW5nZSkgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNoYWxsZW5nZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG90cFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICB1cGRhdGVNZmFDaGFsbGVuZ2UoY2hhbGxlbmdlSWQsIG90cCkge1xuICAgICAgICBpZiAodHlwZW9mIGNoYWxsZW5nZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjaGFsbGVuZ2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBvdHAgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm90cFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvY2hhbGxlbmdlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGNoYWxsZW5nZUlkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnY2hhbGxlbmdlSWQnXSA9IGNoYWxsZW5nZUlkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygb3RwICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnb3RwJ10gPSBvdHA7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IHRoZSBmYWN0b3JzIGF2YWlsYWJsZSBvbiB0aGUgYWNjb3VudCB0byBiZSB1c2VkIGFzIGEgTUZBIGNoYWxsYW5nZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZmFGYWN0b3JzPn1cbiAgICAgKi9cbiAgICBsaXN0TWZhRmFjdG9ycygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvZmFjdG9ycyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHJlY292ZXJ5IGNvZGVzIHRoYXQgY2FuIGJlIHVzZWQgYXMgYmFja3VwIGZvciBNRkEgZmxvdy4gQmVmb3JlIGdldHRpbmcgY29kZXMsIHRoZXkgbXVzdCBiZSBnZW5lcmF0ZWQgdXNpbmcgW2NyZWF0ZU1mYVJlY292ZXJ5Q29kZXNdKC9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZU1mYVJlY292ZXJ5Q29kZXMpIG1ldGhvZC4gQW4gT1RQIGNoYWxsZW5nZSBpcyByZXF1aXJlZCB0byByZWFkIHJlY292ZXJ5IGNvZGVzLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1mYVJlY292ZXJ5Q29kZXM+fVxuICAgICAqL1xuICAgIGdldE1mYVJlY292ZXJ5Q29kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL3JlY292ZXJ5LWNvZGVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZW5lcmF0ZSByZWNvdmVyeSBjb2RlcyBhcyBiYWNrdXAgZm9yIE1GQSBmbG93LiBJdCYjMDM5O3MgcmVjb21tZW5kZWQgdG8gZ2VuZXJhdGUgYW5kIHNob3cgdGhlbiBpbW1lZGlhdGVseSBhZnRlciB1c2VyIHN1Y2Nlc3NmdWxseSBhZGRzIHRoZWlyIGF1dGhlaHRpY2F0b3IuIFJlY292ZXJ5IGNvZGVzIGNhbiBiZSB1c2VkIGFzIGEgTUZBIHZlcmlmaWNhdGlvbiB0eXBlIGluIFtjcmVhdGVNZmFDaGFsbGVuZ2VdKC9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZU1mYUNoYWxsZW5nZSkgbWV0aG9kLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1mYVJlY292ZXJ5Q29kZXM+fVxuICAgICAqL1xuICAgIGNyZWF0ZU1mYVJlY292ZXJ5Q29kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvbWZhL3JlY292ZXJ5LWNvZGVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWdlbmVyYXRlIHJlY292ZXJ5IGNvZGVzIHRoYXQgY2FuIGJlIHVzZWQgYXMgYmFja3VwIGZvciBNRkEgZmxvdy4gQmVmb3JlIHJlZ2VuZXJhdGluZyBjb2RlcywgdGhleSBtdXN0IGJlIGZpcnN0IGdlbmVyYXRlZCB1c2luZyBbY3JlYXRlTWZhUmVjb3ZlcnlDb2Rlc10oL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlTWZhUmVjb3ZlcnlDb2RlcykgbWV0aG9kLiBBbiBPVFAgY2hhbGxlbmdlIGlzIHJlcXVpcmVkIHRvIHJlZ2VucmVhdGUgcmVjb3ZlcnkgY29kZXMuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWZhUmVjb3ZlcnlDb2Rlcz59XG4gICAgICovXG4gICAgdXBkYXRlTWZhUmVjb3ZlcnlDb2RlcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9tZmEvcmVjb3ZlcnktY29kZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVcGRhdGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyIGFjY291bnQgbmFtZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlVzZXI8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICB1cGRhdGVOYW1lKG5hbWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJuYW1lXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L25hbWUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgcGFzc3dvcmQuIEZvciB2YWxpZGF0aW9uLCB1c2VyIGlzIHJlcXVpcmVkIHRvIHBhc3MgaW4gdGhlIG5ldyBwYXNzd29yZCwgYW5kIHRoZSBvbGQgcGFzc3dvcmQuIEZvciB1c2VycyBjcmVhdGVkIHdpdGggT0F1dGgsIFRlYW0gSW52aXRlcyBhbmQgTWFnaWMgVVJMLCBvbGRQYXNzd29yZCBpcyBvcHRpb25hbC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwYXNzd29yZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBvbGRQYXNzd29yZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlUGFzc3dvcmQocGFzc3dvcmQsIG9sZFBhc3N3b3JkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgcGFzc3dvcmQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBhc3N3b3JkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Bhc3N3b3JkJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygb2xkUGFzc3dvcmQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydvbGRQYXNzd29yZCddID0gb2xkUGFzc3dvcmQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyJiMwMzk7cyBwaG9uZSBudW1iZXIuIEFmdGVyIHVwZGF0aW5nIHRoZSBwaG9uZSBudW1iZXIsIHRoZSBwaG9uZSB2ZXJpZmljYXRpb24gc3RhdHVzIHdpbGwgYmUgcmVzZXQuIEEgY29uZmlybWF0aW9uIFNNUyBpcyBub3Qgc2VudCBhdXRvbWF0aWNhbGx5LCBob3dldmVyIHlvdSBjYW4gdXNlIHRoZSBbUE9TVCAvYWNjb3VudC92ZXJpZmljYXRpb24vcGhvbmVdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVQaG9uZVZlcmlmaWNhdGlvbikgZW5kcG9pbnQgdG8gc2VuZCBhIGNvbmZpcm1hdGlvbiBTTVMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGhvbmVcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcGFzc3dvcmRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVBob25lKHBob25lLCBwYXNzd29yZCkge1xuICAgICAgICBpZiAodHlwZW9mIHBob25lID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwaG9uZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcGhvbmUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaG9uZSddID0gcGhvbmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Bhc3N3b3JkJ10gPSBwYXNzd29yZDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBwcmVmZXJlbmNlcyBhcyBhIGtleS12YWx1ZSBvYmplY3QgZm9yIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxQcmVmZXJlbmNlcz59XG4gICAgICovXG4gICAgZ2V0UHJlZnMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcHJlZnMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIgYWNjb3VudCBwcmVmZXJlbmNlcy4gVGhlIG9iamVjdCB5b3UgcGFzcyBpcyBzdG9yZWQgYXMgaXMsIGFuZCByZXBsYWNlcyBhbnkgcHJldmlvdXMgdmFsdWUuIFRoZSBtYXhpbXVtIGFsbG93ZWQgcHJlZnMgc2l6ZSBpcyA2NGtCIGFuZCB0aHJvd3MgZXJyb3IgaWYgZXhjZWVkZWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge1BhcnRpYWw8UHJlZmVyZW5jZXM+fSBwcmVmc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Vc2VyPFByZWZlcmVuY2VzPj59XG4gICAgICovXG4gICAgdXBkYXRlUHJlZnMocHJlZnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBwcmVmcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicHJlZnNcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcHJlZnMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcHJlZnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwcmVmcyddID0gcHJlZnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIHRoZSB1c2VyIGFuIGVtYWlsIHdpdGggYSB0ZW1wb3Jhcnkgc2VjcmV0IGtleSBmb3IgcGFzc3dvcmQgcmVzZXQuIFdoZW4gdGhlIHVzZXIgY2xpY2tzIHRoZSBjb25maXJtYXRpb24gbGluayBoZSBpcyByZWRpcmVjdGVkIGJhY2sgdG8geW91ciBhcHAgcGFzc3dvcmQgcmVzZXQgVVJMIHdpdGggdGhlIHNlY3JldCBrZXkgYW5kIGVtYWlsIGFkZHJlc3MgdmFsdWVzIGF0dGFjaGVkIHRvIHRoZSBVUkwgcXVlcnkgc3RyaW5nLiBVc2UgdGhlIHF1ZXJ5IHN0cmluZyBwYXJhbXMgdG8gc3VibWl0IGEgcmVxdWVzdCB0byB0aGUgW1BVVCAvYWNjb3VudC9yZWNvdmVyeV0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I3VwZGF0ZVJlY292ZXJ5KSBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgcHJvY2Vzcy4gVGhlIHZlcmlmaWNhdGlvbiBsaW5rIHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIGVtYWlsIGFkZHJlc3MgaXMgdmFsaWQgZm9yIDEgaG91ci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIGNyZWF0ZVJlY292ZXJ5KGVtYWlsLCB1cmwpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZW1haWxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdXJsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1cmxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcmVjb3ZlcnknO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydlbWFpbCddID0gZW1haWw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgdXNlciBhY2NvdW50IHBhc3N3b3JkIHJlc2V0LiBCb3RoIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIGFyZ3VtZW50cyB3aWxsIGJlIHBhc3NlZCBhcyBxdWVyeSBwYXJhbWV0ZXJzIHRvIHRoZSByZWRpcmVjdCBVUkwgeW91IGhhdmUgcHJvdmlkZWQgd2hlbiBzZW5kaW5nIHlvdXIgcmVxdWVzdCB0byB0aGUgW1BPU1QgL2FjY291bnQvcmVjb3ZlcnldKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNjcmVhdGVSZWNvdmVyeSkgZW5kcG9pbnQuXG5cblBsZWFzZSBub3RlIHRoYXQgaW4gb3JkZXIgdG8gYXZvaWQgYSBbUmVkaXJlY3QgQXR0YWNrXShodHRwczovL2dpdGh1Yi5jb20vT1dBU1AvQ2hlYXRTaGVldFNlcmllcy9ibG9iL21hc3Rlci9jaGVhdHNoZWV0cy9VbnZhbGlkYXRlZF9SZWRpcmVjdHNfYW5kX0ZvcndhcmRzX0NoZWF0X1NoZWV0Lm1kKSB0aGUgb25seSB2YWxpZCByZWRpcmVjdCBVUkxzIGFyZSB0aGUgb25lcyBmcm9tIGRvbWFpbnMgeW91IGhhdmUgc2V0IHdoZW4gYWRkaW5nIHlvdXIgcGxhdGZvcm1zIGluIHRoZSBjb25zb2xlIGludGVyZmFjZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBhc3N3b3JkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICB1cGRhdGVSZWNvdmVyeSh1c2VySWQsIHNlY3JldCwgcGFzc3dvcmQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvcmVjb3ZlcnknO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhc3N3b3JkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGFzc3dvcmQnXSA9IHBhc3N3b3JkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBsaXN0IG9mIGFjdGl2ZSBzZXNzaW9ucyBhY3Jvc3MgZGlmZmVyZW50IGRldmljZXMgZm9yIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXIuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbkxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RTZXNzaW9ucygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGFsbCBzZXNzaW9ucyBmcm9tIHRoZSB1c2VyIGFjY291bnQgYW5kIHJlbW92ZSBhbnkgc2Vzc2lvbnMgY29va2llcyBmcm9tIHRoZSBlbmQgY2xpZW50LlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZVNlc3Npb25zKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGFsbG93IGEgbmV3IHVzZXIgdG8gcmVnaXN0ZXIgYW4gYW5vbnltb3VzIGFjY291bnQgaW4geW91ciBwcm9qZWN0LiBUaGlzIHJvdXRlIHdpbGwgYWxzbyBjcmVhdGUgYSBuZXcgc2Vzc2lvbiBmb3IgdGhlIHVzZXIuIFRvIGFsbG93IHRoZSBuZXcgdXNlciB0byBjb252ZXJ0IGFuIGFub255bW91cyBhY2NvdW50IHRvIGEgbm9ybWFsIGFjY291bnQsIHlvdSBuZWVkIHRvIHVwZGF0ZSBpdHMgW2VtYWlsIGFuZCBwYXNzd29yZF0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I3VwZGF0ZUVtYWlsKSBvciBjcmVhdGUgYW4gW09BdXRoMiBzZXNzaW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjQ3JlYXRlT0F1dGgyU2Vzc2lvbikuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgY3JlYXRlQW5vbnltb3VzU2Vzc2lvbigpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9hbm9ueW1vdXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFsbG93IHRoZSB1c2VyIHRvIGxvZ2luIGludG8gdGhlaXIgYWNjb3VudCBieSBwcm92aWRpbmcgYSB2YWxpZCBlbWFpbCBhbmQgcGFzc3dvcmQgY29tYmluYXRpb24uIFRoaXMgcm91dGUgd2lsbCBjcmVhdGUgYSBuZXcgc2Vzc2lvbiBmb3IgdGhlIHVzZXIuXG5cbkEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHBhc3N3b3JkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIGNyZWF0ZUVtYWlsUGFzc3dvcmRTZXNzaW9uKGVtYWlsLCBwYXNzd29yZCkge1xuICAgICAgICBpZiAodHlwZW9mIGVtYWlsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJlbWFpbFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicGFzc3dvcmRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMvZW1haWwnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydlbWFpbCddID0gZW1haWw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwYXNzd29yZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Bhc3N3b3JkJ10gPSBwYXNzd29yZDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjcmVhdGUgYSBzZXNzaW9uIGZyb20gdG9rZW4uIFByb3ZpZGUgdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogcGFyYW1ldGVycyBmcm9tIHRoZSBzdWNjZXNzZnVsIHJlc3BvbnNlIG9mIGF1dGhlbnRpY2F0aW9uIGZsb3dzIGluaXRpYXRlZCBieSB0b2tlbiBjcmVhdGlvbi4gRm9yIGV4YW1wbGUsIG1hZ2ljIFVSTCBhbmQgcGhvbmUgbG9naW4uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICB1cGRhdGVNYWdpY1VSTFNlc3Npb24odXNlcklkLCBzZWNyZXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9tYWdpYy11cmwnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFsbG93IHRoZSB1c2VyIHRvIGxvZ2luIHRvIHRoZWlyIGFjY291bnQgdXNpbmcgdGhlIE9BdXRoMiBwcm92aWRlciBvZiB0aGVpciBjaG9pY2UuIEVhY2ggT0F1dGgyIHByb3ZpZGVyIHNob3VsZCBiZSBlbmFibGVkIGZyb20gdGhlIEFwcHdyaXRlIGNvbnNvbGUgZmlyc3QuIFVzZSB0aGUgc3VjY2VzcyBhbmQgZmFpbHVyZSBhcmd1bWVudHMgdG8gcHJvdmlkZSBhIHJlZGlyZWN0IFVSTCYjMDM5O3MgYmFjayB0byB5b3VyIGFwcCB3aGVuIGxvZ2luIGlzIGNvbXBsZXRlZC5cblxuSWYgdGhlcmUgaXMgYWxyZWFkeSBhbiBhY3RpdmUgc2Vzc2lvbiwgdGhlIG5ldyBzZXNzaW9uIHdpbGwgYmUgYXR0YWNoZWQgdG8gdGhlIGxvZ2dlZC1pbiBhY2NvdW50LiBJZiB0aGVyZSBhcmUgbm8gYWN0aXZlIHNlc3Npb25zLCB0aGUgc2VydmVyIHdpbGwgYXR0ZW1wdCB0byBsb29rIGZvciBhIHVzZXIgd2l0aCB0aGUgc2FtZSBlbWFpbCBhZGRyZXNzIGFzIHRoZSBlbWFpbCByZWNlaXZlZCBmcm9tIHRoZSBPQXV0aDIgcHJvdmlkZXIgYW5kIGF0dGFjaCB0aGUgbmV3IHNlc3Npb24gdG8gdGhlIGV4aXN0aW5nIHVzZXIuIElmIG5vIG1hdGNoaW5nIHVzZXIgaXMgZm91bmQgLSB0aGUgc2VydmVyIHdpbGwgY3JlYXRlIGEgbmV3IHVzZXIuXG5cbkEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge09BdXRoUHJvdmlkZXJ9IHByb3ZpZGVyXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN1Y2Nlc3NcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmFpbHVyZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHNjb3Blc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHt2b2lkIHwgc3RyaW5nfVxuICAgICAqL1xuICAgIGNyZWF0ZU9BdXRoMlNlc3Npb24ocHJvdmlkZXIsIHN1Y2Nlc3MsIGZhaWx1cmUsIHNjb3Blcykge1xuICAgICAgICBpZiAodHlwZW9mIHByb3ZpZGVyID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwcm92aWRlclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy9vYXV0aDIve3Byb3ZpZGVyfScucmVwbGFjZSgne3Byb3ZpZGVyfScsIHByb3ZpZGVyKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHN1Y2Nlc3MgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzdWNjZXNzJ10gPSBzdWNjZXNzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmFpbHVyZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZhaWx1cmUnXSA9IGZhaWx1cmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzY29wZXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzY29wZXMnXSA9IHNjb3BlcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICh3aW5kb3cgPT09IG51bGwgfHwgd2luZG93ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB3aW5kb3cubG9jYXRpb24pKSB7XG4gICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVyaS50b1N0cmluZygpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNyZWF0ZSBhIHNlc3Npb24gZnJvbSB0b2tlbi4gUHJvdmlkZSB0aGUgKip1c2VySWQqKiBhbmQgKipzZWNyZXQqKiBwYXJhbWV0ZXJzIGZyb20gdGhlIHN1Y2Nlc3NmdWwgcmVzcG9uc2Ugb2YgYXV0aGVudGljYXRpb24gZmxvd3MgaW5pdGlhdGVkIGJ5IHRva2VuIGNyZWF0aW9uLiBGb3IgZXhhbXBsZSwgbWFnaWMgVVJMIGFuZCBwaG9uZSBsb2dpbi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIHVwZGF0ZVBob25lU2Vzc2lvbih1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Nlc3Npb25zL3Bob25lJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjcmVhdGUgYSBzZXNzaW9uIGZyb20gdG9rZW4uIFByb3ZpZGUgdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogcGFyYW1ldGVycyBmcm9tIHRoZSBzdWNjZXNzZnVsIHJlc3BvbnNlIG9mIGF1dGhlbnRpY2F0aW9uIGZsb3dzIGluaXRpYXRlZCBieSB0b2tlbiBjcmVhdGlvbi4gRm9yIGV4YW1wbGUsIG1hZ2ljIFVSTCBhbmQgcGhvbmUgbG9naW4uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5TZXNzaW9uPn1cbiAgICAgKi9cbiAgICBjcmVhdGVTZXNzaW9uKHVzZXJJZCwgc2VjcmV0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZWNyZXRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMvdG9rZW4nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBnZXQgYSBsb2dnZWQgaW4gdXNlciYjMDM5O3Mgc2Vzc2lvbiB1c2luZyBhIFNlc3Npb24gSUQuIElucHV0dGluZyAmIzAzOTtjdXJyZW50JiMwMzk7IHdpbGwgcmV0dXJuIHRoZSBjdXJyZW50IHNlc3Npb24gYmVpbmcgdXNlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZXNzaW9uSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU2Vzc2lvbj59XG4gICAgICovXG4gICAgZ2V0U2Vzc2lvbihzZXNzaW9uSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBzZXNzaW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlc3Npb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy97c2Vzc2lvbklkfScucmVwbGFjZSgne3Nlc3Npb25JZH0nLCBzZXNzaW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGV4dGVuZCBhIHNlc3Npb24mIzAzOTtzIGxlbmd0aC4gRXh0ZW5kaW5nIGEgc2Vzc2lvbiBpcyB1c2VmdWwgd2hlbiBzZXNzaW9uIGV4cGlyeSBpcyBzaG9ydC4gSWYgdGhlIHNlc3Npb24gd2FzIGNyZWF0ZWQgdXNpbmcgYW4gT0F1dGggcHJvdmlkZXIsIHRoaXMgZW5kcG9pbnQgcmVmcmVzaGVzIHRoZSBhY2Nlc3MgdG9rZW4gZnJvbSB0aGUgcHJvdmlkZXIuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2Vzc2lvbklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlNlc3Npb24+fVxuICAgICAqL1xuICAgIHVwZGF0ZVNlc3Npb24oc2Vzc2lvbklkKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygc2Vzc2lvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZXNzaW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2FjY291bnQvc2Vzc2lvbnMve3Nlc3Npb25JZH0nLnJlcGxhY2UoJ3tzZXNzaW9uSWR9Jywgc2Vzc2lvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3BhdGNoJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTG9nb3V0IHRoZSB1c2VyLiBVc2UgJiMwMzk7Y3VycmVudCYjMDM5OyBhcyB0aGUgc2Vzc2lvbiBJRCB0byBsb2dvdXQgb24gdGhpcyBkZXZpY2UsIHVzZSBhIHNlc3Npb24gSUQgdG8gbG9nb3V0IG9uIGFub3RoZXIgZGV2aWNlLiBJZiB5b3UmIzAzOTtyZSBsb29raW5nIHRvIGxvZ291dCB0aGUgdXNlciBvbiBhbGwgZGV2aWNlcywgdXNlIFtEZWxldGUgU2Vzc2lvbnNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNkZWxldGVTZXNzaW9ucykgaW5zdGVhZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZXNzaW9uSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlU2Vzc2lvbihzZXNzaW9uSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBzZXNzaW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlc3Npb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zZXNzaW9ucy97c2Vzc2lvbklkfScucmVwbGFjZSgne3Nlc3Npb25JZH0nLCBzZXNzaW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQmxvY2sgdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlciBhY2NvdW50LiBCZWhpbmQgdGhlIHNjZW5lLCB0aGUgdXNlciByZWNvcmQgaXMgbm90IGRlbGV0ZWQgYnV0IHBlcm1hbmVudGx5IGJsb2NrZWQgZnJvbSBhbnkgYWNjZXNzLiBUbyBjb21wbGV0ZWx5IGRlbGV0ZSBhIHVzZXIsIHVzZSB0aGUgVXNlcnMgQVBJIGluc3RlYWQuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVXNlcjxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVN0YXR1cygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC9zdGF0dXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byByZWdpc3RlciBhIGRldmljZSBmb3IgcHVzaCBub3RpZmljYXRpb25zLiBQcm92aWRlIGEgdGFyZ2V0IElEIChjdXN0b20gb3IgZ2VuZXJhdGVkIHVzaW5nIElELnVuaXF1ZSgpKSwgYSBkZXZpY2UgaWRlbnRpZmllciAodXN1YWxseSBhIGRldmljZSB0b2tlbiksIGFuZCBvcHRpb25hbGx5IHNwZWNpZnkgd2hpY2ggcHJvdmlkZXIgc2hvdWxkIHNlbmQgbm90aWZpY2F0aW9ucyB0byB0aGlzIHRhcmdldC4gVGhlIHRhcmdldCBpcyBhdXRvbWF0aWNhbGx5IGxpbmtlZCB0byB0aGUgY3VycmVudCBzZXNzaW9uIGFuZCBpbmNsdWRlcyBkZXZpY2UgaW5mb3JtYXRpb24gbGlrZSBicmFuZCBhbmQgbW9kZWwuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRlbnRpZmllclxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwcm92aWRlcklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRhcmdldD59XG4gICAgICovXG4gICAgY3JlYXRlUHVzaFRhcmdldCh0YXJnZXRJZCwgaWRlbnRpZmllciwgcHJvdmlkZXJJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRhcmdldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0YXJnZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBpZGVudGlmaWVyID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJpZGVudGlmaWVyXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3RhcmdldHMvcHVzaCc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3RhcmdldElkJ10gPSB0YXJnZXRJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGlkZW50aWZpZXIgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydpZGVudGlmaWVyJ10gPSBpZGVudGlmaWVyO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcHJvdmlkZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Byb3ZpZGVySWQnXSA9IHByb3ZpZGVySWQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIHRoZSBjdXJyZW50bHkgbG9nZ2VkIGluIHVzZXImIzAzOTtzIHB1c2ggbm90aWZpY2F0aW9uIHRhcmdldC4gWW91IGNhbiBtb2RpZnkgdGhlIHRhcmdldCYjMDM5O3MgaWRlbnRpZmllciAoZGV2aWNlIHRva2VuKSBhbmQgcHJvdmlkZXIgSUQgKHRva2VuLCBlbWFpbCwgcGhvbmUgZXRjLikuIFRoZSB0YXJnZXQgbXVzdCBleGlzdCBhbmQgYmVsb25nIHRvIHRoZSBjdXJyZW50IHVzZXIuIElmIHlvdSBjaGFuZ2UgdGhlIHByb3ZpZGVyIElELCBub3RpZmljYXRpb25zIHdpbGwgYmUgc2VudCB0aHJvdWdoIHRoZSBuZXcgbWVzc2FnaW5nIHByb3ZpZGVyIGluc3RlYWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRlbnRpZmllclxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5UYXJnZXQ+fVxuICAgICAqL1xuICAgIHVwZGF0ZVB1c2hUYXJnZXQodGFyZ2V0SWQsIGlkZW50aWZpZXIpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGFyZ2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgaWRlbnRpZmllciA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiaWRlbnRpZmllclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC90YXJnZXRzL3t0YXJnZXRJZH0vcHVzaCcucmVwbGFjZSgne3RhcmdldElkfScsIHRhcmdldElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGlkZW50aWZpZXIgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydpZGVudGlmaWVyJ10gPSBpZGVudGlmaWVyO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgcHVzaCBub3RpZmljYXRpb24gdGFyZ2V0IGZvciB0aGUgY3VycmVudGx5IGxvZ2dlZCBpbiB1c2VyLiBBZnRlciBkZWxldGlvbiwgdGhlIGRldmljZSB3aWxsIG5vIGxvbmdlciByZWNlaXZlIHB1c2ggbm90aWZpY2F0aW9ucy4gVGhlIHRhcmdldCBtdXN0IGV4aXN0IGFuZCBiZWxvbmcgdG8gdGhlIGN1cnJlbnQgdXNlci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0YXJnZXRJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVQdXNoVGFyZ2V0KHRhcmdldElkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRhcmdldElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3RhcmdldHMve3RhcmdldElkfS9wdXNoJy5yZXBsYWNlKCd7dGFyZ2V0SWR9JywgdGFyZ2V0SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VuZHMgdGhlIHVzZXIgYW4gZW1haWwgd2l0aCBhIHNlY3JldCBrZXkgZm9yIGNyZWF0aW5nIGEgc2Vzc2lvbi4gSWYgdGhlIHByb3ZpZGVkIHVzZXIgSUQgaGFzIG5vdCBiZSByZWdpc3RlcmVkLCBhIG5ldyB1c2VyIHdpbGwgYmUgY3JlYXRlZC4gVXNlIHRoZSByZXR1cm5lZCB1c2VyIElEIGFuZCBzZWNyZXQgYW5kIHN1Ym1pdCBhIHJlcXVlc3QgdG8gdGhlIFtQT1NUIC92MS9hY2NvdW50L3Nlc3Npb25zL3Rva2VuXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlU2Vzc2lvbikgZW5kcG9pbnQgdG8gY29tcGxldGUgdGhlIGxvZ2luIHByb2Nlc3MuIFRoZSBzZWNyZXQgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgZW1haWwgaXMgdmFsaWQgZm9yIDE1IG1pbnV0ZXMuXG5cbkEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBlbWFpbFxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gcGhyYXNlXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICBjcmVhdGVFbWFpbFRva2VuKHVzZXJJZCwgZW1haWwsIHBocmFzZSkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGVtYWlsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJlbWFpbFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC90b2tlbnMvZW1haWwnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBocmFzZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3BocmFzZSddID0gcGhyYXNlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIHRoZSB1c2VyIGFuIGVtYWlsIHdpdGggYSBzZWNyZXQga2V5IGZvciBjcmVhdGluZyBhIHNlc3Npb24uIElmIHRoZSBwcm92aWRlZCB1c2VyIElEIGhhcyBub3QgYmVlbiByZWdpc3RlcmVkLCBhIG5ldyB1c2VyIHdpbGwgYmUgY3JlYXRlZC4gV2hlbiB0aGUgdXNlciBjbGlja3MgdGhlIGxpbmsgaW4gdGhlIGVtYWlsLCB0aGUgdXNlciBpcyByZWRpcmVjdGVkIGJhY2sgdG8gdGhlIFVSTCB5b3UgcHJvdmlkZWQgd2l0aCB0aGUgc2VjcmV0IGtleSBhbmQgdXNlcklkIHZhbHVlcyBhdHRhY2hlZCB0byB0aGUgVVJMIHF1ZXJ5IHN0cmluZy4gVXNlIHRoZSBxdWVyeSBzdHJpbmcgcGFyYW1ldGVycyB0byBzdWJtaXQgYSByZXF1ZXN0IHRvIHRoZSBbUE9TVCAvdjEvYWNjb3VudC9zZXNzaW9ucy90b2tlbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVNlc3Npb24pIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSBsb2dpbiBwcm9jZXNzLiBUaGUgbGluayBzZW50IHRvIHRoZSB1c2VyJiMwMzk7cyBlbWFpbCBhZGRyZXNzIGlzIHZhbGlkIGZvciAxIGhvdXIuXG5cbkEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gcGhyYXNlXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNYWdpY1VSTFRva2VuKHVzZXJJZCwgZW1haWwsIHVybCwgcGhyYXNlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZW1haWwgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImVtYWlsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Rva2Vucy9tYWdpYy11cmwnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhyYXNlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGhyYXNlJ10gPSBwaHJhc2U7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQWxsb3cgdGhlIHVzZXIgdG8gbG9naW4gdG8gdGhlaXIgYWNjb3VudCB1c2luZyB0aGUgT0F1dGgyIHByb3ZpZGVyIG9mIHRoZWlyIGNob2ljZS4gRWFjaCBPQXV0aDIgcHJvdmlkZXIgc2hvdWxkIGJlIGVuYWJsZWQgZnJvbSB0aGUgQXBwd3JpdGUgY29uc29sZSBmaXJzdC4gVXNlIHRoZSBzdWNjZXNzIGFuZCBmYWlsdXJlIGFyZ3VtZW50cyB0byBwcm92aWRlIGEgcmVkaXJlY3QgVVJMJiMwMzk7cyBiYWNrIHRvIHlvdXIgYXBwIHdoZW4gbG9naW4gaXMgY29tcGxldGVkLlxuXG5JZiBhdXRoZW50aWNhdGlvbiBzdWNjZWVkcywgYHVzZXJJZGAgYW5kIGBzZWNyZXRgIG9mIGEgdG9rZW4gd2lsbCBiZSBhcHBlbmRlZCB0byB0aGUgc3VjY2VzcyBVUkwgYXMgcXVlcnkgcGFyYW1ldGVycy4gVGhlc2UgY2FuIGJlIHVzZWQgdG8gY3JlYXRlIGEgbmV3IHNlc3Npb24gdXNpbmcgdGhlIFtDcmVhdGUgc2Vzc2lvbl0oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2NyZWF0ZVNlc3Npb24pIGVuZHBvaW50LlxuXG5BIHVzZXIgaXMgbGltaXRlZCB0byAxMCBhY3RpdmUgc2Vzc2lvbnMgYXQgYSB0aW1lIGJ5IGRlZmF1bHQuIFtMZWFybiBtb3JlIGFib3V0IHNlc3Npb24gbGltaXRzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvYXV0aGVudGljYXRpb24tc2VjdXJpdHkjbGltaXRzKS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7T0F1dGhQcm92aWRlcn0gcHJvdmlkZXJcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3VjY2Vzc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmYWlsdXJlXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gc2NvcGVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3ZvaWQgfCBzdHJpbmd9XG4gICAgICovXG4gICAgY3JlYXRlT0F1dGgyVG9rZW4ocHJvdmlkZXIsIHN1Y2Nlc3MsIGZhaWx1cmUsIHNjb3Blcykge1xuICAgICAgICBpZiAodHlwZW9mIHByb3ZpZGVyID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJwcm92aWRlclwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC90b2tlbnMvb2F1dGgyL3twcm92aWRlcn0nLnJlcGxhY2UoJ3twcm92aWRlcn0nLCBwcm92aWRlcik7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBzdWNjZXNzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc3VjY2VzcyddID0gc3VjY2VzcztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGZhaWx1cmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydmYWlsdXJlJ10gPSBmYWlsdXJlO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2NvcGVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2NvcGVzJ10gPSBzY29wZXM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAod2luZG93ID09PSBudWxsIHx8IHdpbmRvdyA9PT0gdm9pZCAwID8gdm9pZCAwIDogd2luZG93LmxvY2F0aW9uKSkge1xuICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSB1cmkudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZW5kcyB0aGUgdXNlciBhbiBTTVMgd2l0aCBhIHNlY3JldCBrZXkgZm9yIGNyZWF0aW5nIGEgc2Vzc2lvbi4gSWYgdGhlIHByb3ZpZGVkIHVzZXIgSUQgaGFzIG5vdCBiZSByZWdpc3RlcmVkLCBhIG5ldyB1c2VyIHdpbGwgYmUgY3JlYXRlZC4gVXNlIHRoZSByZXR1cm5lZCB1c2VyIElEIGFuZCBzZWNyZXQgYW5kIHN1Ym1pdCBhIHJlcXVlc3QgdG8gdGhlIFtQT1NUIC92MS9hY2NvdW50L3Nlc3Npb25zL3Rva2VuXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjY3JlYXRlU2Vzc2lvbikgZW5kcG9pbnQgdG8gY29tcGxldGUgdGhlIGxvZ2luIHByb2Nlc3MuIFRoZSBzZWNyZXQgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgcGhvbmUgaXMgdmFsaWQgZm9yIDE1IG1pbnV0ZXMuXG5cbkEgdXNlciBpcyBsaW1pdGVkIHRvIDEwIGFjdGl2ZSBzZXNzaW9ucyBhdCBhIHRpbWUgYnkgZGVmYXVsdC4gW0xlYXJuIG1vcmUgYWJvdXQgc2Vzc2lvbiBsaW1pdHNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9hdXRoZW50aWNhdGlvbi1zZWN1cml0eSNsaW1pdHMpLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwaG9uZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlUGhvbmVUb2tlbih1c2VySWQsIHBob25lKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInBob25lXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3Rva2Vucy9waG9uZSc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBob25lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGhvbmUnXSA9IHBob25lO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIHNlbmQgYSB2ZXJpZmljYXRpb24gbWVzc2FnZSB0byB5b3VyIHVzZXIgZW1haWwgYWRkcmVzcyB0byBjb25maXJtIHRoZXkgYXJlIHRoZSB2YWxpZCBvd25lcnMgb2YgdGhhdCBhZGRyZXNzLiBCb3RoIHRoZSAqKnVzZXJJZCoqIGFuZCAqKnNlY3JldCoqIGFyZ3VtZW50cyB3aWxsIGJlIHBhc3NlZCBhcyBxdWVyeSBwYXJhbWV0ZXJzIHRvIHRoZSBVUkwgeW91IGhhdmUgcHJvdmlkZWQgdG8gYmUgYXR0YWNoZWQgdG8gdGhlIHZlcmlmaWNhdGlvbiBlbWFpbC4gVGhlIHByb3ZpZGVkIFVSTCBzaG91bGQgcmVkaXJlY3QgdGhlIHVzZXIgYmFjayB0byB5b3VyIGFwcCBhbmQgYWxsb3cgeW91IHRvIGNvbXBsZXRlIHRoZSB2ZXJpZmljYXRpb24gcHJvY2VzcyBieSB2ZXJpZnlpbmcgYm90aCB0aGUgKip1c2VySWQqKiBhbmQgKipzZWNyZXQqKiBwYXJhbWV0ZXJzLiBMZWFybiBtb3JlIGFib3V0IGhvdyB0byBbY29tcGxldGUgdGhlIHZlcmlmaWNhdGlvbiBwcm9jZXNzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlVmVyaWZpY2F0aW9uKS4gVGhlIHZlcmlmaWNhdGlvbiBsaW5rIHNlbnQgdG8gdGhlIHVzZXImIzAzOTtzIGVtYWlsIGFkZHJlc3MgaXMgdmFsaWQgZm9yIDcgZGF5cy5cblxuUGxlYXNlIG5vdGUgdGhhdCBpbiBvcmRlciB0byBhdm9pZCBhIFtSZWRpcmVjdCBBdHRhY2tdKGh0dHBzOi8vZ2l0aHViLmNvbS9PV0FTUC9DaGVhdFNoZWV0U2VyaWVzL2Jsb2IvbWFzdGVyL2NoZWF0c2hlZXRzL1VudmFsaWRhdGVkX1JlZGlyZWN0c19hbmRfRm9yd2FyZHNfQ2hlYXRfU2hlZXQubWQpLCB0aGUgb25seSB2YWxpZCByZWRpcmVjdCBVUkxzIGFyZSB0aGUgb25lcyBmcm9tIGRvbWFpbnMgeW91IGhhdmUgc2V0IHdoZW4gYWRkaW5nIHlvdXIgcGxhdGZvcm1zIGluIHRoZSBjb25zb2xlIGludGVyZmFjZS5cblxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgY3JlYXRlVmVyaWZpY2F0aW9uKHVybCkge1xuICAgICAgICBpZiAodHlwZW9mIHVybCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXJsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3ZlcmlmaWNhdGlvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBjb21wbGV0ZSB0aGUgdXNlciBlbWFpbCB2ZXJpZmljYXRpb24gcHJvY2Vzcy4gVXNlIGJvdGggdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogcGFyYW1ldGVycyB0aGF0IHdlcmUgYXR0YWNoZWQgdG8geW91ciBhcHAgVVJMIHRvIHZlcmlmeSB0aGUgdXNlciBlbWFpbCBvd25lcnNoaXAuIElmIGNvbmZpcm1lZCB0aGlzIHJvdXRlIHdpbGwgcmV0dXJuIGEgMjAwIHN0YXR1cyBjb2RlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWNyZXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVG9rZW4+fVxuICAgICAqL1xuICAgIHVwZGF0ZVZlcmlmaWNhdGlvbih1c2VySWQsIHNlY3JldCkge1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXNlcklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwic2VjcmV0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hY2NvdW50L3ZlcmlmaWNhdGlvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1c2VySWQnXSA9IHVzZXJJZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlY3JldCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlY3JldCddID0gc2VjcmV0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gc2VuZCBhIHZlcmlmaWNhdGlvbiBTTVMgdG8gdGhlIGN1cnJlbnRseSBsb2dnZWQgaW4gdXNlci4gVGhpcyBlbmRwb2ludCBpcyBtZWFudCBmb3IgdXNlIGFmdGVyIHVwZGF0aW5nIGEgdXNlciYjMDM5O3MgcGhvbmUgbnVtYmVyIHVzaW5nIHRoZSBbYWNjb3VudFVwZGF0ZVBob25lXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlUGhvbmUpIGVuZHBvaW50LiBMZWFybiBtb3JlIGFib3V0IGhvdyB0byBbY29tcGxldGUgdGhlIHZlcmlmaWNhdGlvbiBwcm9jZXNzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL2FjY291bnQjdXBkYXRlUGhvbmVWZXJpZmljYXRpb24pLiBUaGUgdmVyaWZpY2F0aW9uIGNvZGUgc2VudCB0byB0aGUgdXNlciYjMDM5O3MgcGhvbmUgbnVtYmVyIGlzIHZhbGlkIGZvciAxNSBtaW51dGVzLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRva2VuPn1cbiAgICAgKi9cbiAgICBjcmVhdGVQaG9uZVZlcmlmaWNhdGlvbigpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC92ZXJpZmljYXRpb24vcGhvbmUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGNvbXBsZXRlIHRoZSB1c2VyIHBob25lIHZlcmlmaWNhdGlvbiBwcm9jZXNzLiBVc2UgdGhlICoqdXNlcklkKiogYW5kICoqc2VjcmV0KiogdGhhdCB3ZXJlIHNlbnQgdG8geW91ciB1c2VyJiMwMzk7cyBwaG9uZSBudW1iZXIgdG8gdmVyaWZ5IHRoZSB1c2VyIGVtYWlsIG93bmVyc2hpcC4gSWYgY29uZmlybWVkIHRoaXMgcm91dGUgd2lsbCByZXR1cm4gYSAyMDAgc3RhdHVzIGNvZGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdXNlcklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlY3JldFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Ub2tlbj59XG4gICAgICovXG4gICAgdXBkYXRlUGhvbmVWZXJpZmljYXRpb24odXNlcklkLCBzZWNyZXQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInVzZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInNlY3JldFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYWNjb3VudC92ZXJpZmljYXRpb24vcGhvbmUnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndXNlcklkJ10gPSB1c2VySWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWNyZXQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWNyZXQnXSA9IHNlY3JldDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBBdmF0YXJzIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFlvdSBjYW4gdXNlIHRoaXMgZW5kcG9pbnQgdG8gc2hvdyBkaWZmZXJlbnQgYnJvd3NlciBpY29ucyB0byB5b3VyIHVzZXJzLiBUaGUgY29kZSBhcmd1bWVudCByZWNlaXZlcyB0aGUgYnJvd3NlciBjb2RlIGFzIGl0IGFwcGVhcnMgaW4geW91ciB1c2VyIFtHRVQgL2FjY291bnQvc2Vzc2lvbnNdKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9yZWZlcmVuY2VzL2Nsb3VkL2NsaWVudC13ZWIvYWNjb3VudCNnZXRTZXNzaW9ucykgZW5kcG9pbnQuIFVzZSB3aWR0aCwgaGVpZ2h0IGFuZCBxdWFsaXR5IGFyZ3VtZW50cyB0byBjaGFuZ2UgdGhlIG91dHB1dCBzZXR0aW5ncy5cblxuV2hlbiBvbmUgZGltZW5zaW9uIGlzIHNwZWNpZmllZCBhbmQgdGhlIG90aGVyIGlzIDAsIHRoZSBpbWFnZSBpcyBzY2FsZWQgd2l0aCBwcmVzZXJ2ZWQgYXNwZWN0IHJhdGlvLiBJZiBib3RoIGRpbWVuc2lvbnMgYXJlIDAsIHRoZSBBUEkgcHJvdmlkZXMgYW4gaW1hZ2UgYXQgc291cmNlIHF1YWxpdHkuIElmIGRpbWVuc2lvbnMgYXJlIG5vdCBzcGVjaWZpZWQsIHRoZSBkZWZhdWx0IHNpemUgb2YgaW1hZ2UgcmV0dXJuZWQgaXMgMTAweDEwMHB4LlxuICAgICAqXG4gICAgICogQHBhcmFtIHtCcm93c2VyfSBjb2RlXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHdpZHRoXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGhlaWdodFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBxdWFsaXR5XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRCcm93c2VyKGNvZGUsIHdpZHRoLCBoZWlnaHQsIHF1YWxpdHkpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBjb2RlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2RlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL2Jyb3dzZXJzL3tjb2RlfScucmVwbGFjZSgne2NvZGV9JywgY29kZSk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcXVhbGl0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1YWxpdHknXSA9IHF1YWxpdHk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVGhlIGNyZWRpdCBjYXJkIGVuZHBvaW50IHdpbGwgcmV0dXJuIHlvdSB0aGUgaWNvbiBvZiB0aGUgY3JlZGl0IGNhcmQgcHJvdmlkZXIgeW91IG5lZWQuIFVzZSB3aWR0aCwgaGVpZ2h0IGFuZCBxdWFsaXR5IGFyZ3VtZW50cyB0byBjaGFuZ2UgdGhlIG91dHB1dCBzZXR0aW5ncy5cblxuV2hlbiBvbmUgZGltZW5zaW9uIGlzIHNwZWNpZmllZCBhbmQgdGhlIG90aGVyIGlzIDAsIHRoZSBpbWFnZSBpcyBzY2FsZWQgd2l0aCBwcmVzZXJ2ZWQgYXNwZWN0IHJhdGlvLiBJZiBib3RoIGRpbWVuc2lvbnMgYXJlIDAsIHRoZSBBUEkgcHJvdmlkZXMgYW4gaW1hZ2UgYXQgc291cmNlIHF1YWxpdHkuIElmIGRpbWVuc2lvbnMgYXJlIG5vdCBzcGVjaWZpZWQsIHRoZSBkZWZhdWx0IHNpemUgb2YgaW1hZ2UgcmV0dXJuZWQgaXMgMTAweDEwMHB4LlxuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge0NyZWRpdENhcmR9IGNvZGVcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gd2lkdGhcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHF1YWxpdHlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldENyZWRpdENhcmQoY29kZSwgd2lkdGgsIGhlaWdodCwgcXVhbGl0eSkge1xuICAgICAgICBpZiAodHlwZW9mIGNvZGUgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvZGVcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvY3JlZGl0LWNhcmRzL3tjb2RlfScucmVwbGFjZSgne2NvZGV9JywgY29kZSk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcXVhbGl0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1YWxpdHknXSA9IHF1YWxpdHk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gZmV0Y2ggdGhlIGZhdm9yaXRlIGljb24gKEFLQSBmYXZpY29uKSBvZiBhbnkgcmVtb3RlIHdlYnNpdGUgVVJMLlxuXG5UaGlzIGVuZHBvaW50IGRvZXMgbm90IGZvbGxvdyBIVFRQIHJlZGlyZWN0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZhdmljb24odXJsKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdXJsID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1cmxcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvZmF2aWNvbic7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB1cmwgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd1cmwnXSA9IHVybDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBZb3UgY2FuIHVzZSB0aGlzIGVuZHBvaW50IHRvIHNob3cgZGlmZmVyZW50IGNvdW50cnkgZmxhZ3MgaWNvbnMgdG8geW91ciB1c2Vycy4gVGhlIGNvZGUgYXJndW1lbnQgcmVjZWl2ZXMgdGhlIDIgbGV0dGVyIGNvdW50cnkgY29kZS4gVXNlIHdpZHRoLCBoZWlnaHQgYW5kIHF1YWxpdHkgYXJndW1lbnRzIHRvIGNoYW5nZSB0aGUgb3V0cHV0IHNldHRpbmdzLiBDb3VudHJ5IGNvZGVzIGZvbGxvdyB0aGUgW0lTTyAzMTY2LTFdKGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0lTT18zMTY2LTEpIHN0YW5kYXJkLlxuXG5XaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyAxMDB4MTAwcHguXG5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7RmxhZ30gY29kZVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gcXVhbGl0eVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgZ2V0RmxhZyhjb2RlLCB3aWR0aCwgaGVpZ2h0LCBxdWFsaXR5KSB7XG4gICAgICAgIGlmICh0eXBlb2YgY29kZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29kZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvYXZhdGFycy9mbGFncy97Y29kZX0nLnJlcGxhY2UoJ3tjb2RlfScsIGNvZGUpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2Ygd2lkdGggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd3aWR0aCddID0gd2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWlnaHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydoZWlnaHQnXSA9IGhlaWdodDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHF1YWxpdHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWFsaXR5J10gPSBxdWFsaXR5O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVzZSB0aGlzIGVuZHBvaW50IHRvIGZldGNoIGEgcmVtb3RlIGltYWdlIFVSTCBhbmQgY3JvcCBpdCB0byBhbnkgaW1hZ2Ugc2l6ZSB5b3Ugd2FudC4gVGhpcyBlbmRwb2ludCBpcyB2ZXJ5IHVzZWZ1bCBpZiB5b3UgbmVlZCB0byBjcm9wIGFuZCBkaXNwbGF5IHJlbW90ZSBpbWFnZXMgaW4geW91ciBhcHAgb3IgaW4gY2FzZSB5b3Ugd2FudCB0byBtYWtlIHN1cmUgYSAzcmQgcGFydHkgaW1hZ2UgaXMgcHJvcGVybHkgc2VydmVkIHVzaW5nIGEgVExTIHByb3RvY29sLlxuXG5XaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyA0MDB4NDAwcHguXG5cblRoaXMgZW5kcG9pbnQgZG9lcyBub3QgZm9sbG93IEhUVFAgcmVkaXJlY3RzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVybFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEltYWdlKHVybCwgd2lkdGgsIGhlaWdodCkge1xuICAgICAgICBpZiAodHlwZW9mIHVybCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidXJsXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL2ltYWdlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygd2lkdGggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd3aWR0aCddID0gd2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWlnaHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydoZWlnaHQnXSA9IGhlaWdodDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBwYXlsb2FkWydwcm9qZWN0J10gPSB0aGlzLmNsaWVudC5jb25maWcucHJvamVjdDtcbiAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoU2VydmljZS5mbGF0dGVuKHBheWxvYWQpKSkge1xuICAgICAgICAgICAgdXJpLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVyaS50b1N0cmluZygpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBzaG93IHlvdXIgdXNlciBpbml0aWFscyBhdmF0YXIgaWNvbiBvbiB5b3VyIHdlYnNpdGUgb3IgYXBwLiBCeSBkZWZhdWx0LCB0aGlzIHJvdXRlIHdpbGwgdHJ5IHRvIHByaW50IHlvdXIgbG9nZ2VkLWluIHVzZXIgbmFtZSBvciBlbWFpbCBpbml0aWFscy4gWW91IGNhbiBhbHNvIG92ZXJ3cml0ZSB0aGUgdXNlciBuYW1lIGlmIHlvdSBwYXNzIHRoZSAmIzAzOTtuYW1lJiMwMzk7IHBhcmFtZXRlci4gSWYgbm8gbmFtZSBpcyBnaXZlbiBhbmQgbm8gdXNlciBpcyBsb2dnZWQsIGFuIGVtcHR5IGF2YXRhciB3aWxsIGJlIHJldHVybmVkLlxuXG5Zb3UgY2FuIHVzZSB0aGUgY29sb3IgYW5kIGJhY2tncm91bmQgcGFyYW1zIHRvIGNoYW5nZSB0aGUgYXZhdGFyIGNvbG9ycy4gQnkgZGVmYXVsdCwgYSByYW5kb20gdGhlbWUgd2lsbCBiZSBzZWxlY3RlZC4gVGhlIHJhbmRvbSB0aGVtZSB3aWxsIHBlcnNpc3QgZm9yIHRoZSB1c2VyJiMwMzk7cyBpbml0aWFscyB3aGVuIHJlbG9hZGluZyB0aGUgc2FtZSB0aGVtZSB3aWxsIGFsd2F5cyByZXR1cm4gZm9yIHRoZSBzYW1lIGluaXRpYWxzLlxuXG5XaGVuIG9uZSBkaW1lbnNpb24gaXMgc3BlY2lmaWVkIGFuZCB0aGUgb3RoZXIgaXMgMCwgdGhlIGltYWdlIGlzIHNjYWxlZCB3aXRoIHByZXNlcnZlZCBhc3BlY3QgcmF0aW8uIElmIGJvdGggZGltZW5zaW9ucyBhcmUgMCwgdGhlIEFQSSBwcm92aWRlcyBhbiBpbWFnZSBhdCBzb3VyY2UgcXVhbGl0eS4gSWYgZGltZW5zaW9ucyBhcmUgbm90IHNwZWNpZmllZCwgdGhlIGRlZmF1bHQgc2l6ZSBvZiBpbWFnZSByZXR1cm5lZCBpcyAxMDB4MTAwcHguXG5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHdpZHRoXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGhlaWdodFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBiYWNrZ3JvdW5kXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBnZXRJbml0aWFscyhuYW1lLCB3aWR0aCwgaGVpZ2h0LCBiYWNrZ3JvdW5kKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2F2YXRhcnMvaW5pdGlhbHMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYmFja2dyb3VuZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2JhY2tncm91bmQnXSA9IGJhY2tncm91bmQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ29udmVydHMgYSBnaXZlbiBwbGFpbiB0ZXh0IHRvIGEgUVIgY29kZSBpbWFnZS4gWW91IGNhbiB1c2UgdGhlIHF1ZXJ5IHBhcmFtZXRlcnMgdG8gY2hhbmdlIHRoZSBzaXplIGFuZCBzdHlsZSBvZiB0aGUgcmVzdWx0aW5nIGltYWdlLlxuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGV4dFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBzaXplXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IG1hcmdpblxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gZG93bmxvYWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldFFSKHRleHQsIHNpemUsIG1hcmdpbiwgZG93bmxvYWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZXh0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZXh0XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9hdmF0YXJzL3FyJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHRleHQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0ZXh0J10gPSB0ZXh0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2l6ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NpemUnXSA9IHNpemU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtYXJnaW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydtYXJnaW4nXSA9IG1hcmdpbjtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvd25sb2FkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZG93bmxvYWQnXSA9IGRvd25sb2FkO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIHBheWxvYWRbJ3Byb2plY3QnXSA9IHRoaXMuY2xpZW50LmNvbmZpZy5wcm9qZWN0O1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTZXJ2aWNlLmZsYXR0ZW4ocGF5bG9hZCkpKSB7XG4gICAgICAgICAgICB1cmkuc2VhcmNoUGFyYW1zLmFwcGVuZChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdXJpLnRvU3RyaW5nKCk7XG4gICAgfVxufVxuXG5jbGFzcyBEYXRhYmFzZXMge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgbGlzdCBvZiBhbGwgdGhlIHVzZXImIzAzOTtzIGRvY3VtZW50cyBpbiBhIGdpdmVuIGNvbGxlY3Rpb24uIFlvdSBjYW4gdXNlIHRoZSBxdWVyeSBwYXJhbXMgdG8gZmlsdGVyIHlvdXIgcmVzdWx0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHF1ZXJpZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRG9jdW1lbnRMaXN0PERvY3VtZW50Pj59XG4gICAgICovXG4gICAgbGlzdERvY3VtZW50cyhkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIHF1ZXJpZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cycucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IERvY3VtZW50LiBCZWZvcmUgdXNpbmcgdGhpcyByb3V0ZSwgeW91IHNob3VsZCBjcmVhdGUgYSBuZXcgY29sbGVjdGlvbiByZXNvdXJjZSB1c2luZyBlaXRoZXIgYSBbc2VydmVyIGludGVncmF0aW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3Mvc2VydmVyL2RhdGFiYXNlcyNkYXRhYmFzZXNDcmVhdGVDb2xsZWN0aW9uKSBBUEkgb3IgZGlyZWN0bHkgZnJvbSB5b3VyIGRhdGFiYXNlIGNvbnNvbGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGF0YWJhc2VJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBjb2xsZWN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZG9jdW1lbnRJZFxuICAgICAqIEBwYXJhbSB7T21pdDxEb2N1bWVudCwga2V5b2YgTW9kZWxzLkRvY3VtZW50Pn0gZGF0YVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIGNyZWF0ZURvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgZGF0YSwgcGVybWlzc2lvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhYmFzZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkYXRhYmFzZUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbGxlY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiY29sbGVjdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRvY3VtZW50SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMnLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50SWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydkb2N1bWVudElkJ10gPSBkb2N1bWVudElkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2RhdGEnXSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBkb2N1bWVudCBieSBpdHMgdW5pcXVlIElELiBUaGlzIGVuZHBvaW50IHJlc3BvbnNlIHJldHVybnMgYSBKU09OIG9iamVjdCB3aXRoIHRoZSBkb2N1bWVudCBkYXRhLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8RG9jdW1lbnQ+fVxuICAgICAqL1xuICAgIGdldERvY3VtZW50KGRhdGFiYXNlSWQsIGNvbGxlY3Rpb25JZCwgZG9jdW1lbnRJZCwgcXVlcmllcykge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9Jy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCkucmVwbGFjZSgne2RvY3VtZW50SWR9JywgZG9jdW1lbnRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgb3IgdXBkYXRlIGEgRG9jdW1lbnQuIEJlZm9yZSB1c2luZyB0aGlzIHJvdXRlLCB5b3Ugc2hvdWxkIGNyZWF0ZSBhIG5ldyBjb2xsZWN0aW9uIHJlc291cmNlIHVzaW5nIGVpdGhlciBhIFtzZXJ2ZXIgaW50ZWdyYXRpb25dKGh0dHBzOi8vYXBwd3JpdGUuaW8vZG9jcy9zZXJ2ZXIvZGF0YWJhc2VzI2RhdGFiYXNlc0NyZWF0ZUNvbGxlY3Rpb24pIEFQSSBvciBkaXJlY3RseSBmcm9tIHlvdXIgZGF0YWJhc2UgY29uc29sZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gICAgICogQHBhcmFtIHtvYmplY3R9IGRhdGFcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBwZXJtaXNzaW9uc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPERvY3VtZW50Pn1cbiAgICAgKi9cbiAgICB1cHNlcnREb2N1bWVudChkYXRhYmFzZUlkLCBjb2xsZWN0aW9uSWQsIGRvY3VtZW50SWQsIGRhdGEsIHBlcm1pc3Npb25zKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YWJhc2VJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YWJhc2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBjb2xsZWN0aW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvbGxlY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkb2N1bWVudElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkb2N1bWVudElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGRhdGEgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2RhdGFiYXNlcy97ZGF0YWJhc2VJZH0vY29sbGVjdGlvbnMve2NvbGxlY3Rpb25JZH0vZG9jdW1lbnRzL3tkb2N1bWVudElkfScucmVwbGFjZSgne2RhdGFiYXNlSWR9JywgZGF0YWJhc2VJZCkucmVwbGFjZSgne2NvbGxlY3Rpb25JZH0nLCBjb2xsZWN0aW9uSWQpLnJlcGxhY2UoJ3tkb2N1bWVudElkfScsIGRvY3VtZW50SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2RhdGEnXSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBhIGRvY3VtZW50IGJ5IGl0cyB1bmlxdWUgSUQuIFVzaW5nIHRoZSBwYXRjaCBtZXRob2QgeW91IGNhbiBwYXNzIG9ubHkgc3BlY2lmaWMgZmllbGRzIHRoYXQgd2lsbCBnZXQgdXBkYXRlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkYXRhYmFzZUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGNvbGxlY3Rpb25JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkb2N1bWVudElkXG4gICAgICogQHBhcmFtIHtQYXJ0aWFsPE9taXQ8RG9jdW1lbnQsIGtleW9mIE1vZGVscy5Eb2N1bWVudD4+fSBkYXRhXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcGVybWlzc2lvbnNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxEb2N1bWVudD59XG4gICAgICovXG4gICAgdXBkYXRlRG9jdW1lbnQoZGF0YWJhc2VJZCwgY29sbGVjdGlvbklkLCBkb2N1bWVudElkLCBkYXRhLCBwZXJtaXNzaW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIGRhdGFiYXNlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImRhdGFiYXNlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY29sbGVjdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJjb2xsZWN0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZG9jdW1lbnRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZGF0YWJhc2VzL3tkYXRhYmFzZUlkfS9jb2xsZWN0aW9ucy97Y29sbGVjdGlvbklkfS9kb2N1bWVudHMve2RvY3VtZW50SWR9Jy5yZXBsYWNlKCd7ZGF0YWJhc2VJZH0nLCBkYXRhYmFzZUlkKS5yZXBsYWNlKCd7Y29sbGVjdGlvbklkfScsIGNvbGxlY3Rpb25JZCkucmVwbGFjZSgne2RvY3VtZW50SWR9JywgZG9jdW1lbnRJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBkYXRhICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnZGF0YSddID0gZGF0YTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBlcm1pc3Npb25zICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncGVybWlzc2lvbnMnXSA9IHBlcm1pc3Npb25zO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYSBkb2N1bWVudCBieSBpdHMgdW5pcXVlIElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRhdGFiYXNlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGRvY3VtZW50SWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlRG9jdW1lbnQoZGF0YWJhc2VJZCwgY29sbGVjdGlvbklkLCBkb2N1bWVudElkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YWJhc2VJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZGF0YWJhc2VJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBjb2xsZWN0aW9uSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImNvbGxlY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBkb2N1bWVudElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJkb2N1bWVudElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9kYXRhYmFzZXMve2RhdGFiYXNlSWR9L2NvbGxlY3Rpb25zL3tjb2xsZWN0aW9uSWR9L2RvY3VtZW50cy97ZG9jdW1lbnRJZH0nLnJlcGxhY2UoJ3tkYXRhYmFzZUlkfScsIGRhdGFiYXNlSWQpLnJlcGxhY2UoJ3tjb2xsZWN0aW9uSWR9JywgY29sbGVjdGlvbklkKS5yZXBsYWNlKCd7ZG9jdW1lbnRJZH0nLCBkb2N1bWVudElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBGdW5jdGlvbnMge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgbGlzdCBvZiBhbGwgdGhlIGN1cnJlbnQgdXNlciBmdW5jdGlvbiBleGVjdXRpb24gbG9ncy4gWW91IGNhbiB1c2UgdGhlIHF1ZXJ5IHBhcmFtcyB0byBmaWx0ZXIgeW91ciByZXN1bHRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZ1bmN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkV4ZWN1dGlvbkxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RFeGVjdXRpb25zKGZ1bmN0aW9uSWQsIHF1ZXJpZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBmdW5jdGlvbklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmdW5jdGlvbklkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9mdW5jdGlvbnMve2Z1bmN0aW9uSWR9L2V4ZWN1dGlvbnMnLnJlcGxhY2UoJ3tmdW5jdGlvbklkfScsIGZ1bmN0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVHJpZ2dlciBhIGZ1bmN0aW9uIGV4ZWN1dGlvbi4gVGhlIHJldHVybmVkIG9iamVjdCB3aWxsIHJldHVybiB5b3UgdGhlIGN1cnJlbnQgZXhlY3V0aW9uIHN0YXR1cy4gWW91IGNhbiBwaW5nIHRoZSBgR2V0IEV4ZWN1dGlvbmAgZW5kcG9pbnQgdG8gZ2V0IHVwZGF0ZXMgb24gdGhlIGN1cnJlbnQgZXhlY3V0aW9uIHN0YXR1cy4gT25jZSB0aGlzIGVuZHBvaW50IGlzIGNhbGxlZCwgeW91ciBmdW5jdGlvbiBleGVjdXRpb24gcHJvY2VzcyB3aWxsIHN0YXJ0IGFzeW5jaHJvbm91c2x5LlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZ1bmN0aW9uSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYm9keVxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gYXN5bmNcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30geHBhdGhcbiAgICAgKiBAcGFyYW0ge0V4ZWN1dGlvbk1ldGhvZH0gbWV0aG9kXG4gICAgICogQHBhcmFtIHtvYmplY3R9IGhlYWRlcnNcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2NoZWR1bGVkQXRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRXhlY3V0aW9uPn1cbiAgICAgKi9cbiAgICBjcmVhdGVFeGVjdXRpb24oZnVuY3Rpb25JZCwgYm9keSwgYXN5bmMsIHhwYXRoLCBtZXRob2QsIGhlYWRlcnMsIHNjaGVkdWxlZEF0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgZnVuY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZnVuY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvZnVuY3Rpb25zL3tmdW5jdGlvbklkfS9leGVjdXRpb25zJy5yZXBsYWNlKCd7ZnVuY3Rpb25JZH0nLCBmdW5jdGlvbklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGJvZHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydib2R5J10gPSBib2R5O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYXN5bmMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydhc3luYyddID0gYXN5bmM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB4cGF0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3BhdGgnXSA9IHhwYXRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWV0aG9kICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbWV0aG9kJ10gPSBtZXRob2Q7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBoZWFkZXJzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnaGVhZGVycyddID0gaGVhZGVycztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNjaGVkdWxlZEF0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2NoZWR1bGVkQXQnXSA9IHNjaGVkdWxlZEF0O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGZ1bmN0aW9uIGV4ZWN1dGlvbiBsb2cgYnkgaXRzIHVuaXF1ZSBJRC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmdW5jdGlvbklkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGV4ZWN1dGlvbklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkV4ZWN1dGlvbj59XG4gICAgICovXG4gICAgZ2V0RXhlY3V0aW9uKGZ1bmN0aW9uSWQsIGV4ZWN1dGlvbklkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZnVuY3Rpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZnVuY3Rpb25JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBleGVjdXRpb25JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiZXhlY3V0aW9uSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2Z1bmN0aW9ucy97ZnVuY3Rpb25JZH0vZXhlY3V0aW9ucy97ZXhlY3V0aW9uSWR9Jy5yZXBsYWNlKCd7ZnVuY3Rpb25JZH0nLCBmdW5jdGlvbklkKS5yZXBsYWNlKCd7ZXhlY3V0aW9uSWR9JywgZXhlY3V0aW9uSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBHcmFwaHFsIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEV4ZWN1dGUgYSBHcmFwaFFMIG11dGF0aW9uLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtvYmplY3R9IHF1ZXJ5XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIHF1ZXJ5KHF1ZXJ5KSB7XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcnkgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInF1ZXJ5XCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9ncmFwaHFsJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHF1ZXJ5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcnknXSA9IHF1ZXJ5O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAneC1zZGstZ3JhcGhxbCc6ICd0cnVlJyxcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRXhlY3V0ZSBhIEdyYXBoUUwgbXV0YXRpb24uXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge29iamVjdH0gcXVlcnlcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgbXV0YXRpb24ocXVlcnkpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyeSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicXVlcnlcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2dyYXBocWwvbXV0YXRpb24nO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcnkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydxdWVyeSddID0gcXVlcnk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICd4LXNkay1ncmFwaHFsJzogJ3RydWUnLFxuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbn1cblxuY2xhc3MgTG9jYWxlIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgY3VycmVudCB1c2VyIGxvY2F0aW9uIGJhc2VkIG9uIElQLiBSZXR1cm5zIGFuIG9iamVjdCB3aXRoIHVzZXIgY291bnRyeSBjb2RlLCBjb3VudHJ5IG5hbWUsIGNvbnRpbmVudCBuYW1lLCBjb250aW5lbnQgY29kZSwgaXAgYWRkcmVzcyBhbmQgc3VnZ2VzdGVkIGN1cnJlbmN5LiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG5cbihbSVAgR2VvbG9jYXRpb24gYnkgREItSVBdKGh0dHBzOi8vZGItaXAuY29tKSlcbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Mb2NhbGU+fVxuICAgICAqL1xuICAgIGdldCgpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbG9jYWxlJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBsb2NhbGUgY29kZXMgaW4gW0lTTyA2MzktMV0oaHR0cHM6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvTGlzdF9vZl9JU09fNjM5LTFfY29kZXMpLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkxvY2FsZUNvZGVMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q29kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jb2Rlcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgY29udGluZW50cy4gWW91IGNhbiB1c2UgdGhlIGxvY2FsZSBoZWFkZXIgdG8gZ2V0IHRoZSBkYXRhIGluIGEgc3VwcG9ydGVkIGxhbmd1YWdlLlxuICAgICAqXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkNvbnRpbmVudExpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb250aW5lbnRzKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY29udGluZW50cyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBhbGwgY291bnRyaWVzLiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuQ291bnRyeUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb3VudHJpZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jb3VudHJpZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgYWxsIGNvdW50cmllcyB0aGF0IGFyZSBjdXJyZW50bHkgbWVtYmVycyBvZiB0aGUgRVUuIFlvdSBjYW4gdXNlIHRoZSBsb2NhbGUgaGVhZGVyIHRvIGdldCB0aGUgZGF0YSBpbiBhIHN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5Db3VudHJ5TGlzdD59XG4gICAgICovXG4gICAgbGlzdENvdW50cmllc0VVKCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9sb2NhbGUvY291bnRyaWVzL2V1JztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBjb3VudHJpZXMgcGhvbmUgY29kZXMuIFlvdSBjYW4gdXNlIHRoZSBsb2NhbGUgaGVhZGVyIHRvIGdldCB0aGUgZGF0YSBpbiBhIHN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5QaG9uZUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RDb3VudHJpZXNQaG9uZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9jb3VudHJpZXMvcGhvbmVzJztcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IG9mIGFsbCBjdXJyZW5jaWVzLCBpbmNsdWRpbmcgY3VycmVuY3kgc3ltYm9sLCBuYW1lLCBwbHVyYWwsIGFuZCBkZWNpbWFsIGRpZ2l0cyBmb3IgYWxsIG1ham9yIGFuZCBtaW5vciBjdXJyZW5jaWVzLiBZb3UgY2FuIHVzZSB0aGUgbG9jYWxlIGhlYWRlciB0byBnZXQgdGhlIGRhdGEgaW4gYSBzdXBwb3J0ZWQgbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuQ3VycmVuY3lMaXN0Pn1cbiAgICAgKi9cbiAgICBsaXN0Q3VycmVuY2llcygpIHtcbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbG9jYWxlL2N1cnJlbmNpZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgYWxsIGxhbmd1YWdlcyBjbGFzc2lmaWVkIGJ5IElTTyA2MzktMSBpbmNsdWRpbmcgMi1sZXR0ZXIgY29kZSwgbmFtZSBpbiBFbmdsaXNoLCBhbmQgbmFtZSBpbiB0aGUgcmVzcGVjdGl2ZSBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5MYW5ndWFnZUxpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RMYW5ndWFnZXMoKSB7XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL2xvY2FsZS9sYW5ndWFnZXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBNZXNzYWdpbmcge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IHN1YnNjcmliZXIuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9waWNJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzdWJzY3JpYmVySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGFyZ2V0SWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuU3Vic2NyaWJlcj59XG4gICAgICovXG4gICAgY3JlYXRlU3Vic2NyaWJlcih0b3BpY0lkLCBzdWJzY3JpYmVySWQsIHRhcmdldElkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdG9waWNJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidG9waWNJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzdWJzY3JpYmVySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInN1YnNjcmliZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiB0YXJnZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGFyZ2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL21lc3NhZ2luZy90b3BpY3Mve3RvcGljSWR9L3N1YnNjcmliZXJzJy5yZXBsYWNlKCd7dG9waWNJZH0nLCB0b3BpY0lkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHN1YnNjcmliZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3N1YnNjcmliZXJJZCddID0gc3Vic2NyaWJlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdGFyZ2V0SWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0YXJnZXRJZCddID0gdGFyZ2V0SWQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgc3Vic2NyaWJlciBieSBpdHMgdW5pcXVlIElELlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRvcGljSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3Vic2NyaWJlcklkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZVN1YnNjcmliZXIodG9waWNJZCwgc3Vic2NyaWJlcklkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdG9waWNJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidG9waWNJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzdWJzY3JpYmVySWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInN1YnNjcmliZXJJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvbWVzc2FnaW5nL3RvcGljcy97dG9waWNJZH0vc3Vic2NyaWJlcnMve3N1YnNjcmliZXJJZH0nLnJlcGxhY2UoJ3t0b3BpY0lkfScsIHRvcGljSWQpLnJlcGxhY2UoJ3tzdWJzY3JpYmVySWR9Jywgc3Vic2NyaWJlcklkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxufVxuXG5jbGFzcyBTdG9yYWdlIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGxpc3Qgb2YgYWxsIHRoZSB1c2VyIGZpbGVzLiBZb3UgY2FuIHVzZSB0aGUgcXVlcnkgcGFyYW1zIHRvIGZpbHRlciB5b3VyIHJlc3VsdHMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYnVja2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfSBxdWVyaWVzXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNlYXJjaFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5GaWxlTGlzdD59XG4gICAgICovXG4gICAgbGlzdEZpbGVzKGJ1Y2tldElkLCBxdWVyaWVzLCBzZWFyY2gpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzJy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWFyY2ggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWFyY2gnXSA9IHNlYXJjaDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgZmlsZS4gQmVmb3JlIHVzaW5nIHRoaXMgcm91dGUsIHlvdSBzaG91bGQgY3JlYXRlIGEgbmV3IGJ1Y2tldCByZXNvdXJjZSB1c2luZyBlaXRoZXIgYSBbc2VydmVyIGludGVncmF0aW9uXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3Mvc2VydmVyL3N0b3JhZ2Ujc3RvcmFnZUNyZWF0ZUJ1Y2tldCkgQVBJIG9yIGRpcmVjdGx5IGZyb20geW91ciBBcHB3cml0ZSBjb25zb2xlLlxuXG5MYXJnZXIgZmlsZXMgc2hvdWxkIGJlIHVwbG9hZGVkIHVzaW5nIG11bHRpcGxlIHJlcXVlc3RzIHdpdGggdGhlIFtjb250ZW50LXJhbmdlXShodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9IVFRQL0hlYWRlcnMvQ29udGVudC1SYW5nZSkgaGVhZGVyIHRvIHNlbmQgYSBwYXJ0aWFsIHJlcXVlc3Qgd2l0aCBhIG1heGltdW0gc3VwcG9ydGVkIGNodW5rIG9mIGA1TUJgLiBUaGUgYGNvbnRlbnQtcmFuZ2VgIGhlYWRlciB2YWx1ZXMgc2hvdWxkIGFsd2F5cyBiZSBpbiBieXRlcy5cblxuV2hlbiB0aGUgZmlyc3QgcmVxdWVzdCBpcyBzZW50LCB0aGUgc2VydmVyIHdpbGwgcmV0dXJuIHRoZSAqKkZpbGUqKiBvYmplY3QsIGFuZCB0aGUgc3Vic2VxdWVudCBwYXJ0IHJlcXVlc3QgbXVzdCBpbmNsdWRlIHRoZSBmaWxlJiMwMzk7cyAqKmlkKiogaW4gYHgtYXBwd3JpdGUtaWRgIGhlYWRlciB0byBhbGxvdyB0aGUgc2VydmVyIHRvIGtub3cgdGhhdCB0aGUgcGFydGlhbCB1cGxvYWQgaXMgZm9yIHRoZSBleGlzdGluZyBmaWxlIGFuZCBub3QgZm9yIGEgbmV3IG9uZS5cblxuSWYgeW91JiMwMzk7cmUgY3JlYXRpbmcgYSBuZXcgZmlsZSB1c2luZyBvbmUgb2YgdGhlIEFwcHdyaXRlIFNES3MsIGFsbCB0aGUgY2h1bmtpbmcgbG9naWMgd2lsbCBiZSBtYW5hZ2VkIGJ5IHRoZSBTREsgaW50ZXJuYWxseS5cblxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7RmlsZX0gZmlsZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHBlcm1pc3Npb25zXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkZpbGU+fVxuICAgICAqL1xuICAgIGNyZWF0ZUZpbGUoYnVja2V0SWQsIGZpbGVJZCwgZmlsZSwgcGVybWlzc2lvbnMsIG9uUHJvZ3Jlc3MgPSAocHJvZ3Jlc3MpID0+IHsgfSkge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy9zdG9yYWdlL2J1Y2tldHMve2J1Y2tldElkfS9maWxlcycucmVwbGFjZSgne2J1Y2tldElkfScsIGJ1Y2tldElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIGZpbGVJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZpbGVJZCddID0gZmlsZUlkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2ZpbGUnXSA9IGZpbGU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNodW5rZWRVcGxvYWQoJ3Bvc3QnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQsIG9uUHJvZ3Jlc3MpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBmaWxlIGJ5IGl0cyB1bmlxdWUgSUQuIFRoaXMgZW5kcG9pbnQgcmVzcG9uc2UgcmV0dXJucyBhIEpTT04gb2JqZWN0IHdpdGggdGhlIGZpbGUgbWV0YWRhdGEuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gYnVja2V0SWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmlsZUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLkZpbGU+fVxuICAgICAqL1xuICAgIGdldEZpbGUoYnVja2V0SWQsIGZpbGVJZCkge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0nLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBhIGZpbGUgYnkgaXRzIHVuaXF1ZSBJRC4gT25seSB1c2VycyB3aXRoIHdyaXRlIHBlcm1pc3Npb25zIGhhdmUgYWNjZXNzIHRvIHVwZGF0ZSB0aGlzIHJlc291cmNlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcGVybWlzc2lvbnNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuRmlsZT59XG4gICAgICovXG4gICAgdXBkYXRlRmlsZShidWNrZXRJZCwgZmlsZUlkLCBuYW1lLCBwZXJtaXNzaW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0nLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9ucyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3Blcm1pc3Npb25zJ10gPSBwZXJtaXNzaW9ucztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ3B1dCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIGZpbGUgYnkgaXRzIHVuaXF1ZSBJRC4gT25seSB1c2VycyB3aXRoIHdyaXRlIHBlcm1pc3Npb25zIGhhdmUgYWNjZXNzIHRvIGRlbGV0ZSB0aGlzIHJlc291cmNlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHt9Pn1cbiAgICAgKi9cbiAgICBkZWxldGVGaWxlKGJ1Y2tldElkLCBmaWxlSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9Jy5yZXBsYWNlKCd7YnVja2V0SWR9JywgYnVja2V0SWQpLnJlcGxhY2UoJ3tmaWxlSWR9JywgZmlsZUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge1xuICAgICAgICAgICAgJ2NvbnRlbnQtdHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2RlbGV0ZScsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGZpbGUgY29udGVudCBieSBpdHMgdW5pcXVlIElELiBUaGUgZW5kcG9pbnQgcmVzcG9uc2UgcmV0dXJuIHdpdGggYSAmIzAzOTtDb250ZW50LURpc3Bvc2l0aW9uOiBhdHRhY2htZW50JiMwMzk7IGhlYWRlciB0aGF0IHRlbGxzIHRoZSBicm93c2VyIHRvIHN0YXJ0IGRvd25sb2FkaW5nIHRoZSBmaWxlIHRvIHVzZXIgZG93bmxvYWRzIGRpcmVjdG9yeS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBidWNrZXRJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWxlSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9rZW5cbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZpbGVEb3dubG9hZChidWNrZXRJZCwgZmlsZUlkLCB0b2tlbikge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0vZG93bmxvYWQnLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0b2tlbiddID0gdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgZmlsZSBwcmV2aWV3IGltYWdlLiBDdXJyZW50bHksIHRoaXMgbWV0aG9kIHN1cHBvcnRzIHByZXZpZXcgZm9yIGltYWdlIGZpbGVzIChqcGcsIHBuZywgYW5kIGdpZiksIG90aGVyIHN1cHBvcnRlZCBmb3JtYXRzLCBsaWtlIHBkZiwgZG9jcywgc2xpZGVzLCBhbmQgc3ByZWFkc2hlZXRzLCB3aWxsIHJldHVybiB0aGUgZmlsZSBpY29uIGltYWdlLiBZb3UgY2FuIGFsc28gcGFzcyBxdWVyeSBzdHJpbmcgYXJndW1lbnRzIGZvciBjdXR0aW5nIGFuZCByZXNpemluZyB5b3VyIHByZXZpZXcgaW1hZ2UuIFByZXZpZXcgaXMgc3VwcG9ydGVkIG9ubHkgZm9yIGltYWdlIGZpbGVzIHNtYWxsZXIgdGhhbiAxME1CLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKiBAcGFyYW0ge0ltYWdlR3Jhdml0eX0gZ3Jhdml0eVxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBxdWFsaXR5XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGJvcmRlcldpZHRoXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJvcmRlckNvbG9yXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IGJvcmRlclJhZGl1c1xuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBvcGFjaXR5XG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHJvdGF0aW9uXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJhY2tncm91bmRcbiAgICAgKiBAcGFyYW0ge0ltYWdlRm9ybWF0fSBvdXRwdXRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdG9rZW5cbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIGdldEZpbGVQcmV2aWV3KGJ1Y2tldElkLCBmaWxlSWQsIHdpZHRoLCBoZWlnaHQsIGdyYXZpdHksIHF1YWxpdHksIGJvcmRlcldpZHRoLCBib3JkZXJDb2xvciwgYm9yZGVyUmFkaXVzLCBvcGFjaXR5LCByb3RhdGlvbiwgYmFja2dyb3VuZCwgb3V0cHV0LCB0b2tlbikge1xuICAgICAgICBpZiAodHlwZW9mIGJ1Y2tldElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJidWNrZXRJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBmaWxlSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcImZpbGVJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvc3RvcmFnZS9idWNrZXRzL3tidWNrZXRJZH0vZmlsZXMve2ZpbGVJZH0vcHJldmlldycucmVwbGFjZSgne2J1Y2tldElkfScsIGJ1Y2tldElkKS5yZXBsYWNlKCd7ZmlsZUlkfScsIGZpbGVJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aWR0aCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3dpZHRoJ10gPSB3aWR0aDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGhlaWdodCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2hlaWdodCddID0gaGVpZ2h0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZ3Jhdml0eSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2dyYXZpdHknXSA9IGdyYXZpdHk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBxdWFsaXR5ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVhbGl0eSddID0gcXVhbGl0eTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGJvcmRlcldpZHRoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYm9yZGVyV2lkdGgnXSA9IGJvcmRlcldpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYm9yZGVyQ29sb3IgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydib3JkZXJDb2xvciddID0gYm9yZGVyQ29sb3I7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBib3JkZXJSYWRpdXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydib3JkZXJSYWRpdXMnXSA9IGJvcmRlclJhZGl1cztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG9wYWNpdHkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydvcGFjaXR5J10gPSBvcGFjaXR5O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygcm90YXRpb24gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydyb3RhdGlvbiddID0gcm90YXRpb247XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBiYWNrZ3JvdW5kICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnYmFja2dyb3VuZCddID0gYmFja2dyb3VuZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG91dHB1dCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ291dHB1dCddID0gb3V0cHV0O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0b2tlbiddID0gdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgZmlsZSBjb250ZW50IGJ5IGl0cyB1bmlxdWUgSUQuIFRoaXMgZW5kcG9pbnQgaXMgc2ltaWxhciB0byB0aGUgZG93bmxvYWQgbWV0aG9kIGJ1dCByZXR1cm5zIHdpdGggbm8gICYjMDM5O0NvbnRlbnQtRGlzcG9zaXRpb246IGF0dGFjaG1lbnQmIzAzOTsgaGVhZGVyLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGJ1Y2tldElkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGZpbGVJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0b2tlblxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgZ2V0RmlsZVZpZXcoYnVja2V0SWQsIGZpbGVJZCwgdG9rZW4pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBidWNrZXRJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwiYnVja2V0SWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgZmlsZUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJmaWxlSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3N0b3JhZ2UvYnVja2V0cy97YnVja2V0SWR9L2ZpbGVzL3tmaWxlSWR9L3ZpZXcnLnJlcGxhY2UoJ3tidWNrZXRJZH0nLCBidWNrZXRJZCkucmVwbGFjZSgne2ZpbGVJZH0nLCBmaWxlSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyd0b2tlbiddID0gdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgcGF5bG9hZFsncHJvamVjdCddID0gdGhpcy5jbGllbnQuY29uZmlnLnByb2plY3Q7XG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKFNlcnZpY2UuZmxhdHRlbihwYXlsb2FkKSkpIHtcbiAgICAgICAgICAgIHVyaS5zZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmkudG9TdHJpbmcoKTtcbiAgICB9XG59XG5cbmNsYXNzIFRlYW1zIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIGxpc3Qgb2YgYWxsIHRoZSB0ZWFtcyBpbiB3aGljaCB0aGUgY3VycmVudCB1c2VyIGlzIGEgbWVtYmVyLiBZb3UgY2FuIHVzZSB0aGUgcGFyYW1ldGVycyB0byBmaWx0ZXIgeW91ciByZXN1bHRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWFyY2hcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGVhbUxpc3Q8UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBsaXN0KHF1ZXJpZXMsIHNlYXJjaCkge1xuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcyc7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBxdWVyaWVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncXVlcmllcyddID0gcXVlcmllcztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHNlYXJjaCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3NlYXJjaCddID0gc2VhcmNoO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7fTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50LmNhbGwoJ2dldCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIG5ldyB0ZWFtLiBUaGUgdXNlciB3aG8gY3JlYXRlcyB0aGUgdGVhbSB3aWxsIGF1dG9tYXRpY2FsbHkgYmUgYXNzaWduZWQgYXMgdGhlIG93bmVyIG9mIHRoZSB0ZWFtLiBPbmx5IHRoZSB1c2VycyB3aXRoIHRoZSBvd25lciByb2xlIGNhbiBpbnZpdGUgbmV3IG1lbWJlcnMsIGFkZCBuZXcgb3duZXJzIGFuZCBkZWxldGUgb3IgdXBkYXRlIHRoZSB0ZWFtLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcm9sZXNcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGVhbTxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIGNyZWF0ZSh0ZWFtSWQsIG5hbWUsIHJvbGVzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibmFtZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMnO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsndGVhbUlkJ10gPSB0ZWFtSWQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBuYW1lICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnbmFtZSddID0gbmFtZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHJvbGVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncm9sZXMnXSA9IHJvbGVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncG9zdCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIHRlYW0gYnkgaXRzIElELiBBbGwgdGVhbSBtZW1iZXJzIGhhdmUgcmVhZCBhY2Nlc3MgZm9yIHRoaXMgcmVzb3VyY2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLlRlYW08UHJlZmVyZW5jZXM+Pn1cbiAgICAgKi9cbiAgICBnZXQodGVhbUlkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9Jy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIHRoZSB0ZWFtJiMwMzk7cyBuYW1lIGJ5IGl0cyB1bmlxdWUgSUQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuVGVhbTxQcmVmZXJlbmNlcz4+fVxuICAgICAqL1xuICAgIHVwZGF0ZU5hbWUodGVhbUlkLCBuYW1lKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibmFtZVwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIG5hbWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWyduYW1lJ10gPSBuYW1lO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncHV0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgdGVhbSB1c2luZyBpdHMgSUQuIE9ubHkgdGVhbSBtZW1iZXJzIHdpdGggdGhlIG93bmVyIHJvbGUgY2FuIGRlbGV0ZSB0aGUgdGVhbS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx7fT59XG4gICAgICovXG4gICAgZGVsZXRlKHRlYW1JZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZGVsZXRlJywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXNlIHRoaXMgZW5kcG9pbnQgdG8gbGlzdCBhIHRlYW0mIzAzOTtzIG1lbWJlcnMgdXNpbmcgdGhlIHRlYW0mIzAzOTtzIElELiBBbGwgdGVhbSBtZW1iZXJzIGhhdmUgcmVhZCBhY2Nlc3MgdG8gdGhpcyBlbmRwb2ludC4gSGlkZSBzZW5zaXRpdmUgYXR0cmlidXRlcyBmcm9tIHRoZSByZXNwb25zZSBieSB0b2dnbGluZyBtZW1iZXJzaGlwIHByaXZhY3kgaW4gdGhlIENvbnNvbGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmdbXX0gcXVlcmllc1xuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBzZWFyY2hcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWVtYmVyc2hpcExpc3Q+fVxuICAgICAqL1xuICAgIGxpc3RNZW1iZXJzaGlwcyh0ZWFtSWQsIHF1ZXJpZXMsIHNlYXJjaCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcycucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcXVlcmllcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3F1ZXJpZXMnXSA9IHF1ZXJpZXM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBzZWFyY2ggIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydzZWFyY2gnXSA9IHNlYXJjaDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBJbnZpdGUgYSBuZXcgbWVtYmVyIHRvIGpvaW4geW91ciB0ZWFtLiBQcm92aWRlIGFuIElEIGZvciBleGlzdGluZyB1c2Vycywgb3IgaW52aXRlIHVucmVnaXN0ZXJlZCB1c2VycyB1c2luZyBhbiBlbWFpbCBvciBwaG9uZSBudW1iZXIuIElmIGluaXRpYXRlZCBmcm9tIGEgQ2xpZW50IFNESywgQXBwd3JpdGUgd2lsbCBzZW5kIGFuIGVtYWlsIG9yIHNtcyB3aXRoIGEgbGluayB0byBqb2luIHRoZSB0ZWFtIHRvIHRoZSBpbnZpdGVkIHVzZXIsIGFuZCBhbiBhY2NvdW50IHdpbGwgYmUgY3JlYXRlZCBmb3IgdGhlbSBpZiBvbmUgZG9lc24mIzAzOTt0IGV4aXN0LiBJZiBpbml0aWF0ZWQgZnJvbSBhIFNlcnZlciBTREssIHRoZSBuZXcgbWVtYmVyIHdpbGwgYmUgYWRkZWQgYXV0b21hdGljYWxseSB0byB0aGUgdGVhbS5cblxuWW91IG9ubHkgbmVlZCB0byBwcm92aWRlIG9uZSBvZiBhIHVzZXIgSUQsIGVtYWlsLCBvciBwaG9uZSBudW1iZXIuIEFwcHdyaXRlIHdpbGwgcHJpb3JpdGl6ZSBhY2NlcHRpbmcgdGhlIHVzZXIgSUQgJmd0OyBlbWFpbCAmZ3Q7IHBob25lIG51bWJlciBpZiB5b3UgcHJvdmlkZSBtb3JlIHRoYW4gb25lIG9mIHRoZXNlIHBhcmFtZXRlcnMuXG5cblVzZSB0aGUgYHVybGAgcGFyYW1ldGVyIHRvIHJlZGlyZWN0IHRoZSB1c2VyIGZyb20gdGhlIGludml0YXRpb24gZW1haWwgdG8geW91ciBhcHAuIEFmdGVyIHRoZSB1c2VyIGlzIHJlZGlyZWN0ZWQsIHVzZSB0aGUgW1VwZGF0ZSBUZWFtIE1lbWJlcnNoaXAgU3RhdHVzXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcmVmZXJlbmNlcy9jbG91ZC9jbGllbnQtd2ViL3RlYW1zI3VwZGF0ZU1lbWJlcnNoaXBTdGF0dXMpIGVuZHBvaW50IHRvIGFsbG93IHRoZSB1c2VyIHRvIGFjY2VwdCB0aGUgaW52aXRhdGlvbiB0byB0aGUgdGVhbS5cblxuUGxlYXNlIG5vdGUgdGhhdCB0byBhdm9pZCBhIFtSZWRpcmVjdCBBdHRhY2tdKGh0dHBzOi8vZ2l0aHViLmNvbS9PV0FTUC9DaGVhdFNoZWV0U2VyaWVzL2Jsb2IvbWFzdGVyL2NoZWF0c2hlZXRzL1VudmFsaWRhdGVkX1JlZGlyZWN0c19hbmRfRm9yd2FyZHNfQ2hlYXRfU2hlZXQubWQpIEFwcHdyaXRlIHdpbGwgYWNjZXB0IHRoZSBvbmx5IHJlZGlyZWN0IFVSTHMgdW5kZXIgdGhlIGRvbWFpbnMgeW91IGhhdmUgYWRkZWQgYXMgYSBwbGF0Zm9ybSBvbiB0aGUgQXBwd3JpdGUgQ29uc29sZS5cblxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHJvbGVzXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHVzZXJJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBwaG9uZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1cmxcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPE1vZGVscy5NZW1iZXJzaGlwPn1cbiAgICAgKi9cbiAgICBjcmVhdGVNZW1iZXJzaGlwKHRlYW1JZCwgcm9sZXMsIGVtYWlsLCB1c2VySWQsIHBob25lLCB1cmwsIG5hbWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiByb2xlcyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwicm9sZXNcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9L21lbWJlcnNoaXBzJy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgaWYgKHR5cGVvZiBlbWFpbCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ2VtYWlsJ10gPSBlbWFpbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcGhvbmUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwaG9uZSddID0gcGhvbmU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiByb2xlcyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3JvbGVzJ10gPSByb2xlcztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHVybCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VybCddID0gdXJsO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbmFtZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ25hbWUnXSA9IG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwb3N0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgdGVhbSBtZW1iZXIgYnkgdGhlIG1lbWJlcnNoaXAgdW5pcXVlIGlkLiBBbGwgdGVhbSBtZW1iZXJzIGhhdmUgcmVhZCBhY2Nlc3MgZm9yIHRoaXMgcmVzb3VyY2UuIEhpZGUgc2Vuc2l0aXZlIGF0dHJpYnV0ZXMgZnJvbSB0aGUgcmVzcG9uc2UgYnkgdG9nZ2xpbmcgbWVtYmVyc2hpcCBwcml2YWN5IGluIHRoZSBDb25zb2xlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBtZW1iZXJzaGlwSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxNb2RlbHMuTWVtYmVyc2hpcD59XG4gICAgICovXG4gICAgZ2V0TWVtYmVyc2hpcCh0ZWFtSWQsIG1lbWJlcnNoaXBJZCkge1xuICAgICAgICBpZiAodHlwZW9mIHRlYW1JZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwidGVhbUlkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIG1lbWJlcnNoaXBJZCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBBcHB3cml0ZUV4Y2VwdGlvbignTWlzc2luZyByZXF1aXJlZCBwYXJhbWV0ZXI6IFwibWVtYmVyc2hpcElkXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcy97bWVtYmVyc2hpcElkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpLnJlcGxhY2UoJ3ttZW1iZXJzaGlwSWR9JywgbWVtYmVyc2hpcElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBjb25zdCB1cmkgPSBuZXcgVVJMKHRoaXMuY2xpZW50LmNvbmZpZy5lbmRwb2ludCArIGFwaVBhdGgpO1xuICAgICAgICBjb25zdCBhcGlIZWFkZXJzID0ge307XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdnZXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBNb2RpZnkgdGhlIHJvbGVzIG9mIGEgdGVhbSBtZW1iZXIuIE9ubHkgdGVhbSBtZW1iZXJzIHdpdGggdGhlIG93bmVyIHJvbGUgaGF2ZSBhY2Nlc3MgdG8gdGhpcyBlbmRwb2ludC4gTGVhcm4gbW9yZSBhYm91dCBbcm9sZXMgYW5kIHBlcm1pc3Npb25zXShodHRwczovL2FwcHdyaXRlLmlvL2RvY3MvcGVybWlzc2lvbnMpLlxuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG1lbWJlcnNoaXBJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nW119IHJvbGVzXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1lbWJlcnNoaXA+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1lbWJlcnNoaXAodGVhbUlkLCBtZW1iZXJzaGlwSWQsIHJvbGVzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWVtYmVyc2hpcElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJtZW1iZXJzaGlwSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygcm9sZXMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInJvbGVzXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9tZW1iZXJzaGlwcy97bWVtYmVyc2hpcElkfScucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpLnJlcGxhY2UoJ3ttZW1iZXJzaGlwSWR9JywgbWVtYmVyc2hpcElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHJvbGVzICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsncm9sZXMnXSA9IHJvbGVzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVyaSA9IG5ldyBVUkwodGhpcy5jbGllbnQuY29uZmlnLmVuZHBvaW50ICsgYXBpUGF0aCk7XG4gICAgICAgIGNvbnN0IGFwaUhlYWRlcnMgPSB7XG4gICAgICAgICAgICAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgncGF0Y2gnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaGlzIGVuZHBvaW50IGFsbG93cyBhIHVzZXIgdG8gbGVhdmUgYSB0ZWFtIG9yIGZvciBhIHRlYW0gb3duZXIgdG8gZGVsZXRlIHRoZSBtZW1iZXJzaGlwIG9mIGFueSBvdGhlciB0ZWFtIG1lbWJlci4gWW91IGNhbiBhbHNvIHVzZSB0aGlzIGVuZHBvaW50IHRvIGRlbGV0ZSBhIHVzZXIgbWVtYmVyc2hpcCBldmVuIGlmIGl0IGlzIG5vdCBhY2NlcHRlZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gbWVtYmVyc2hpcElkXG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8e30+fVxuICAgICAqL1xuICAgIGRlbGV0ZU1lbWJlcnNoaXAodGVhbUlkLCBtZW1iZXJzaGlwSWQpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0ZWFtSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInRlYW1JZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBtZW1iZXJzaGlwSWQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcIm1lbWJlcnNoaXBJZFwiJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXBpUGF0aCA9ICcvdGVhbXMve3RlYW1JZH0vbWVtYmVyc2hpcHMve21lbWJlcnNoaXBJZH0nLnJlcGxhY2UoJ3t0ZWFtSWR9JywgdGVhbUlkKS5yZXBsYWNlKCd7bWVtYmVyc2hpcElkfScsIG1lbWJlcnNoaXBJZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdkZWxldGUnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVc2UgdGhpcyBlbmRwb2ludCB0byBhbGxvdyBhIHVzZXIgdG8gYWNjZXB0IGFuIGludml0YXRpb24gdG8gam9pbiBhIHRlYW0gYWZ0ZXIgYmVpbmcgcmVkaXJlY3RlZCBiYWNrIHRvIHlvdXIgYXBwIGZyb20gdGhlIGludml0YXRpb24gZW1haWwgcmVjZWl2ZWQgYnkgdGhlIHVzZXIuXG5cbklmIHRoZSByZXF1ZXN0IGlzIHN1Y2Nlc3NmdWwsIGEgc2Vzc2lvbiBmb3IgdGhlIHVzZXIgaXMgYXV0b21hdGljYWxseSBjcmVhdGVkLlxuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdGVhbUlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IG1lbWJlcnNoaXBJZFxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB1c2VySWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc2VjcmV0XG4gICAgICogQHRocm93cyB7QXBwd3JpdGVFeGNlcHRpb259XG4gICAgICogQHJldHVybnMge1Byb21pc2U8TW9kZWxzLk1lbWJlcnNoaXA+fVxuICAgICAqL1xuICAgIHVwZGF0ZU1lbWJlcnNoaXBTdGF0dXModGVhbUlkLCBtZW1iZXJzaGlwSWQsIHVzZXJJZCwgc2VjcmV0KSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgbWVtYmVyc2hpcElkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJtZW1iZXJzaGlwSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdXNlcklkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ1c2VySWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJzZWNyZXRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9L21lbWJlcnNoaXBzL3ttZW1iZXJzaGlwSWR9L3N0YXR1cycucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpLnJlcGxhY2UoJ3ttZW1iZXJzaGlwSWR9JywgbWVtYmVyc2hpcElkKTtcbiAgICAgICAgY29uc3QgcGF5bG9hZCA9IHt9O1xuICAgICAgICBpZiAodHlwZW9mIHVzZXJJZCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHBheWxvYWRbJ3VzZXJJZCddID0gdXNlcklkO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygc2VjcmV0ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcGF5bG9hZFsnc2VjcmV0J10gPSBzZWNyZXQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwYXRjaCcsIHVyaSwgYXBpSGVhZGVycywgcGF5bG9hZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgdGVhbSYjMDM5O3Mgc2hhcmVkIHByZWZlcmVuY2VzIGJ5IGl0cyB1bmlxdWUgSUQuIElmIGEgcHJlZmVyZW5jZSBkb2VzbiYjMDM5O3QgbmVlZCB0byBiZSBzaGFyZWQgYnkgYWxsIHRlYW0gbWVtYmVycywgcHJlZmVyIHN0b3JpbmcgdGhlbSBpbiBbdXNlciBwcmVmZXJlbmNlc10oaHR0cHM6Ly9hcHB3cml0ZS5pby9kb2NzL3JlZmVyZW5jZXMvY2xvdWQvY2xpZW50LXdlYi9hY2NvdW50I2dldFByZWZzKS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0ZWFtSWRcbiAgICAgKiBAdGhyb3dzIHtBcHB3cml0ZUV4Y2VwdGlvbn1cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTxQcmVmZXJlbmNlcz59XG4gICAgICovXG4gICAgZ2V0UHJlZnModGVhbUlkKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwaVBhdGggPSAnL3RlYW1zL3t0ZWFtSWR9L3ByZWZzJy5yZXBsYWNlKCd7dGVhbUlkfScsIHRlYW1JZCk7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSB7fTtcbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHt9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQuY2FsbCgnZ2V0JywgdXJpLCBhcGlIZWFkZXJzLCBwYXlsb2FkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlIHRoZSB0ZWFtJiMwMzk7cyBwcmVmZXJlbmNlcyBieSBpdHMgdW5pcXVlIElELiBUaGUgb2JqZWN0IHlvdSBwYXNzIGlzIHN0b3JlZCBhcyBpcyBhbmQgcmVwbGFjZXMgYW55IHByZXZpb3VzIHZhbHVlLiBUaGUgbWF4aW11bSBhbGxvd2VkIHByZWZzIHNpemUgaXMgNjRrQiBhbmQgdGhyb3dzIGFuIGVycm9yIGlmIGV4Y2VlZGVkLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHRlYW1JZFxuICAgICAqIEBwYXJhbSB7b2JqZWN0fSBwcmVmc1xuICAgICAqIEB0aHJvd3Mge0FwcHdyaXRlRXhjZXB0aW9ufVxuICAgICAqIEByZXR1cm5zIHtQcm9taXNlPFByZWZlcmVuY2VzPn1cbiAgICAgKi9cbiAgICB1cGRhdGVQcmVmcyh0ZWFtSWQsIHByZWZzKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGVhbUlkID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEFwcHdyaXRlRXhjZXB0aW9uKCdNaXNzaW5nIHJlcXVpcmVkIHBhcmFtZXRlcjogXCJ0ZWFtSWRcIicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcHJlZnMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQXBwd3JpdGVFeGNlcHRpb24oJ01pc3NpbmcgcmVxdWlyZWQgcGFyYW1ldGVyOiBcInByZWZzXCInKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcGlQYXRoID0gJy90ZWFtcy97dGVhbUlkfS9wcmVmcycucmVwbGFjZSgne3RlYW1JZH0nLCB0ZWFtSWQpO1xuICAgICAgICBjb25zdCBwYXlsb2FkID0ge307XG4gICAgICAgIGlmICh0eXBlb2YgcHJlZnMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBwYXlsb2FkWydwcmVmcyddID0gcHJlZnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJpID0gbmV3IFVSTCh0aGlzLmNsaWVudC5jb25maWcuZW5kcG9pbnQgKyBhcGlQYXRoKTtcbiAgICAgICAgY29uc3QgYXBpSGVhZGVycyA9IHtcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5jYWxsKCdwdXQnLCB1cmksIGFwaUhlYWRlcnMsIHBheWxvYWQpO1xuICAgIH1cbn1cblxuLyoqXG4gKiBIZWxwZXIgY2xhc3MgdG8gZ2VuZXJhdGUgcGVybWlzc2lvbiBzdHJpbmdzIGZvciByZXNvdXJjZXMuXG4gKi9cbmNsYXNzIFBlcm1pc3Npb24ge1xufVxuLyoqXG4gKiBHZW5lcmF0ZSByZWFkIHBlcm1pc3Npb24gc3RyaW5nIGZvciB0aGUgcHJvdmlkZWQgcm9sZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gcm9sZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUGVybWlzc2lvbi5yZWFkID0gKHJvbGUpID0+IHtcbiAgICByZXR1cm4gYHJlYWQoXCIke3JvbGV9XCIpYDtcbn07XG4vKipcbiAqIEdlbmVyYXRlIHdyaXRlIHBlcm1pc3Npb24gc3RyaW5nIGZvciB0aGUgcHJvdmlkZWQgcm9sZS5cbiAqXG4gKiBUaGlzIGlzIGFuIGFsaWFzIG9mIHVwZGF0ZSwgZGVsZXRlLCBhbmQgcG9zc2libHkgY3JlYXRlLlxuICogRG9uJ3QgdXNlIHdyaXRlIGluIGNvbWJpbmF0aW9uIHdpdGggdXBkYXRlLCBkZWxldGUsIG9yIGNyZWF0ZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gcm9sZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUGVybWlzc2lvbi53cml0ZSA9IChyb2xlKSA9PiB7XG4gICAgcmV0dXJuIGB3cml0ZShcIiR7cm9sZX1cIilgO1xufTtcbi8qKlxuICogR2VuZXJhdGUgY3JlYXRlIHBlcm1pc3Npb24gc3RyaW5nIGZvciB0aGUgcHJvdmlkZWQgcm9sZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gcm9sZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuUGVybWlzc2lvbi5jcmVhdGUgPSAocm9sZSkgPT4ge1xuICAgIHJldHVybiBgY3JlYXRlKFwiJHtyb2xlfVwiKWA7XG59O1xuLyoqXG4gKiBHZW5lcmF0ZSB1cGRhdGUgcGVybWlzc2lvbiBzdHJpbmcgZm9yIHRoZSBwcm92aWRlZCByb2xlLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSByb2xlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5QZXJtaXNzaW9uLnVwZGF0ZSA9IChyb2xlKSA9PiB7XG4gICAgcmV0dXJuIGB1cGRhdGUoXCIke3JvbGV9XCIpYDtcbn07XG4vKipcbiAqIEdlbmVyYXRlIGRlbGV0ZSBwZXJtaXNzaW9uIHN0cmluZyBmb3IgdGhlIHByb3ZpZGVkIHJvbGUuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHJvbGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cblBlcm1pc3Npb24uZGVsZXRlID0gKHJvbGUpID0+IHtcbiAgICByZXR1cm4gYGRlbGV0ZShcIiR7cm9sZX1cIilgO1xufTtcblxuLyoqXG4gKiBIZWxwZXIgY2xhc3MgdG8gZ2VuZXJhdGUgcm9sZSBzdHJpbmdzIGZvciBgUGVybWlzc2lvbmAuXG4gKi9cbmNsYXNzIFJvbGUge1xuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYW55b25lLlxuICAgICAqXG4gICAgICogVGhpcyBpbmNsdWRlcyBhdXRoZW50aWNhdGVkIGFuZCB1bmF1dGhlbnRpY2F0ZWQgdXNlcnMuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyBhbnkoKSB7XG4gICAgICAgIHJldHVybiAnYW55JztcbiAgICB9XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhIHNwZWNpZmljIHVzZXIgYnkgdXNlciBJRC5cbiAgICAgKlxuICAgICAqIFlvdSBjYW4gb3B0aW9uYWxseSBwYXNzIHZlcmlmaWVkIG9yIHVudmVyaWZpZWQgZm9yXG4gICAgICogYHN0YXR1c2AgdG8gdGFyZ2V0IHNwZWNpZmljIHR5cGVzIG9mIHVzZXJzLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHN0YXR1c1xuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIHVzZXIoaWQsIHN0YXR1cyA9ICcnKSB7XG4gICAgICAgIGlmIChzdGF0dXMgPT09ICcnKSB7XG4gICAgICAgICAgICByZXR1cm4gYHVzZXI6JHtpZH1gO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBgdXNlcjoke2lkfS8ke3N0YXR1c31gO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGFueSBhdXRoZW50aWNhdGVkIG9yIGFub255bW91cyB1c2VyLlxuICAgICAqXG4gICAgICogWW91IGNhbiBvcHRpb25hbGx5IHBhc3MgdmVyaWZpZWQgb3IgdW52ZXJpZmllZCBmb3JcbiAgICAgKiBgc3RhdHVzYCB0byB0YXJnZXQgc3BlY2lmaWMgdHlwZXMgb2YgdXNlcnMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3RhdHVzXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgdXNlcnMoc3RhdHVzID0gJycpIHtcbiAgICAgICAgaWYgKHN0YXR1cyA9PT0gJycpIHtcbiAgICAgICAgICAgIHJldHVybiAndXNlcnMnO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBgdXNlcnMvJHtzdGF0dXN9YDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR3JhbnRzIGFjY2VzcyB0byBhbnkgZ3Vlc3QgdXNlciB3aXRob3V0IGEgc2Vzc2lvbi5cbiAgICAgKlxuICAgICAqIEF1dGhlbnRpY2F0ZWQgdXNlcnMgZG9uJ3QgaGF2ZSBhY2Nlc3MgdG8gdGhpcyByb2xlLlxuICAgICAqXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgZ3Vlc3RzKCkge1xuICAgICAgICByZXR1cm4gJ2d1ZXN0cyc7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYSB0ZWFtIGJ5IHRlYW0gSUQuXG4gICAgICpcbiAgICAgKiBZb3UgY2FuIG9wdGlvbmFsbHkgcGFzcyBhIHJvbGUgZm9yIGByb2xlYCB0byB0YXJnZXRcbiAgICAgKiB0ZWFtIG1lbWJlcnMgd2l0aCB0aGUgc3BlY2lmaWVkIHJvbGUuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gaWRcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gcm9sZVxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIHRlYW0oaWQsIHJvbGUgPSAnJykge1xuICAgICAgICBpZiAocm9sZSA9PT0gJycpIHtcbiAgICAgICAgICAgIHJldHVybiBgdGVhbToke2lkfWA7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGB0ZWFtOiR7aWR9LyR7cm9sZX1gO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHcmFudHMgYWNjZXNzIHRvIGEgc3BlY2lmaWMgbWVtYmVyIG9mIGEgdGVhbS5cbiAgICAgKlxuICAgICAqIFdoZW4gdGhlIG1lbWJlciBpcyByZW1vdmVkIGZyb20gdGhlIHRlYW0sIHRoZXkgd2lsbFxuICAgICAqIG5vIGxvbmdlciBoYXZlIGFjY2Vzcy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBpZFxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIG1lbWJlcihpZCkge1xuICAgICAgICByZXR1cm4gYG1lbWJlcjoke2lkfWA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdyYW50cyBhY2Nlc3MgdG8gYSB1c2VyIHdpdGggdGhlIHNwZWNpZmllZCBsYWJlbC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICogQHJldHVybnMgIHtzdHJpbmd9XG4gICAgICovXG4gICAgc3RhdGljIGxhYmVsKG5hbWUpIHtcbiAgICAgICAgcmV0dXJuIGBsYWJlbDoke25hbWV9YDtcbiAgICB9XG59XG5cbnZhciBfYSwgX0lEX2hleFRpbWVzdGFtcDtcbi8qKlxuICogSGVscGVyIGNsYXNzIHRvIGdlbmVyYXRlIElEIHN0cmluZ3MgZm9yIHJlc291cmNlcy5cbiAqL1xuY2xhc3MgSUQge1xuICAgIC8qKlxuICAgICAqIFVzZXMgdGhlIHByb3ZpZGVkIElEIGFzIHRoZSBJRCBmb3IgdGhlIHJlc291cmNlLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IGlkXG4gICAgICogQHJldHVybnMge3N0cmluZ31cbiAgICAgKi9cbiAgICBzdGF0aWMgY3VzdG9tKGlkKSB7XG4gICAgICAgIHJldHVybiBpZDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogSGF2ZSBBcHB3cml0ZSBnZW5lcmF0ZSBhIHVuaXF1ZSBJRCBmb3IgeW91LlxuICAgICAqXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHBhZGRpbmcuIERlZmF1bHQgaXMgNy5cbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAqL1xuICAgIHN0YXRpYyB1bmlxdWUocGFkZGluZyA9IDcpIHtcbiAgICAgICAgLy8gR2VuZXJhdGUgYSB1bmlxdWUgSUQgd2l0aCBwYWRkaW5nIHRvIGhhdmUgYSBsb25nZXIgSURcbiAgICAgICAgY29uc3QgYmFzZUlkID0gX19jbGFzc1ByaXZhdGVGaWVsZEdldChJRCwgX2EsIFwibVwiLCBfSURfaGV4VGltZXN0YW1wKS5jYWxsKElEKTtcbiAgICAgICAgbGV0IHJhbmRvbVBhZGRpbmcgPSAnJztcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwYWRkaW5nOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IHJhbmRvbUhleERpZ2l0ID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTYpLnRvU3RyaW5nKDE2KTtcbiAgICAgICAgICAgIHJhbmRvbVBhZGRpbmcgKz0gcmFuZG9tSGV4RGlnaXQ7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGJhc2VJZCArIHJhbmRvbVBhZGRpbmc7XG4gICAgfVxufVxuX2EgPSBJRCwgX0lEX2hleFRpbWVzdGFtcCA9IGZ1bmN0aW9uIF9JRF9oZXhUaW1lc3RhbXAoKSB7XG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcbiAgICBjb25zdCBzZWMgPSBNYXRoLmZsb29yKG5vdy5nZXRUaW1lKCkgLyAxMDAwKTtcbiAgICBjb25zdCBtc2VjID0gbm93LmdldE1pbGxpc2Vjb25kcygpO1xuICAgIC8vIENvbnZlcnQgdG8gaGV4YWRlY2ltYWxcbiAgICBjb25zdCBoZXhUaW1lc3RhbXAgPSBzZWMudG9TdHJpbmcoMTYpICsgbXNlYy50b1N0cmluZygxNikucGFkU3RhcnQoNSwgJzAnKTtcbiAgICByZXR1cm4gaGV4VGltZXN0YW1wO1xufTtcblxudmFyIEF1dGhlbnRpY2F0b3JUeXBlO1xuKGZ1bmN0aW9uIChBdXRoZW50aWNhdG9yVHlwZSkge1xuICAgIEF1dGhlbnRpY2F0b3JUeXBlW1wiVG90cFwiXSA9IFwidG90cFwiO1xufSkoQXV0aGVudGljYXRvclR5cGUgfHwgKEF1dGhlbnRpY2F0b3JUeXBlID0ge30pKTtcblxudmFyIEF1dGhlbnRpY2F0aW9uRmFjdG9yO1xuKGZ1bmN0aW9uIChBdXRoZW50aWNhdGlvbkZhY3Rvcikge1xuICAgIEF1dGhlbnRpY2F0aW9uRmFjdG9yW1wiRW1haWxcIl0gPSBcImVtYWlsXCI7XG4gICAgQXV0aGVudGljYXRpb25GYWN0b3JbXCJQaG9uZVwiXSA9IFwicGhvbmVcIjtcbiAgICBBdXRoZW50aWNhdGlvbkZhY3RvcltcIlRvdHBcIl0gPSBcInRvdHBcIjtcbiAgICBBdXRoZW50aWNhdGlvbkZhY3RvcltcIlJlY292ZXJ5Y29kZVwiXSA9IFwicmVjb3Zlcnljb2RlXCI7XG59KShBdXRoZW50aWNhdGlvbkZhY3RvciB8fCAoQXV0aGVudGljYXRpb25GYWN0b3IgPSB7fSkpO1xuXG52YXIgT0F1dGhQcm92aWRlcjtcbihmdW5jdGlvbiAoT0F1dGhQcm92aWRlcikge1xuICAgIE9BdXRoUHJvdmlkZXJbXCJBbWF6b25cIl0gPSBcImFtYXpvblwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJBcHBsZVwiXSA9IFwiYXBwbGVcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQXV0aDBcIl0gPSBcImF1dGgwXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkF1dGhlbnRpa1wiXSA9IFwiYXV0aGVudGlrXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkF1dG9kZXNrXCJdID0gXCJhdXRvZGVza1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJCaXRidWNrZXRcIl0gPSBcImJpdGJ1Y2tldFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJCaXRseVwiXSA9IFwiYml0bHlcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiQm94XCJdID0gXCJib3hcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRGFpbHltb3Rpb25cIl0gPSBcImRhaWx5bW90aW9uXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkRpc2NvcmRcIl0gPSBcImRpc2NvcmRcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRGlzcXVzXCJdID0gXCJkaXNxdXNcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiRHJvcGJveFwiXSA9IFwiZHJvcGJveFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJFdHN5XCJdID0gXCJldHN5XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIkZhY2Vib29rXCJdID0gXCJmYWNlYm9va1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJGaWdtYVwiXSA9IFwiZmlnbWFcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiR2l0aHViXCJdID0gXCJnaXRodWJcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiR2l0bGFiXCJdID0gXCJnaXRsYWJcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiR29vZ2xlXCJdID0gXCJnb29nbGVcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiTGlua2VkaW5cIl0gPSBcImxpbmtlZGluXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIk1pY3Jvc29mdFwiXSA9IFwibWljcm9zb2Z0XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIk5vdGlvblwiXSA9IFwibm90aW9uXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIk9pZGNcIl0gPSBcIm9pZGNcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiT2t0YVwiXSA9IFwib2t0YVwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJQYXlwYWxcIl0gPSBcInBheXBhbFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJQYXlwYWxTYW5kYm94XCJdID0gXCJwYXlwYWxTYW5kYm94XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlBvZGlvXCJdID0gXCJwb2Rpb1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJTYWxlc2ZvcmNlXCJdID0gXCJzYWxlc2ZvcmNlXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlNsYWNrXCJdID0gXCJzbGFja1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJTcG90aWZ5XCJdID0gXCJzcG90aWZ5XCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlN0cmlwZVwiXSA9IFwic3RyaXBlXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIlRyYWRlc2hpZnRcIl0gPSBcInRyYWRlc2hpZnRcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiVHJhZGVzaGlmdEJveFwiXSA9IFwidHJhZGVzaGlmdEJveFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJUd2l0Y2hcIl0gPSBcInR3aXRjaFwiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJXb3JkcHJlc3NcIl0gPSBcIndvcmRwcmVzc1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJZYWhvb1wiXSA9IFwieWFob29cIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiWWFtbWVyXCJdID0gXCJ5YW1tZXJcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiWWFuZGV4XCJdID0gXCJ5YW5kZXhcIjtcbiAgICBPQXV0aFByb3ZpZGVyW1wiWm9ob1wiXSA9IFwiem9ob1wiO1xuICAgIE9BdXRoUHJvdmlkZXJbXCJab29tXCJdID0gXCJ6b29tXCI7XG4gICAgT0F1dGhQcm92aWRlcltcIk1vY2tcIl0gPSBcIm1vY2tcIjtcbn0pKE9BdXRoUHJvdmlkZXIgfHwgKE9BdXRoUHJvdmlkZXIgPSB7fSkpO1xuXG52YXIgQnJvd3NlcjtcbihmdW5jdGlvbiAoQnJvd3Nlcikge1xuICAgIEJyb3dzZXJbXCJBdmFudEJyb3dzZXJcIl0gPSBcImFhXCI7XG4gICAgQnJvd3NlcltcIkFuZHJvaWRXZWJWaWV3QmV0YVwiXSA9IFwiYW5cIjtcbiAgICBCcm93c2VyW1wiR29vZ2xlQ2hyb21lXCJdID0gXCJjaFwiO1xuICAgIEJyb3dzZXJbXCJHb29nbGVDaHJvbWVJT1NcIl0gPSBcImNpXCI7XG4gICAgQnJvd3NlcltcIkdvb2dsZUNocm9tZU1vYmlsZVwiXSA9IFwiY21cIjtcbiAgICBCcm93c2VyW1wiQ2hyb21pdW1cIl0gPSBcImNyXCI7XG4gICAgQnJvd3NlcltcIk1vemlsbGFGaXJlZm94XCJdID0gXCJmZlwiO1xuICAgIEJyb3dzZXJbXCJTYWZhcmlcIl0gPSBcInNmXCI7XG4gICAgQnJvd3NlcltcIk1vYmlsZVNhZmFyaVwiXSA9IFwibWZcIjtcbiAgICBCcm93c2VyW1wiTWljcm9zb2Z0RWRnZVwiXSA9IFwicHNcIjtcbiAgICBCcm93c2VyW1wiTWljcm9zb2Z0RWRnZUlPU1wiXSA9IFwib2lcIjtcbiAgICBCcm93c2VyW1wiT3BlcmFNaW5pXCJdID0gXCJvbVwiO1xuICAgIEJyb3dzZXJbXCJPcGVyYVwiXSA9IFwib3BcIjtcbiAgICBCcm93c2VyW1wiT3BlcmFOZXh0XCJdID0gXCJvblwiO1xufSkoQnJvd3NlciB8fCAoQnJvd3NlciA9IHt9KSk7XG5cbnZhciBDcmVkaXRDYXJkO1xuKGZ1bmN0aW9uIChDcmVkaXRDYXJkKSB7XG4gICAgQ3JlZGl0Q2FyZFtcIkFtZXJpY2FuRXhwcmVzc1wiXSA9IFwiYW1leFwiO1xuICAgIENyZWRpdENhcmRbXCJBcmdlbmNhcmRcIl0gPSBcImFyZ2VuY2FyZFwiO1xuICAgIENyZWRpdENhcmRbXCJDYWJhbFwiXSA9IFwiY2FiYWxcIjtcbiAgICBDcmVkaXRDYXJkW1wiQ2VuY29zdWRcIl0gPSBcImNlbmNvc3VkXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIkRpbmVyc0NsdWJcIl0gPSBcImRpbmVyc1wiO1xuICAgIENyZWRpdENhcmRbXCJEaXNjb3ZlclwiXSA9IFwiZGlzY292ZXJcIjtcbiAgICBDcmVkaXRDYXJkW1wiRWxvXCJdID0gXCJlbG9cIjtcbiAgICBDcmVkaXRDYXJkW1wiSGlwZXJjYXJkXCJdID0gXCJoaXBlcmNhcmRcIjtcbiAgICBDcmVkaXRDYXJkW1wiSkNCXCJdID0gXCJqY2JcIjtcbiAgICBDcmVkaXRDYXJkW1wiTWFzdGVyY2FyZFwiXSA9IFwibWFzdGVyY2FyZFwiO1xuICAgIENyZWRpdENhcmRbXCJOYXJhbmphXCJdID0gXCJuYXJhbmphXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIlRhcmpldGFTaG9wcGluZ1wiXSA9IFwidGFyZ2V0YS1zaG9wcGluZ1wiO1xuICAgIENyZWRpdENhcmRbXCJVbmlvbkNoaW5hUGF5XCJdID0gXCJ1bmlvbi1jaGluYS1wYXlcIjtcbiAgICBDcmVkaXRDYXJkW1wiVmlzYVwiXSA9IFwidmlzYVwiO1xuICAgIENyZWRpdENhcmRbXCJNSVJcIl0gPSBcIm1pclwiO1xuICAgIENyZWRpdENhcmRbXCJNYWVzdHJvXCJdID0gXCJtYWVzdHJvXCI7XG4gICAgQ3JlZGl0Q2FyZFtcIlJ1cGF5XCJdID0gXCJydXBheVwiO1xufSkoQ3JlZGl0Q2FyZCB8fCAoQ3JlZGl0Q2FyZCA9IHt9KSk7XG5cbnZhciBGbGFnO1xuKGZ1bmN0aW9uIChGbGFnKSB7XG4gICAgRmxhZ1tcIkFmZ2hhbmlzdGFuXCJdID0gXCJhZlwiO1xuICAgIEZsYWdbXCJBbmdvbGFcIl0gPSBcImFvXCI7XG4gICAgRmxhZ1tcIkFsYmFuaWFcIl0gPSBcImFsXCI7XG4gICAgRmxhZ1tcIkFuZG9ycmFcIl0gPSBcImFkXCI7XG4gICAgRmxhZ1tcIlVuaXRlZEFyYWJFbWlyYXRlc1wiXSA9IFwiYWVcIjtcbiAgICBGbGFnW1wiQXJnZW50aW5hXCJdID0gXCJhclwiO1xuICAgIEZsYWdbXCJBcm1lbmlhXCJdID0gXCJhbVwiO1xuICAgIEZsYWdbXCJBbnRpZ3VhQW5kQmFyYnVkYVwiXSA9IFwiYWdcIjtcbiAgICBGbGFnW1wiQXVzdHJhbGlhXCJdID0gXCJhdVwiO1xuICAgIEZsYWdbXCJBdXN0cmlhXCJdID0gXCJhdFwiO1xuICAgIEZsYWdbXCJBemVyYmFpamFuXCJdID0gXCJhelwiO1xuICAgIEZsYWdbXCJCdXJ1bmRpXCJdID0gXCJiaVwiO1xuICAgIEZsYWdbXCJCZWxnaXVtXCJdID0gXCJiZVwiO1xuICAgIEZsYWdbXCJCZW5pblwiXSA9IFwiYmpcIjtcbiAgICBGbGFnW1wiQnVya2luYUZhc29cIl0gPSBcImJmXCI7XG4gICAgRmxhZ1tcIkJhbmdsYWRlc2hcIl0gPSBcImJkXCI7XG4gICAgRmxhZ1tcIkJ1bGdhcmlhXCJdID0gXCJiZ1wiO1xuICAgIEZsYWdbXCJCYWhyYWluXCJdID0gXCJiaFwiO1xuICAgIEZsYWdbXCJCYWhhbWFzXCJdID0gXCJic1wiO1xuICAgIEZsYWdbXCJCb3NuaWFBbmRIZXJ6ZWdvdmluYVwiXSA9IFwiYmFcIjtcbiAgICBGbGFnW1wiQmVsYXJ1c1wiXSA9IFwiYnlcIjtcbiAgICBGbGFnW1wiQmVsaXplXCJdID0gXCJielwiO1xuICAgIEZsYWdbXCJCb2xpdmlhXCJdID0gXCJib1wiO1xuICAgIEZsYWdbXCJCcmF6aWxcIl0gPSBcImJyXCI7XG4gICAgRmxhZ1tcIkJhcmJhZG9zXCJdID0gXCJiYlwiO1xuICAgIEZsYWdbXCJCcnVuZWlEYXJ1c3NhbGFtXCJdID0gXCJiblwiO1xuICAgIEZsYWdbXCJCaHV0YW5cIl0gPSBcImJ0XCI7XG4gICAgRmxhZ1tcIkJvdHN3YW5hXCJdID0gXCJid1wiO1xuICAgIEZsYWdbXCJDZW50cmFsQWZyaWNhblJlcHVibGljXCJdID0gXCJjZlwiO1xuICAgIEZsYWdbXCJDYW5hZGFcIl0gPSBcImNhXCI7XG4gICAgRmxhZ1tcIlN3aXR6ZXJsYW5kXCJdID0gXCJjaFwiO1xuICAgIEZsYWdbXCJDaGlsZVwiXSA9IFwiY2xcIjtcbiAgICBGbGFnW1wiQ2hpbmFcIl0gPSBcImNuXCI7XG4gICAgRmxhZ1tcIkNvdGVESXZvaXJlXCJdID0gXCJjaVwiO1xuICAgIEZsYWdbXCJDYW1lcm9vblwiXSA9IFwiY21cIjtcbiAgICBGbGFnW1wiRGVtb2NyYXRpY1JlcHVibGljT2ZUaGVDb25nb1wiXSA9IFwiY2RcIjtcbiAgICBGbGFnW1wiUmVwdWJsaWNPZlRoZUNvbmdvXCJdID0gXCJjZ1wiO1xuICAgIEZsYWdbXCJDb2xvbWJpYVwiXSA9IFwiY29cIjtcbiAgICBGbGFnW1wiQ29tb3Jvc1wiXSA9IFwia21cIjtcbiAgICBGbGFnW1wiQ2FwZVZlcmRlXCJdID0gXCJjdlwiO1xuICAgIEZsYWdbXCJDb3N0YVJpY2FcIl0gPSBcImNyXCI7XG4gICAgRmxhZ1tcIkN1YmFcIl0gPSBcImN1XCI7XG4gICAgRmxhZ1tcIkN5cHJ1c1wiXSA9IFwiY3lcIjtcbiAgICBGbGFnW1wiQ3plY2hSZXB1YmxpY1wiXSA9IFwiY3pcIjtcbiAgICBGbGFnW1wiR2VybWFueVwiXSA9IFwiZGVcIjtcbiAgICBGbGFnW1wiRGppYm91dGlcIl0gPSBcImRqXCI7XG4gICAgRmxhZ1tcIkRvbWluaWNhXCJdID0gXCJkbVwiO1xuICAgIEZsYWdbXCJEZW5tYXJrXCJdID0gXCJka1wiO1xuICAgIEZsYWdbXCJEb21pbmljYW5SZXB1YmxpY1wiXSA9IFwiZG9cIjtcbiAgICBGbGFnW1wiQWxnZXJpYVwiXSA9IFwiZHpcIjtcbiAgICBGbGFnW1wiRWN1YWRvclwiXSA9IFwiZWNcIjtcbiAgICBGbGFnW1wiRWd5cHRcIl0gPSBcImVnXCI7XG4gICAgRmxhZ1tcIkVyaXRyZWFcIl0gPSBcImVyXCI7XG4gICAgRmxhZ1tcIlNwYWluXCJdID0gXCJlc1wiO1xuICAgIEZsYWdbXCJFc3RvbmlhXCJdID0gXCJlZVwiO1xuICAgIEZsYWdbXCJFdGhpb3BpYVwiXSA9IFwiZXRcIjtcbiAgICBGbGFnW1wiRmlubGFuZFwiXSA9IFwiZmlcIjtcbiAgICBGbGFnW1wiRmlqaVwiXSA9IFwiZmpcIjtcbiAgICBGbGFnW1wiRnJhbmNlXCJdID0gXCJmclwiO1xuICAgIEZsYWdbXCJNaWNyb25lc2lhRmVkZXJhdGVkU3RhdGVzT2ZcIl0gPSBcImZtXCI7XG4gICAgRmxhZ1tcIkdhYm9uXCJdID0gXCJnYVwiO1xuICAgIEZsYWdbXCJVbml0ZWRLaW5nZG9tXCJdID0gXCJnYlwiO1xuICAgIEZsYWdbXCJHZW9yZ2lhXCJdID0gXCJnZVwiO1xuICAgIEZsYWdbXCJHaGFuYVwiXSA9IFwiZ2hcIjtcbiAgICBGbGFnW1wiR3VpbmVhXCJdID0gXCJnblwiO1xuICAgIEZsYWdbXCJHYW1iaWFcIl0gPSBcImdtXCI7XG4gICAgRmxhZ1tcIkd1aW5lYUJpc3NhdVwiXSA9IFwiZ3dcIjtcbiAgICBGbGFnW1wiRXF1YXRvcmlhbEd1aW5lYVwiXSA9IFwiZ3FcIjtcbiAgICBGbGFnW1wiR3JlZWNlXCJdID0gXCJnclwiO1xuICAgIEZsYWdbXCJHcmVuYWRhXCJdID0gXCJnZFwiO1xuICAgIEZsYWdbXCJHdWF0ZW1hbGFcIl0gPSBcImd0XCI7XG4gICAgRmxhZ1tcIkd1eWFuYVwiXSA9IFwiZ3lcIjtcbiAgICBGbGFnW1wiSG9uZHVyYXNcIl0gPSBcImhuXCI7XG4gICAgRmxhZ1tcIkNyb2F0aWFcIl0gPSBcImhyXCI7XG4gICAgRmxhZ1tcIkhhaXRpXCJdID0gXCJodFwiO1xuICAgIEZsYWdbXCJIdW5nYXJ5XCJdID0gXCJodVwiO1xuICAgIEZsYWdbXCJJbmRvbmVzaWFcIl0gPSBcImlkXCI7XG4gICAgRmxhZ1tcIkluZGlhXCJdID0gXCJpblwiO1xuICAgIEZsYWdbXCJJcmVsYW5kXCJdID0gXCJpZVwiO1xuICAgIEZsYWdbXCJJcmFuSXNsYW1pY1JlcHVibGljT2ZcIl0gPSBcImlyXCI7XG4gICAgRmxhZ1tcIklyYXFcIl0gPSBcImlxXCI7XG4gICAgRmxhZ1tcIkljZWxhbmRcIl0gPSBcImlzXCI7XG4gICAgRmxhZ1tcIklzcmFlbFwiXSA9IFwiaWxcIjtcbiAgICBGbGFnW1wiSXRhbHlcIl0gPSBcIml0XCI7XG4gICAgRmxhZ1tcIkphbWFpY2FcIl0gPSBcImptXCI7XG4gICAgRmxhZ1tcIkpvcmRhblwiXSA9IFwiam9cIjtcbiAgICBGbGFnW1wiSmFwYW5cIl0gPSBcImpwXCI7XG4gICAgRmxhZ1tcIkthemFraHN0YW5cIl0gPSBcImt6XCI7XG4gICAgRmxhZ1tcIktlbnlhXCJdID0gXCJrZVwiO1xuICAgIEZsYWdbXCJLeXJneXpzdGFuXCJdID0gXCJrZ1wiO1xuICAgIEZsYWdbXCJDYW1ib2RpYVwiXSA9IFwia2hcIjtcbiAgICBGbGFnW1wiS2lyaWJhdGlcIl0gPSBcImtpXCI7XG4gICAgRmxhZ1tcIlNhaW50S2l0dHNBbmROZXZpc1wiXSA9IFwia25cIjtcbiAgICBGbGFnW1wiU291dGhLb3JlYVwiXSA9IFwia3JcIjtcbiAgICBGbGFnW1wiS3V3YWl0XCJdID0gXCJrd1wiO1xuICAgIEZsYWdbXCJMYW9QZW9wbGVTRGVtb2NyYXRpY1JlcHVibGljXCJdID0gXCJsYVwiO1xuICAgIEZsYWdbXCJMZWJhbm9uXCJdID0gXCJsYlwiO1xuICAgIEZsYWdbXCJMaWJlcmlhXCJdID0gXCJsclwiO1xuICAgIEZsYWdbXCJMaWJ5YVwiXSA9IFwibHlcIjtcbiAgICBGbGFnW1wiU2FpbnRMdWNpYVwiXSA9IFwibGNcIjtcbiAgICBGbGFnW1wiTGllY2h0ZW5zdGVpblwiXSA9IFwibGlcIjtcbiAgICBGbGFnW1wiU3JpTGFua2FcIl0gPSBcImxrXCI7XG4gICAgRmxhZ1tcIkxlc290aG9cIl0gPSBcImxzXCI7XG4gICAgRmxhZ1tcIkxpdGh1YW5pYVwiXSA9IFwibHRcIjtcbiAgICBGbGFnW1wiTHV4ZW1ib3VyZ1wiXSA9IFwibHVcIjtcbiAgICBGbGFnW1wiTGF0dmlhXCJdID0gXCJsdlwiO1xuICAgIEZsYWdbXCJNb3JvY2NvXCJdID0gXCJtYVwiO1xuICAgIEZsYWdbXCJNb25hY29cIl0gPSBcIm1jXCI7XG4gICAgRmxhZ1tcIk1vbGRvdmFcIl0gPSBcIm1kXCI7XG4gICAgRmxhZ1tcIk1hZGFnYXNjYXJcIl0gPSBcIm1nXCI7XG4gICAgRmxhZ1tcIk1hbGRpdmVzXCJdID0gXCJtdlwiO1xuICAgIEZsYWdbXCJNZXhpY29cIl0gPSBcIm14XCI7XG4gICAgRmxhZ1tcIk1hcnNoYWxsSXNsYW5kc1wiXSA9IFwibWhcIjtcbiAgICBGbGFnW1wiTm9ydGhNYWNlZG9uaWFcIl0gPSBcIm1rXCI7XG4gICAgRmxhZ1tcIk1hbGlcIl0gPSBcIm1sXCI7XG4gICAgRmxhZ1tcIk1hbHRhXCJdID0gXCJtdFwiO1xuICAgIEZsYWdbXCJNeWFubWFyXCJdID0gXCJtbVwiO1xuICAgIEZsYWdbXCJNb250ZW5lZ3JvXCJdID0gXCJtZVwiO1xuICAgIEZsYWdbXCJNb25nb2xpYVwiXSA9IFwibW5cIjtcbiAgICBGbGFnW1wiTW96YW1iaXF1ZVwiXSA9IFwibXpcIjtcbiAgICBGbGFnW1wiTWF1cml0YW5pYVwiXSA9IFwibXJcIjtcbiAgICBGbGFnW1wiTWF1cml0aXVzXCJdID0gXCJtdVwiO1xuICAgIEZsYWdbXCJNYWxhd2lcIl0gPSBcIm13XCI7XG4gICAgRmxhZ1tcIk1hbGF5c2lhXCJdID0gXCJteVwiO1xuICAgIEZsYWdbXCJOYW1pYmlhXCJdID0gXCJuYVwiO1xuICAgIEZsYWdbXCJOaWdlclwiXSA9IFwibmVcIjtcbiAgICBGbGFnW1wiTmlnZXJpYVwiXSA9IFwibmdcIjtcbiAgICBGbGFnW1wiTmljYXJhZ3VhXCJdID0gXCJuaVwiO1xuICAgIEZsYWdbXCJOZXRoZXJsYW5kc1wiXSA9IFwibmxcIjtcbiAgICBGbGFnW1wiTm9yd2F5XCJdID0gXCJub1wiO1xuICAgIEZsYWdbXCJOZXBhbFwiXSA9IFwibnBcIjtcbiAgICBGbGFnW1wiTmF1cnVcIl0gPSBcIm5yXCI7XG4gICAgRmxhZ1tcIk5ld1plYWxhbmRcIl0gPSBcIm56XCI7XG4gICAgRmxhZ1tcIk9tYW5cIl0gPSBcIm9tXCI7XG4gICAgRmxhZ1tcIlBha2lzdGFuXCJdID0gXCJwa1wiO1xuICAgIEZsYWdbXCJQYW5hbWFcIl0gPSBcInBhXCI7XG4gICAgRmxhZ1tcIlBlcnVcIl0gPSBcInBlXCI7XG4gICAgRmxhZ1tcIlBoaWxpcHBpbmVzXCJdID0gXCJwaFwiO1xuICAgIEZsYWdbXCJQYWxhdVwiXSA9IFwicHdcIjtcbiAgICBGbGFnW1wiUGFwdWFOZXdHdWluZWFcIl0gPSBcInBnXCI7XG4gICAgRmxhZ1tcIlBvbGFuZFwiXSA9IFwicGxcIjtcbiAgICBGbGFnW1wiRnJlbmNoUG9seW5lc2lhXCJdID0gXCJwZlwiO1xuICAgIEZsYWdbXCJOb3J0aEtvcmVhXCJdID0gXCJrcFwiO1xuICAgIEZsYWdbXCJQb3J0dWdhbFwiXSA9IFwicHRcIjtcbiAgICBGbGFnW1wiUGFyYWd1YXlcIl0gPSBcInB5XCI7XG4gICAgRmxhZ1tcIlFhdGFyXCJdID0gXCJxYVwiO1xuICAgIEZsYWdbXCJSb21hbmlhXCJdID0gXCJyb1wiO1xuICAgIEZsYWdbXCJSdXNzaWFcIl0gPSBcInJ1XCI7XG4gICAgRmxhZ1tcIlJ3YW5kYVwiXSA9IFwicndcIjtcbiAgICBGbGFnW1wiU2F1ZGlBcmFiaWFcIl0gPSBcInNhXCI7XG4gICAgRmxhZ1tcIlN1ZGFuXCJdID0gXCJzZFwiO1xuICAgIEZsYWdbXCJTZW5lZ2FsXCJdID0gXCJzblwiO1xuICAgIEZsYWdbXCJTaW5nYXBvcmVcIl0gPSBcInNnXCI7XG4gICAgRmxhZ1tcIlNvbG9tb25Jc2xhbmRzXCJdID0gXCJzYlwiO1xuICAgIEZsYWdbXCJTaWVycmFMZW9uZVwiXSA9IFwic2xcIjtcbiAgICBGbGFnW1wiRWxTYWx2YWRvclwiXSA9IFwic3ZcIjtcbiAgICBGbGFnW1wiU2FuTWFyaW5vXCJdID0gXCJzbVwiO1xuICAgIEZsYWdbXCJTb21hbGlhXCJdID0gXCJzb1wiO1xuICAgIEZsYWdbXCJTZXJiaWFcIl0gPSBcInJzXCI7XG4gICAgRmxhZ1tcIlNvdXRoU3VkYW5cIl0gPSBcInNzXCI7XG4gICAgRmxhZ1tcIlNhb1RvbWVBbmRQcmluY2lwZVwiXSA9IFwic3RcIjtcbiAgICBGbGFnW1wiU3VyaW5hbWVcIl0gPSBcInNyXCI7XG4gICAgRmxhZ1tcIlNsb3Zha2lhXCJdID0gXCJza1wiO1xuICAgIEZsYWdbXCJTbG92ZW5pYVwiXSA9IFwic2lcIjtcbiAgICBGbGFnW1wiU3dlZGVuXCJdID0gXCJzZVwiO1xuICAgIEZsYWdbXCJFc3dhdGluaVwiXSA9IFwic3pcIjtcbiAgICBGbGFnW1wiU2V5Y2hlbGxlc1wiXSA9IFwic2NcIjtcbiAgICBGbGFnW1wiU3lyaWFcIl0gPSBcInN5XCI7XG4gICAgRmxhZ1tcIkNoYWRcIl0gPSBcInRkXCI7XG4gICAgRmxhZ1tcIlRvZ29cIl0gPSBcInRnXCI7XG4gICAgRmxhZ1tcIlRoYWlsYW5kXCJdID0gXCJ0aFwiO1xuICAgIEZsYWdbXCJUYWppa2lzdGFuXCJdID0gXCJ0alwiO1xuICAgIEZsYWdbXCJUdXJrbWVuaXN0YW5cIl0gPSBcInRtXCI7XG4gICAgRmxhZ1tcIlRpbW9yTGVzdGVcIl0gPSBcInRsXCI7XG4gICAgRmxhZ1tcIlRvbmdhXCJdID0gXCJ0b1wiO1xuICAgIEZsYWdbXCJUcmluaWRhZEFuZFRvYmFnb1wiXSA9IFwidHRcIjtcbiAgICBGbGFnW1wiVHVuaXNpYVwiXSA9IFwidG5cIjtcbiAgICBGbGFnW1wiVHVya2V5XCJdID0gXCJ0clwiO1xuICAgIEZsYWdbXCJUdXZhbHVcIl0gPSBcInR2XCI7XG4gICAgRmxhZ1tcIlRhbnphbmlhXCJdID0gXCJ0elwiO1xuICAgIEZsYWdbXCJVZ2FuZGFcIl0gPSBcInVnXCI7XG4gICAgRmxhZ1tcIlVrcmFpbmVcIl0gPSBcInVhXCI7XG4gICAgRmxhZ1tcIlVydWd1YXlcIl0gPSBcInV5XCI7XG4gICAgRmxhZ1tcIlVuaXRlZFN0YXRlc1wiXSA9IFwidXNcIjtcbiAgICBGbGFnW1wiVXpiZWtpc3RhblwiXSA9IFwidXpcIjtcbiAgICBGbGFnW1wiVmF0aWNhbkNpdHlcIl0gPSBcInZhXCI7XG4gICAgRmxhZ1tcIlNhaW50VmluY2VudEFuZFRoZUdyZW5hZGluZXNcIl0gPSBcInZjXCI7XG4gICAgRmxhZ1tcIlZlbmV6dWVsYVwiXSA9IFwidmVcIjtcbiAgICBGbGFnW1wiVmlldG5hbVwiXSA9IFwidm5cIjtcbiAgICBGbGFnW1wiVmFudWF0dVwiXSA9IFwidnVcIjtcbiAgICBGbGFnW1wiU2Ftb2FcIl0gPSBcIndzXCI7XG4gICAgRmxhZ1tcIlllbWVuXCJdID0gXCJ5ZVwiO1xuICAgIEZsYWdbXCJTb3V0aEFmcmljYVwiXSA9IFwiemFcIjtcbiAgICBGbGFnW1wiWmFtYmlhXCJdID0gXCJ6bVwiO1xuICAgIEZsYWdbXCJaaW1iYWJ3ZVwiXSA9IFwiendcIjtcbn0pKEZsYWcgfHwgKEZsYWcgPSB7fSkpO1xuXG52YXIgRXhlY3V0aW9uTWV0aG9kO1xuKGZ1bmN0aW9uIChFeGVjdXRpb25NZXRob2QpIHtcbiAgICBFeGVjdXRpb25NZXRob2RbXCJHRVRcIl0gPSBcIkdFVFwiO1xuICAgIEV4ZWN1dGlvbk1ldGhvZFtcIlBPU1RcIl0gPSBcIlBPU1RcIjtcbiAgICBFeGVjdXRpb25NZXRob2RbXCJQVVRcIl0gPSBcIlBVVFwiO1xuICAgIEV4ZWN1dGlvbk1ldGhvZFtcIlBBVENIXCJdID0gXCJQQVRDSFwiO1xuICAgIEV4ZWN1dGlvbk1ldGhvZFtcIkRFTEVURVwiXSA9IFwiREVMRVRFXCI7XG4gICAgRXhlY3V0aW9uTWV0aG9kW1wiT1BUSU9OU1wiXSA9IFwiT1BUSU9OU1wiO1xufSkoRXhlY3V0aW9uTWV0aG9kIHx8IChFeGVjdXRpb25NZXRob2QgPSB7fSkpO1xuXG52YXIgSW1hZ2VHcmF2aXR5O1xuKGZ1bmN0aW9uIChJbWFnZUdyYXZpdHkpIHtcbiAgICBJbWFnZUdyYXZpdHlbXCJDZW50ZXJcIl0gPSBcImNlbnRlclwiO1xuICAgIEltYWdlR3Jhdml0eVtcIlRvcGxlZnRcIl0gPSBcInRvcC1sZWZ0XCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiVG9wXCJdID0gXCJ0b3BcIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJUb3ByaWdodFwiXSA9IFwidG9wLXJpZ2h0XCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiTGVmdFwiXSA9IFwibGVmdFwiO1xuICAgIEltYWdlR3Jhdml0eVtcIlJpZ2h0XCJdID0gXCJyaWdodFwiO1xuICAgIEltYWdlR3Jhdml0eVtcIkJvdHRvbWxlZnRcIl0gPSBcImJvdHRvbS1sZWZ0XCI7XG4gICAgSW1hZ2VHcmF2aXR5W1wiQm90dG9tXCJdID0gXCJib3R0b21cIjtcbiAgICBJbWFnZUdyYXZpdHlbXCJCb3R0b21yaWdodFwiXSA9IFwiYm90dG9tLXJpZ2h0XCI7XG59KShJbWFnZUdyYXZpdHkgfHwgKEltYWdlR3Jhdml0eSA9IHt9KSk7XG5cbnZhciBJbWFnZUZvcm1hdDtcbihmdW5jdGlvbiAoSW1hZ2VGb3JtYXQpIHtcbiAgICBJbWFnZUZvcm1hdFtcIkpwZ1wiXSA9IFwianBnXCI7XG4gICAgSW1hZ2VGb3JtYXRbXCJKcGVnXCJdID0gXCJqcGVnXCI7XG4gICAgSW1hZ2VGb3JtYXRbXCJQbmdcIl0gPSBcInBuZ1wiO1xuICAgIEltYWdlRm9ybWF0W1wiV2VicFwiXSA9IFwid2VicFwiO1xuICAgIEltYWdlRm9ybWF0W1wiSGVpY1wiXSA9IFwiaGVpY1wiO1xuICAgIEltYWdlRm9ybWF0W1wiQXZpZlwiXSA9IFwiYXZpZlwiO1xufSkoSW1hZ2VGb3JtYXQgfHwgKEltYWdlRm9ybWF0ID0ge30pKTtcblxuZXhwb3J0IHsgQWNjb3VudCwgQXBwd3JpdGVFeGNlcHRpb24sIEF1dGhlbnRpY2F0aW9uRmFjdG9yLCBBdXRoZW50aWNhdG9yVHlwZSwgQXZhdGFycywgQnJvd3NlciwgQ2xpZW50LCBDcmVkaXRDYXJkLCBEYXRhYmFzZXMsIEV4ZWN1dGlvbk1ldGhvZCwgRmxhZywgRnVuY3Rpb25zLCBHcmFwaHFsLCBJRCwgSW1hZ2VGb3JtYXQsIEltYWdlR3Jhdml0eSwgTG9jYWxlLCBNZXNzYWdpbmcsIE9BdXRoUHJvdmlkZXIsIFBlcm1pc3Npb24sIFF1ZXJ5LCBSb2xlLCBTdG9yYWdlLCBUZWFtcyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2RrLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/appwrite/dist/esm/sdk.js\n");

/***/ })

};
;