/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 2v2", key: "tus03m" }],
  [
    "path",
    {
      d: "M14.837 16.385a6 6 0 1 1-7.223-7.222c.624-.147.97.66.715 1.248a4 4 0 0 0 5.26 5.259c.589-.255 1.396.09 1.248.715",
      key: "xlf6rm"
    }
  ],
  ["path", { d: "M16 12a4 4 0 0 0-4-4", key: "6vsxu" }],
  ["path", { d: "m19 5-1.256 1.256", key: "1yg6a6" }],
  ["path", { d: "M20 12h2", key: "1q8mjw" }]
];
const SunMoon = createLucideIcon("sun-moon", __iconNode);

export { __iconNode, SunMoon as default };
//# sourceMappingURL=sun-moon.js.map
