@font-face {font-family: "icon";
  src: url('icon.eot'); /* IE9*/
  src: url('icon.eot#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("icon.woff2") format("woff2"),
  url("icon.woff") format("woff"),
  url('icon.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('icon.svg#icon') format('svg'); /* iOS 4.1- */
}

[class^="icon-"], [class*=" icon-"] {
  font-family: 'icon' !important;font-size: 16px;
  font-display: swap;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:root {
--icon-academic-cap: "\ea01";
--icon-adjustments: "\ea02";
--icon-akamai: "\ea03";
--icon-algolia: "\ea04";
--icon-amazon: "\ea05";
--icon-android: "\ea06";
--icon-angular: "\ea07";
--icon-annotation: "\ea08";
--icon-anonymous: "\ea09";
--icon-api: "\ea0a";
--icon-apple: "\ea0b";
--icon-appwrite: "\ea0c";
--icon-archive: "\ea0d";
--icon-arrow-circle-down: "\ea0e";
--icon-arrow-circle-left: "\ea0f";
--icon-arrow-circle-right: "\ea10";
--icon-arrow-circle-up: "\ea11";
--icon-arrow-down: "\ea12";
--icon-arrow-expand: "\ea13";
--icon-arrow-left: "\ea14";
--icon-arrow-narrow-down: "\ea15";
--icon-arrow-narrow-left: "\ea16";
--icon-arrow-narrow-right: "\ea17";
--icon-arrow-narrow-up: "\ea18";
--icon-arrow-right: "\ea19";
--icon-arrow-sm-down: "\ea1a";
--icon-arrow-sm-left: "\ea1b";
--icon-arrow-sm-right: "\ea1c";
--icon-arrow-sm-up: "\ea1d";
--icon-arrow-up: "\ea1e";
--icon-astro: "\ea1f";
--icon-at-symbol: "\ea20";
--icon-auth0: "\ea21";
--icon-authentik: "\ea22";
--icon-autodesk: "\ea23";
--icon-azure: "\ea24";
--icon-backspace: "\ea25";
--icon-badge-check: "\ea26";
--icon-ban: "\ea27";
--icon-beaker: "\ea28";
--icon-behance: "\ea29";
--icon-bell: "\ea2a";
--icon-bitBucket: "\ea2b";
--icon-bitly: "\ea2c";
--icon-book-open: "\ea2d";
--icon-bookmark-alt: "\ea2e";
--icon-bookmark: "\ea2f";
--icon-box: "\ea30";
--icon-briefcase: "\ea31";
--icon-bun-sh: "\ea32";
--icon-cake: "\ea33";
--icon-calculator: "\ea34";
--icon-calendar: "\ea35";
--icon-camera: "\ea36";
--icon-cash: "\ea37";
--icon-chart-bar: "\ea38";
--icon-chart-pie: "\ea39";
--icon-chart-square-bar: "\ea3a";
--icon-chat-alt-2: "\ea3b";
--icon-chat-alt: "\ea3c";
--icon-chat: "\ea3d";
--icon-check-circle: "\ea3e";
--icon-check: "\ea3f";
--icon-cheveron-down: "\ea40";
--icon-cheveron-left: "\ea41";
--icon-cheveron-right: "\ea42";
--icon-cheveron-up: "\ea43";
--icon-chevron-double-down: "\ea44";
--icon-chevron-double-left: "\ea45";
--icon-chevron-double-right: "\ea46";
--icon-chevron-double-up: "\ea47";
--icon-chip: "\ea48";
--icon-clipboard-arrow: "\ea49";
--icon-clipboard-check: "\ea4a";
--icon-clipboard-copy: "\ea4b";
--icon-clipboard-list: "\ea4c";
--icon-clock: "\ea4d";
--icon-cloud-download: "\ea4e";
--icon-cloud-upload: "\ea4f";
--icon-cloud: "\ea50";
--icon-code: "\ea51";
--icon-cog: "\ea52";
--icon-collection: "\ea53";
--icon-color-swatch: "\ea54";
--icon-cpp: "\ea55";
--icon-credit-card: "\ea56";
--icon-css3: "\ea57";
--icon-cube-transparent: "\ea58";
--icon-cube: "\ea59";
--icon-currency-bangladesh: "\ea5a";
--icon-currency-dollar: "\ea5b";
--icon-currency-euro: "\ea5c";
--icon-currency-pound: "\ea5d";
--icon-currency-rupee: "\ea5e";
--icon-currency-yen: "\ea5f";
--icon-cursor-click: "\ea60";
--icon-dailymotion: "\ea61";
--icon-dart: "\ea62";
--icon-database: "\ea63";
--icon-deno: "\ea64";
--icon-desktop-computer: "\ea65";
--icon-device-ipad: "\ea66";
--icon-device-mobile: "\ea67";
--icon-discord: "\ea68";
--icon-disqus: "\ea69";
--icon-docker: "\ea6a";
--icon-document-add: "\ea6b";
--icon-document-download: "\ea6c";
--icon-document-duplicate: "\ea6d";
--icon-document-remove: "\ea6e";
--icon-document-report: "\ea6f";
--icon-document-search: "\ea70";
--icon-document-text: "\ea71";
--icon-document: "\ea72";
--icon-dotnet: "\ea73";
--icon-dots-circle-horizontal: "\ea74";
--icon-dots-horizontal: "\ea75";
--icon-dots-vertical: "\ea76";
--icon-download: "\ea77";
--icon-dribbble: "\ea78";
--icon-dropbox: "\ea79";
--icon-duplicate: "\ea7a";
--icon-emoji-happy: "\ea7b";
--icon-emoji-sad: "\ea7c";
--icon-etsy: "\ea7d";
--icon-exclamation-circle: "\ea7e";
--icon-exclamation: "\ea7f";
--icon-external-link: "\ea80";
--icon-eye-off: "\ea81";
--icon-eye: "\ea82";
--icon-facebook: "\ea83";
--icon-fast-forward: "\ea84";
--icon-figma: "\ea85";
--icon-film: "\ea86";
--icon-filter-line: "\ea87";
--icon-filter: "\ea88";
--icon-finger-print: "\ea89";
--icon-fire: "\ea8a";
--icon-firefox: "\ea8b";
--icon-flag: "\ea8c";
--icon-flutter: "\ea8d";
--icon-folder-add: "\ea8e";
--icon-folder-download: "\ea8f";
--icon-folder-open: "\ea90";
--icon-folder-remove: "\ea91";
--icon-folder: "\ea92";
--icon-gift: "\ea93";
--icon-git-branch: "\ea94";
--icon-git-commit: "\ea95";
--icon-git: "\ea96";
--icon-github: "\ea97";
--icon-gitlab: "\ea98";
--icon-globe-alt: "\ea99";
--icon-globe: "\ea9a";
--icon-go: "\ea9b";
--icon-google: "\ea9c";
--icon-graphql: "\ea9d";
--icon-hand: "\ea9e";
--icon-hashtag: "\ea9f";
--icon-heart: "\eaa0";
--icon-home: "\eaa1";
--icon-html5: "\eaa2";
--icon-identification: "\eaa3";
--icon-inbox-in: "\eaa4";
--icon-inbox: "\eaa5";
--icon-info: "\eaa6";
--icon-instagram: "\eaa7";
--icon-ionic: "\eaa8";
--icon-ios: "\eaa9";
--icon-java: "\eaaa";
--icon-js: "\eaab";
--icon-key: "\eaac";
--icon-kotlin: "\eaad";
--icon-light-bulb: "\eaae";
--icon-lightning-bolt: "\eaaf";
--icon-link: "\eab0";
--icon-linkedin: "\eab1";
--icon-linux: "\eab2";
--icon-list: "\eab3";
--icon-location-marker: "\eab4";
--icon-lock-closed: "\eab5";
--icon-lock-open: "\eab6";
--icon-logout-left: "\eab7";
--icon-logout-right: "\eab8";
--icon-mail-open: "\eab9";
--icon-mail: "\eaba";
--icon-map: "\eabb";
--icon-md-library: "\eabc";
--icon-medium: "\eabd";
--icon-meilisearch: "\eabe";
--icon-menu-alt-1: "\eabf";
--icon-menu-alt-2: "\eac0";
--icon-menu-alt-3: "\eac1";
--icon-menu-alt-4: "\eac2";
--icon-menu: "\eac3";
--icon-microphone: "\eac4";
--icon-microsoft: "\eac5";
--icon-microsoft_edge: "\eac6";
--icon-minus-circle: "\eac7";
--icon-minus-sm: "\eac8";
--icon-minus: "\eac9";
--icon-mode: "\eaca";
--icon-mongodb: "\eacb";
--icon-moon: "\eacc";
--icon-ms_yammer: "\eacd";
--icon-msg91: "\eace";
--icon-music-note: "\eacf";
--icon-neo4j: "\ead0";
--icon-neon: "\ead1";
--icon-newspaper: "\ead2";
--icon-nextjs: "\ead3";
--icon-node_js: "\ead4";
--icon-notion: "\ead5";
--icon-null: "\ead6";
--icon-nuxt: "\ead7";
--icon-office-building: "\ead8";
--icon-okta: "\ead9";
--icon-open-ai: "\eada";
--icon-openid: "\eadb";
--icon-opera: "\eadc";
--icon-pangea: "\eadd";
--icon-paper-airplane: "\eade";
--icon-paper-clip: "\eadf";
--icon-pause: "\eae0";
--icon-paypal: "\eae1";
--icon-pencil-alt: "\eae2";
--icon-pencil: "\eae3";
--icon-perspective-api: "\eae4";
--icon-phone-incoming: "\eae5";
--icon-phone-missed-call: "\eae6";
--icon-phone-outgoing: "\eae7";
--icon-phone: "\eae8";
--icon-photograph: "\eae9";
--icon-php: "\eaea";
--icon-pinterest: "\eaeb";
--icon-play-button: "\eaec";
--icon-play: "\eaed";
--icon-plus-circle: "\eaee";
--icon-plus-sm: "\eaef";
--icon-plus: "\eaf0";
--icon-podio: "\eaf1";
--icon-presentation-chart-1: "\eaf2";
--icon-presentation-chart-2: "\eaf3";
--icon-printer: "\eaf4";
--icon-product_hunt: "\eaf5";
--icon-puzzle: "\eaf6";
--icon-python: "\eaf7";
--icon-qrcode: "\eaf8";
--icon-question-mark-circle: "\eaf9";
--icon-qwik: "\eafa";
--icon-react-native: "\eafb";
--icon-react: "\eafc";
--icon-receipt-refund: "\eafd";
--icon-receipt-tax: "\eafe";
--icon-reddit: "\eaff";
--icon-redis: "\eb00";
--icon-refresh: "\eb01";
--icon-relation: "\eb02";
--icon-relationship: "\eb03";
--icon-replay: "\eb04";
--icon-rewind: "\eb05";
--icon-rss: "\eb06";
--icon-ruby: "\eb07";
--icon-safari: "\eb08";
--icon-salesforce: "\eb09";
--icon-save-as: "\eb0a";
--icon-save: "\eb0b";
--icon-scale: "\eb0c";
--icon-scissors: "\eb0d";
--icon-search-circle: "\eb0e";
--icon-search: "\eb0f";
--icon-selector: "\eb10";
--icon-send: "\eb11";
--icon-server: "\eb12";
--icon-share: "\eb13";
--icon-shield-check: "\eb14";
--icon-shield-exclamation: "\eb15";
--icon-shopping-bag: "\eb16";
--icon-shopping-cart: "\eb17";
--icon-skype: "\eb18";
--icon-slack: "\eb19";
--icon-solidjs: "\eb1a";
--icon-sort-ascending: "\eb1b";
--icon-sort-descending: "\eb1c";
--icon-sparkles: "\eb1d";
--icon-speakerphone: "\eb1e";
--icon-spin: "\eb1f";
--icon-spotify: "\eb20";
--icon-star: "\eb21";
--icon-status-offline: "\eb22";
--icon-status-online: "\eb23";
--icon-stop: "\eb24";
--icon-stripe: "\eb25";
--icon-sun: "\eb26";
--icon-support: "\eb27";
--icon-svelte: "\eb28";
--icon-swift: "\eb29";
--icon-switch-horizontal: "\eb2a";
--icon-switch-vertical: "\eb2b";
--icon-table: "\eb2c";
--icon-tag: "\eb2d";
--icon-telegram: "\eb2e";
--icon-telesign: "\eb2f";
--icon-template: "\eb30";
--icon-terminal: "\eb31";
--icon-text: "\eb32";
--icon-textmagic: "\eb33";
--icon-thumb-dowm: "\eb34";
--icon-thumb-up: "\eb35";
--icon-ticket: "\eb36";
--icon-tiktok: "\eb37";
--icon-toggle: "\eb38";
--icon-tradeshift: "\eb39";
--icon-translate: "\eb3a";
--icon-trash: "\eb3b";
--icon-trending-down: "\eb3c";
--icon-trending-up: "\eb3d";
--icon-truck: "\eb3e";
--icon-tumbir: "\eb3f";
--icon-twilio: "\eb40";
--icon-twitch: "\eb41";
--icon-twitter: "\eb42";
--icon-typescript: "\eb43";
--icon-unity: "\eb44";
--icon-upload: "\eb45";
--icon-upstash: "\eb46";
--icon-user-add: "\eb47";
--icon-user-circle: "\eb48";
--icon-user-group: "\eb49";
--icon-user-remove: "\eb4a";
--icon-user: "\eb4b";
--icon-users: "\eb4c";
--icon-variable: "\eb4d";
--icon-video-camera: "\eb4e";
--icon-video: "\eb4f";
--icon-view-boards: "\eb50";
--icon-view-grid-add: "\eb51";
--icon-view-grid: "\eb52";
--icon-view-list: "\eb53";
--icon-vimeo: "\eb54";
--icon-vk: "\eb55";
--icon-volume-off: "\eb56";
--icon-volume-up: "\eb57";
--icon-vonage: "\eb58";
--icon-vs_code: "\eb59";
--icon-vue: "\eb5a";
--icon-whatsapp: "\eb5b";
--icon-wifi: "\eb5c";
--icon-wordpress: "\eb5d";
--icon-x-circle: "\eb5e";
--icon-x: "\eb5f";
--icon-yahoo: "\eb60";
--icon-yandex: "\eb61";
--icon-ycombinator: "\eb62";
--icon-youtube: "\eb63";
--icon-zoom-in: "\eb64";
--icon-zoom-out: "\eb65";
--icon-zoom: "\eb66";
}
.icon-academic-cap:before { content: var(--icon-academic-cap); }
.icon-adjustments:before { content: var(--icon-adjustments); }
.icon-akamai:before { content: var(--icon-akamai); }
.icon-algolia:before { content: var(--icon-algolia); }
.icon-amazon:before { content: var(--icon-amazon); }
.icon-android:before { content: var(--icon-android); }
.icon-angular:before { content: var(--icon-angular); }
.icon-annotation:before { content: var(--icon-annotation); }
.icon-anonymous:before { content: var(--icon-anonymous); }
.icon-api:before { content: var(--icon-api); }
.icon-apple:before { content: var(--icon-apple); }
.icon-appwrite:before { content: var(--icon-appwrite); }
.icon-archive:before { content: var(--icon-archive); }
.icon-arrow-circle-down:before { content: var(--icon-arrow-circle-down); }
.icon-arrow-circle-left:before { content: var(--icon-arrow-circle-left); }
.icon-arrow-circle-right:before { content: var(--icon-arrow-circle-right); }
.icon-arrow-circle-up:before { content: var(--icon-arrow-circle-up); }
.icon-arrow-down:before { content: var(--icon-arrow-down); }
.icon-arrow-expand:before { content: var(--icon-arrow-expand); }
.icon-arrow-left:before { content: var(--icon-arrow-left); }
.icon-arrow-narrow-down:before { content: var(--icon-arrow-narrow-down); }
.icon-arrow-narrow-left:before { content: var(--icon-arrow-narrow-left); }
.icon-arrow-narrow-right:before { content: var(--icon-arrow-narrow-right); }
.icon-arrow-narrow-up:before { content: var(--icon-arrow-narrow-up); }
.icon-arrow-right:before { content: var(--icon-arrow-right); }
.icon-arrow-sm-down:before { content: var(--icon-arrow-sm-down); }
.icon-arrow-sm-left:before { content: var(--icon-arrow-sm-left); }
.icon-arrow-sm-right:before { content: var(--icon-arrow-sm-right); }
.icon-arrow-sm-up:before { content: var(--icon-arrow-sm-up); }
.icon-arrow-up:before { content: var(--icon-arrow-up); }
.icon-astro:before { content: var(--icon-astro); }
.icon-at-symbol:before { content: var(--icon-at-symbol); }
.icon-auth0:before { content: var(--icon-auth0); }
.icon-authentik:before { content: var(--icon-authentik); }
.icon-autodesk:before { content: var(--icon-autodesk); }
.icon-azure:before { content: var(--icon-azure); }
.icon-backspace:before { content: var(--icon-backspace); }
.icon-badge-check:before { content: var(--icon-badge-check); }
.icon-ban:before { content: var(--icon-ban); }
.icon-beaker:before { content: var(--icon-beaker); }
.icon-behance:before { content: var(--icon-behance); }
.icon-bell:before { content: var(--icon-bell); }
.icon-bitBucket:before { content: var(--icon-bitBucket); }
.icon-bitly:before { content: var(--icon-bitly); }
.icon-book-open:before { content: var(--icon-book-open); }
.icon-bookmark-alt:before { content: var(--icon-bookmark-alt); }
.icon-bookmark:before { content: var(--icon-bookmark); }
.icon-box:before { content: var(--icon-box); }
.icon-briefcase:before { content: var(--icon-briefcase); }
.icon-bun-sh:before { content: var(--icon-bun-sh); }
.icon-cake:before { content: var(--icon-cake); }
.icon-calculator:before { content: var(--icon-calculator); }
.icon-calendar:before { content: var(--icon-calendar); }
.icon-camera:before { content: var(--icon-camera); }
.icon-cash:before { content: var(--icon-cash); }
.icon-chart-bar:before { content: var(--icon-chart-bar); }
.icon-chart-pie:before { content: var(--icon-chart-pie); }
.icon-chart-square-bar:before { content: var(--icon-chart-square-bar); }
.icon-chat-alt-2:before { content: var(--icon-chat-alt-2); }
.icon-chat-alt:before { content: var(--icon-chat-alt); }
.icon-chat:before { content: var(--icon-chat); }
.icon-check-circle:before { content: var(--icon-check-circle); }
.icon-check:before { content: var(--icon-check); }
.icon-cheveron-down:before { content: var(--icon-cheveron-down); }
.icon-cheveron-left:before { content: var(--icon-cheveron-left); }
.icon-cheveron-right:before { content: var(--icon-cheveron-right); }
.icon-cheveron-up:before { content: var(--icon-cheveron-up); }
.icon-chevron-double-down:before { content: var(--icon-chevron-double-down); }
.icon-chevron-double-left:before { content: var(--icon-chevron-double-left); }
.icon-chevron-double-right:before { content: var(--icon-chevron-double-right); }
.icon-chevron-double-up:before { content: var(--icon-chevron-double-up); }
.icon-chip:before { content: var(--icon-chip); }
.icon-clipboard-arrow:before { content: var(--icon-clipboard-arrow); }
.icon-clipboard-check:before { content: var(--icon-clipboard-check); }
.icon-clipboard-copy:before { content: var(--icon-clipboard-copy); }
.icon-clipboard-list:before { content: var(--icon-clipboard-list); }
.icon-clock:before { content: var(--icon-clock); }
.icon-cloud-download:before { content: var(--icon-cloud-download); }
.icon-cloud-upload:before { content: var(--icon-cloud-upload); }
.icon-cloud:before { content: var(--icon-cloud); }
.icon-code:before { content: var(--icon-code); }
.icon-cog:before { content: var(--icon-cog); }
.icon-collection:before { content: var(--icon-collection); }
.icon-color-swatch:before { content: var(--icon-color-swatch); }
.icon-cpp:before { content: var(--icon-cpp); }
.icon-credit-card:before { content: var(--icon-credit-card); }
.icon-css3:before { content: var(--icon-css3); }
.icon-cube-transparent:before { content: var(--icon-cube-transparent); }
.icon-cube:before { content: var(--icon-cube); }
.icon-currency-bangladesh:before { content: var(--icon-currency-bangladesh); }
.icon-currency-dollar:before { content: var(--icon-currency-dollar); }
.icon-currency-euro:before { content: var(--icon-currency-euro); }
.icon-currency-pound:before { content: var(--icon-currency-pound); }
.icon-currency-rupee:before { content: var(--icon-currency-rupee); }
.icon-currency-yen:before { content: var(--icon-currency-yen); }
.icon-cursor-click:before { content: var(--icon-cursor-click); }
.icon-dailymotion:before { content: var(--icon-dailymotion); }
.icon-dart:before { content: var(--icon-dart); }
.icon-database:before { content: var(--icon-database); }
.icon-deno:before { content: var(--icon-deno); }
.icon-desktop-computer:before { content: var(--icon-desktop-computer); }
.icon-device-ipad:before { content: var(--icon-device-ipad); }
.icon-device-mobile:before { content: var(--icon-device-mobile); }
.icon-discord:before { content: var(--icon-discord); }
.icon-disqus:before { content: var(--icon-disqus); }
.icon-docker:before { content: var(--icon-docker); }
.icon-document-add:before { content: var(--icon-document-add); }
.icon-document-download:before { content: var(--icon-document-download); }
.icon-document-duplicate:before { content: var(--icon-document-duplicate); }
.icon-document-remove:before { content: var(--icon-document-remove); }
.icon-document-report:before { content: var(--icon-document-report); }
.icon-document-search:before { content: var(--icon-document-search); }
.icon-document-text:before { content: var(--icon-document-text); }
.icon-document:before { content: var(--icon-document); }
.icon-dotnet:before { content: var(--icon-dotnet); }
.icon-dots-circle-horizontal:before { content: var(--icon-dots-circle-horizontal); }
.icon-dots-horizontal:before { content: var(--icon-dots-horizontal); }
.icon-dots-vertical:before { content: var(--icon-dots-vertical); }
.icon-download:before { content: var(--icon-download); }
.icon-dribbble:before { content: var(--icon-dribbble); }
.icon-dropbox:before { content: var(--icon-dropbox); }
.icon-duplicate:before { content: var(--icon-duplicate); }
.icon-emoji-happy:before { content: var(--icon-emoji-happy); }
.icon-emoji-sad:before { content: var(--icon-emoji-sad); }
.icon-etsy:before { content: var(--icon-etsy); }
.icon-exclamation-circle:before { content: var(--icon-exclamation-circle); }
.icon-exclamation:before { content: var(--icon-exclamation); }
.icon-external-link:before { content: var(--icon-external-link); }
.icon-eye-off:before { content: var(--icon-eye-off); }
.icon-eye:before { content: var(--icon-eye); }
.icon-facebook:before { content: var(--icon-facebook); }
.icon-fast-forward:before { content: var(--icon-fast-forward); }
.icon-figma:before { content: var(--icon-figma); }
.icon-film:before { content: var(--icon-film); }
.icon-filter-line:before { content: var(--icon-filter-line); }
.icon-filter:before { content: var(--icon-filter); }
.icon-finger-print:before { content: var(--icon-finger-print); }
.icon-fire:before { content: var(--icon-fire); }
.icon-firefox:before { content: var(--icon-firefox); }
.icon-flag:before { content: var(--icon-flag); }
.icon-flutter:before { content: var(--icon-flutter); }
.icon-folder-add:before { content: var(--icon-folder-add); }
.icon-folder-download:before { content: var(--icon-folder-download); }
.icon-folder-open:before { content: var(--icon-folder-open); }
.icon-folder-remove:before { content: var(--icon-folder-remove); }
.icon-folder:before { content: var(--icon-folder); }
.icon-gift:before { content: var(--icon-gift); }
.icon-git-branch:before { content: var(--icon-git-branch); }
.icon-git-commit:before { content: var(--icon-git-commit); }
.icon-git:before { content: var(--icon-git); }
.icon-github:before { content: var(--icon-github); }
.icon-gitlab:before { content: var(--icon-gitlab); }
.icon-globe-alt:before { content: var(--icon-globe-alt); }
.icon-globe:before { content: var(--icon-globe); }
.icon-go:before { content: var(--icon-go); }
.icon-google:before { content: var(--icon-google); }
.icon-graphql:before { content: var(--icon-graphql); }
.icon-hand:before { content: var(--icon-hand); }
.icon-hashtag:before { content: var(--icon-hashtag); }
.icon-heart:before { content: var(--icon-heart); }
.icon-home:before { content: var(--icon-home); }
.icon-html5:before { content: var(--icon-html5); }
.icon-identification:before { content: var(--icon-identification); }
.icon-inbox-in:before { content: var(--icon-inbox-in); }
.icon-inbox:before { content: var(--icon-inbox); }
.icon-info:before { content: var(--icon-info); }
.icon-instagram:before { content: var(--icon-instagram); }
.icon-ionic:before { content: var(--icon-ionic); }
.icon-ios:before { content: var(--icon-ios); }
.icon-java:before { content: var(--icon-java); }
.icon-js:before { content: var(--icon-js); }
.icon-key:before { content: var(--icon-key); }
.icon-kotlin:before { content: var(--icon-kotlin); }
.icon-light-bulb:before { content: var(--icon-light-bulb); }
.icon-lightning-bolt:before { content: var(--icon-lightning-bolt); }
.icon-link:before { content: var(--icon-link); }
.icon-linkedin:before { content: var(--icon-linkedin); }
.icon-linux:before { content: var(--icon-linux); }
.icon-list:before { content: var(--icon-list); }
.icon-location-marker:before { content: var(--icon-location-marker); }
.icon-lock-closed:before { content: var(--icon-lock-closed); }
.icon-lock-open:before { content: var(--icon-lock-open); }
.icon-logout-left:before { content: var(--icon-logout-left); }
.icon-logout-right:before { content: var(--icon-logout-right); }
.icon-mail-open:before { content: var(--icon-mail-open); }
.icon-mail:before { content: var(--icon-mail); }
.icon-map:before { content: var(--icon-map); }
.icon-md-library:before { content: var(--icon-md-library); }
.icon-medium:before { content: var(--icon-medium); }
.icon-meilisearch:before { content: var(--icon-meilisearch); }
.icon-menu-alt-1:before { content: var(--icon-menu-alt-1); }
.icon-menu-alt-2:before { content: var(--icon-menu-alt-2); }
.icon-menu-alt-3:before { content: var(--icon-menu-alt-3); }
.icon-menu-alt-4:before { content: var(--icon-menu-alt-4); }
.icon-menu:before { content: var(--icon-menu); }
.icon-microphone:before { content: var(--icon-microphone); }
.icon-microsoft:before { content: var(--icon-microsoft); }
.icon-microsoft_edge:before { content: var(--icon-microsoft_edge); }
.icon-minus-circle:before { content: var(--icon-minus-circle); }
.icon-minus-sm:before { content: var(--icon-minus-sm); }
.icon-minus:before { content: var(--icon-minus); }
.icon-mode:before { content: var(--icon-mode); }
.icon-mongodb:before { content: var(--icon-mongodb); }
.icon-moon:before { content: var(--icon-moon); }
.icon-ms_yammer:before { content: var(--icon-ms_yammer); }
.icon-msg91:before { content: var(--icon-msg91); }
.icon-music-note:before { content: var(--icon-music-note); }
.icon-neo4j:before { content: var(--icon-neo4j); }
.icon-neon:before { content: var(--icon-neon); }
.icon-newspaper:before { content: var(--icon-newspaper); }
.icon-nextjs:before { content: var(--icon-nextjs); }
.icon-node_js:before { content: var(--icon-node_js); }
.icon-notion:before { content: var(--icon-notion); }
.icon-null:before { content: var(--icon-null); }
.icon-nuxt:before { content: var(--icon-nuxt); }
.icon-office-building:before { content: var(--icon-office-building); }
.icon-okta:before { content: var(--icon-okta); }
.icon-open-ai:before { content: var(--icon-open-ai); }
.icon-openid:before { content: var(--icon-openid); }
.icon-opera:before { content: var(--icon-opera); }
.icon-pangea:before { content: var(--icon-pangea); }
.icon-paper-airplane:before { content: var(--icon-paper-airplane); }
.icon-paper-clip:before { content: var(--icon-paper-clip); }
.icon-pause:before { content: var(--icon-pause); }
.icon-paypal:before { content: var(--icon-paypal); }
.icon-pencil-alt:before { content: var(--icon-pencil-alt); }
.icon-pencil:before { content: var(--icon-pencil); }
.icon-perspective-api:before { content: var(--icon-perspective-api); }
.icon-phone-incoming:before { content: var(--icon-phone-incoming); }
.icon-phone-missed-call:before { content: var(--icon-phone-missed-call); }
.icon-phone-outgoing:before { content: var(--icon-phone-outgoing); }
.icon-phone:before { content: var(--icon-phone); }
.icon-photograph:before { content: var(--icon-photograph); }
.icon-php:before { content: var(--icon-php); }
.icon-pinterest:before { content: var(--icon-pinterest); }
.icon-play-button:before { content: var(--icon-play-button); }
.icon-play:before { content: var(--icon-play); }
.icon-plus-circle:before { content: var(--icon-plus-circle); }
.icon-plus-sm:before { content: var(--icon-plus-sm); }
.icon-plus:before { content: var(--icon-plus); }
.icon-podio:before { content: var(--icon-podio); }
.icon-presentation-chart-1:before { content: var(--icon-presentation-chart-1); }
.icon-presentation-chart-2:before { content: var(--icon-presentation-chart-2); }
.icon-printer:before { content: var(--icon-printer); }
.icon-product_hunt:before { content: var(--icon-product_hunt); }
.icon-puzzle:before { content: var(--icon-puzzle); }
.icon-python:before { content: var(--icon-python); }
.icon-qrcode:before { content: var(--icon-qrcode); }
.icon-question-mark-circle:before { content: var(--icon-question-mark-circle); }
.icon-qwik:before { content: var(--icon-qwik); }
.icon-react-native:before { content: var(--icon-react-native); }
.icon-react:before { content: var(--icon-react); }
.icon-receipt-refund:before { content: var(--icon-receipt-refund); }
.icon-receipt-tax:before { content: var(--icon-receipt-tax); }
.icon-reddit:before { content: var(--icon-reddit); }
.icon-redis:before { content: var(--icon-redis); }
.icon-refresh:before { content: var(--icon-refresh); }
.icon-relation:before { content: var(--icon-relation); }
.icon-relationship:before { content: var(--icon-relationship); }
.icon-replay:before { content: var(--icon-replay); }
.icon-rewind:before { content: var(--icon-rewind); }
.icon-rss:before { content: var(--icon-rss); }
.icon-ruby:before { content: var(--icon-ruby); }
.icon-safari:before { content: var(--icon-safari); }
.icon-salesforce:before { content: var(--icon-salesforce); }
.icon-save-as:before { content: var(--icon-save-as); }
.icon-save:before { content: var(--icon-save); }
.icon-scale:before { content: var(--icon-scale); }
.icon-scissors:before { content: var(--icon-scissors); }
.icon-search-circle:before { content: var(--icon-search-circle); }
.icon-search:before { content: var(--icon-search); }
.icon-selector:before { content: var(--icon-selector); }
.icon-send:before { content: var(--icon-send); }
.icon-server:before { content: var(--icon-server); }
.icon-share:before { content: var(--icon-share); }
.icon-shield-check:before { content: var(--icon-shield-check); }
.icon-shield-exclamation:before { content: var(--icon-shield-exclamation); }
.icon-shopping-bag:before { content: var(--icon-shopping-bag); }
.icon-shopping-cart:before { content: var(--icon-shopping-cart); }
.icon-skype:before { content: var(--icon-skype); }
.icon-slack:before { content: var(--icon-slack); }
.icon-solidjs:before { content: var(--icon-solidjs); }
.icon-sort-ascending:before { content: var(--icon-sort-ascending); }
.icon-sort-descending:before { content: var(--icon-sort-descending); }
.icon-sparkles:before { content: var(--icon-sparkles); }
.icon-speakerphone:before { content: var(--icon-speakerphone); }
.icon-spin:before { content: var(--icon-spin); }
.icon-spotify:before { content: var(--icon-spotify); }
.icon-star:before { content: var(--icon-star); }
.icon-status-offline:before { content: var(--icon-status-offline); }
.icon-status-online:before { content: var(--icon-status-online); }
.icon-stop:before { content: var(--icon-stop); }
.icon-stripe:before { content: var(--icon-stripe); }
.icon-sun:before { content: var(--icon-sun); }
.icon-support:before { content: var(--icon-support); }
.icon-svelte:before { content: var(--icon-svelte); }
.icon-swift:before { content: var(--icon-swift); }
.icon-switch-horizontal:before { content: var(--icon-switch-horizontal); }
.icon-switch-vertical:before { content: var(--icon-switch-vertical); }
.icon-table:before { content: var(--icon-table); }
.icon-tag:before { content: var(--icon-tag); }
.icon-telegram:before { content: var(--icon-telegram); }
.icon-telesign:before { content: var(--icon-telesign); }
.icon-template:before { content: var(--icon-template); }
.icon-terminal:before { content: var(--icon-terminal); }
.icon-text:before { content: var(--icon-text); }
.icon-textmagic:before { content: var(--icon-textmagic); }
.icon-thumb-dowm:before { content: var(--icon-thumb-dowm); }
.icon-thumb-up:before { content: var(--icon-thumb-up); }
.icon-ticket:before { content: var(--icon-ticket); }
.icon-tiktok:before { content: var(--icon-tiktok); }
.icon-toggle:before { content: var(--icon-toggle); }
.icon-tradeshift:before { content: var(--icon-tradeshift); }
.icon-translate:before { content: var(--icon-translate); }
.icon-trash:before { content: var(--icon-trash); }
.icon-trending-down:before { content: var(--icon-trending-down); }
.icon-trending-up:before { content: var(--icon-trending-up); }
.icon-truck:before { content: var(--icon-truck); }
.icon-tumbir:before { content: var(--icon-tumbir); }
.icon-twilio:before { content: var(--icon-twilio); }
.icon-twitch:before { content: var(--icon-twitch); }
.icon-twitter:before { content: var(--icon-twitter); }
.icon-typescript:before { content: var(--icon-typescript); }
.icon-unity:before { content: var(--icon-unity); }
.icon-upload:before { content: var(--icon-upload); }
.icon-upstash:before { content: var(--icon-upstash); }
.icon-user-add:before { content: var(--icon-user-add); }
.icon-user-circle:before { content: var(--icon-user-circle); }
.icon-user-group:before { content: var(--icon-user-group); }
.icon-user-remove:before { content: var(--icon-user-remove); }
.icon-user:before { content: var(--icon-user); }
.icon-users:before { content: var(--icon-users); }
.icon-variable:before { content: var(--icon-variable); }
.icon-video-camera:before { content: var(--icon-video-camera); }
.icon-video:before { content: var(--icon-video); }
.icon-view-boards:before { content: var(--icon-view-boards); }
.icon-view-grid-add:before { content: var(--icon-view-grid-add); }
.icon-view-grid:before { content: var(--icon-view-grid); }
.icon-view-list:before { content: var(--icon-view-list); }
.icon-vimeo:before { content: var(--icon-vimeo); }
.icon-vk:before { content: var(--icon-vk); }
.icon-volume-off:before { content: var(--icon-volume-off); }
.icon-volume-up:before { content: var(--icon-volume-up); }
.icon-vonage:before { content: var(--icon-vonage); }
.icon-vs_code:before { content: var(--icon-vs_code); }
.icon-vue:before { content: var(--icon-vue); }
.icon-whatsapp:before { content: var(--icon-whatsapp); }
.icon-wifi:before { content: var(--icon-wifi); }
.icon-wordpress:before { content: var(--icon-wordpress); }
.icon-x-circle:before { content: var(--icon-x-circle); }
.icon-x:before { content: var(--icon-x); }
.icon-yahoo:before { content: var(--icon-yahoo); }
.icon-yandex:before { content: var(--icon-yandex); }
.icon-ycombinator:before { content: var(--icon-ycombinator); }
.icon-youtube:before { content: var(--icon-youtube); }
.icon-zoom-in:before { content: var(--icon-zoom-in); }
.icon-zoom-out:before { content: var(--icon-zoom-out); }
.icon-zoom:before { content: var(--icon-zoom); }

$icon-academic-cap: "\ea01";
$icon-adjustments: "\ea02";
$icon-akamai: "\ea03";
$icon-algolia: "\ea04";
$icon-amazon: "\ea05";
$icon-android: "\ea06";
$icon-angular: "\ea07";
$icon-annotation: "\ea08";
$icon-anonymous: "\ea09";
$icon-api: "\ea0a";
$icon-apple: "\ea0b";
$icon-appwrite: "\ea0c";
$icon-archive: "\ea0d";
$icon-arrow-circle-down: "\ea0e";
$icon-arrow-circle-left: "\ea0f";
$icon-arrow-circle-right: "\ea10";
$icon-arrow-circle-up: "\ea11";
$icon-arrow-down: "\ea12";
$icon-arrow-expand: "\ea13";
$icon-arrow-left: "\ea14";
$icon-arrow-narrow-down: "\ea15";
$icon-arrow-narrow-left: "\ea16";
$icon-arrow-narrow-right: "\ea17";
$icon-arrow-narrow-up: "\ea18";
$icon-arrow-right: "\ea19";
$icon-arrow-sm-down: "\ea1a";
$icon-arrow-sm-left: "\ea1b";
$icon-arrow-sm-right: "\ea1c";
$icon-arrow-sm-up: "\ea1d";
$icon-arrow-up: "\ea1e";
$icon-astro: "\ea1f";
$icon-at-symbol: "\ea20";
$icon-auth0: "\ea21";
$icon-authentik: "\ea22";
$icon-autodesk: "\ea23";
$icon-azure: "\ea24";
$icon-backspace: "\ea25";
$icon-badge-check: "\ea26";
$icon-ban: "\ea27";
$icon-beaker: "\ea28";
$icon-behance: "\ea29";
$icon-bell: "\ea2a";
$icon-bitBucket: "\ea2b";
$icon-bitly: "\ea2c";
$icon-book-open: "\ea2d";
$icon-bookmark-alt: "\ea2e";
$icon-bookmark: "\ea2f";
$icon-box: "\ea30";
$icon-briefcase: "\ea31";
$icon-bun-sh: "\ea32";
$icon-cake: "\ea33";
$icon-calculator: "\ea34";
$icon-calendar: "\ea35";
$icon-camera: "\ea36";
$icon-cash: "\ea37";
$icon-chart-bar: "\ea38";
$icon-chart-pie: "\ea39";
$icon-chart-square-bar: "\ea3a";
$icon-chat-alt-2: "\ea3b";
$icon-chat-alt: "\ea3c";
$icon-chat: "\ea3d";
$icon-check-circle: "\ea3e";
$icon-check: "\ea3f";
$icon-cheveron-down: "\ea40";
$icon-cheveron-left: "\ea41";
$icon-cheveron-right: "\ea42";
$icon-cheveron-up: "\ea43";
$icon-chevron-double-down: "\ea44";
$icon-chevron-double-left: "\ea45";
$icon-chevron-double-right: "\ea46";
$icon-chevron-double-up: "\ea47";
$icon-chip: "\ea48";
$icon-clipboard-arrow: "\ea49";
$icon-clipboard-check: "\ea4a";
$icon-clipboard-copy: "\ea4b";
$icon-clipboard-list: "\ea4c";
$icon-clock: "\ea4d";
$icon-cloud-download: "\ea4e";
$icon-cloud-upload: "\ea4f";
$icon-cloud: "\ea50";
$icon-code: "\ea51";
$icon-cog: "\ea52";
$icon-collection: "\ea53";
$icon-color-swatch: "\ea54";
$icon-cpp: "\ea55";
$icon-credit-card: "\ea56";
$icon-css3: "\ea57";
$icon-cube-transparent: "\ea58";
$icon-cube: "\ea59";
$icon-currency-bangladesh: "\ea5a";
$icon-currency-dollar: "\ea5b";
$icon-currency-euro: "\ea5c";
$icon-currency-pound: "\ea5d";
$icon-currency-rupee: "\ea5e";
$icon-currency-yen: "\ea5f";
$icon-cursor-click: "\ea60";
$icon-dailymotion: "\ea61";
$icon-dart: "\ea62";
$icon-database: "\ea63";
$icon-deno: "\ea64";
$icon-desktop-computer: "\ea65";
$icon-device-ipad: "\ea66";
$icon-device-mobile: "\ea67";
$icon-discord: "\ea68";
$icon-disqus: "\ea69";
$icon-docker: "\ea6a";
$icon-document-add: "\ea6b";
$icon-document-download: "\ea6c";
$icon-document-duplicate: "\ea6d";
$icon-document-remove: "\ea6e";
$icon-document-report: "\ea6f";
$icon-document-search: "\ea70";
$icon-document-text: "\ea71";
$icon-document: "\ea72";
$icon-dotnet: "\ea73";
$icon-dots-circle-horizontal: "\ea74";
$icon-dots-horizontal: "\ea75";
$icon-dots-vertical: "\ea76";
$icon-download: "\ea77";
$icon-dribbble: "\ea78";
$icon-dropbox: "\ea79";
$icon-duplicate: "\ea7a";
$icon-emoji-happy: "\ea7b";
$icon-emoji-sad: "\ea7c";
$icon-etsy: "\ea7d";
$icon-exclamation-circle: "\ea7e";
$icon-exclamation: "\ea7f";
$icon-external-link: "\ea80";
$icon-eye-off: "\ea81";
$icon-eye: "\ea82";
$icon-facebook: "\ea83";
$icon-fast-forward: "\ea84";
$icon-figma: "\ea85";
$icon-film: "\ea86";
$icon-filter-line: "\ea87";
$icon-filter: "\ea88";
$icon-finger-print: "\ea89";
$icon-fire: "\ea8a";
$icon-firefox: "\ea8b";
$icon-flag: "\ea8c";
$icon-flutter: "\ea8d";
$icon-folder-add: "\ea8e";
$icon-folder-download: "\ea8f";
$icon-folder-open: "\ea90";
$icon-folder-remove: "\ea91";
$icon-folder: "\ea92";
$icon-gift: "\ea93";
$icon-git-branch: "\ea94";
$icon-git-commit: "\ea95";
$icon-git: "\ea96";
$icon-github: "\ea97";
$icon-gitlab: "\ea98";
$icon-globe-alt: "\ea99";
$icon-globe: "\ea9a";
$icon-go: "\ea9b";
$icon-google: "\ea9c";
$icon-graphql: "\ea9d";
$icon-hand: "\ea9e";
$icon-hashtag: "\ea9f";
$icon-heart: "\eaa0";
$icon-home: "\eaa1";
$icon-html5: "\eaa2";
$icon-identification: "\eaa3";
$icon-inbox-in: "\eaa4";
$icon-inbox: "\eaa5";
$icon-info: "\eaa6";
$icon-instagram: "\eaa7";
$icon-ionic: "\eaa8";
$icon-ios: "\eaa9";
$icon-java: "\eaaa";
$icon-js: "\eaab";
$icon-key: "\eaac";
$icon-kotlin: "\eaad";
$icon-light-bulb: "\eaae";
$icon-lightning-bolt: "\eaaf";
$icon-link: "\eab0";
$icon-linkedin: "\eab1";
$icon-linux: "\eab2";
$icon-list: "\eab3";
$icon-location-marker: "\eab4";
$icon-lock-closed: "\eab5";
$icon-lock-open: "\eab6";
$icon-logout-left: "\eab7";
$icon-logout-right: "\eab8";
$icon-mail-open: "\eab9";
$icon-mail: "\eaba";
$icon-map: "\eabb";
$icon-md-library: "\eabc";
$icon-medium: "\eabd";
$icon-meilisearch: "\eabe";
$icon-menu-alt-1: "\eabf";
$icon-menu-alt-2: "\eac0";
$icon-menu-alt-3: "\eac1";
$icon-menu-alt-4: "\eac2";
$icon-menu: "\eac3";
$icon-microphone: "\eac4";
$icon-microsoft: "\eac5";
$icon-microsoft_edge: "\eac6";
$icon-minus-circle: "\eac7";
$icon-minus-sm: "\eac8";
$icon-minus: "\eac9";
$icon-mode: "\eaca";
$icon-mongodb: "\eacb";
$icon-moon: "\eacc";
$icon-ms_yammer: "\eacd";
$icon-msg91: "\eace";
$icon-music-note: "\eacf";
$icon-neo4j: "\ead0";
$icon-neon: "\ead1";
$icon-newspaper: "\ead2";
$icon-nextjs: "\ead3";
$icon-node_js: "\ead4";
$icon-notion: "\ead5";
$icon-null: "\ead6";
$icon-nuxt: "\ead7";
$icon-office-building: "\ead8";
$icon-okta: "\ead9";
$icon-open-ai: "\eada";
$icon-openid: "\eadb";
$icon-opera: "\eadc";
$icon-pangea: "\eadd";
$icon-paper-airplane: "\eade";
$icon-paper-clip: "\eadf";
$icon-pause: "\eae0";
$icon-paypal: "\eae1";
$icon-pencil-alt: "\eae2";
$icon-pencil: "\eae3";
$icon-perspective-api: "\eae4";
$icon-phone-incoming: "\eae5";
$icon-phone-missed-call: "\eae6";
$icon-phone-outgoing: "\eae7";
$icon-phone: "\eae8";
$icon-photograph: "\eae9";
$icon-php: "\eaea";
$icon-pinterest: "\eaeb";
$icon-play-button: "\eaec";
$icon-play: "\eaed";
$icon-plus-circle: "\eaee";
$icon-plus-sm: "\eaef";
$icon-plus: "\eaf0";
$icon-podio: "\eaf1";
$icon-presentation-chart-1: "\eaf2";
$icon-presentation-chart-2: "\eaf3";
$icon-printer: "\eaf4";
$icon-product_hunt: "\eaf5";
$icon-puzzle: "\eaf6";
$icon-python: "\eaf7";
$icon-qrcode: "\eaf8";
$icon-question-mark-circle: "\eaf9";
$icon-qwik: "\eafa";
$icon-react-native: "\eafb";
$icon-react: "\eafc";
$icon-receipt-refund: "\eafd";
$icon-receipt-tax: "\eafe";
$icon-reddit: "\eaff";
$icon-redis: "\eb00";
$icon-refresh: "\eb01";
$icon-relation: "\eb02";
$icon-relationship: "\eb03";
$icon-replay: "\eb04";
$icon-rewind: "\eb05";
$icon-rss: "\eb06";
$icon-ruby: "\eb07";
$icon-safari: "\eb08";
$icon-salesforce: "\eb09";
$icon-save-as: "\eb0a";
$icon-save: "\eb0b";
$icon-scale: "\eb0c";
$icon-scissors: "\eb0d";
$icon-search-circle: "\eb0e";
$icon-search: "\eb0f";
$icon-selector: "\eb10";
$icon-send: "\eb11";
$icon-server: "\eb12";
$icon-share: "\eb13";
$icon-shield-check: "\eb14";
$icon-shield-exclamation: "\eb15";
$icon-shopping-bag: "\eb16";
$icon-shopping-cart: "\eb17";
$icon-skype: "\eb18";
$icon-slack: "\eb19";
$icon-solidjs: "\eb1a";
$icon-sort-ascending: "\eb1b";
$icon-sort-descending: "\eb1c";
$icon-sparkles: "\eb1d";
$icon-speakerphone: "\eb1e";
$icon-spin: "\eb1f";
$icon-spotify: "\eb20";
$icon-star: "\eb21";
$icon-status-offline: "\eb22";
$icon-status-online: "\eb23";
$icon-stop: "\eb24";
$icon-stripe: "\eb25";
$icon-sun: "\eb26";
$icon-support: "\eb27";
$icon-svelte: "\eb28";
$icon-swift: "\eb29";
$icon-switch-horizontal: "\eb2a";
$icon-switch-vertical: "\eb2b";
$icon-table: "\eb2c";
$icon-tag: "\eb2d";
$icon-telegram: "\eb2e";
$icon-telesign: "\eb2f";
$icon-template: "\eb30";
$icon-terminal: "\eb31";
$icon-text: "\eb32";
$icon-textmagic: "\eb33";
$icon-thumb-dowm: "\eb34";
$icon-thumb-up: "\eb35";
$icon-ticket: "\eb36";
$icon-tiktok: "\eb37";
$icon-toggle: "\eb38";
$icon-tradeshift: "\eb39";
$icon-translate: "\eb3a";
$icon-trash: "\eb3b";
$icon-trending-down: "\eb3c";
$icon-trending-up: "\eb3d";
$icon-truck: "\eb3e";
$icon-tumbir: "\eb3f";
$icon-twilio: "\eb40";
$icon-twitch: "\eb41";
$icon-twitter: "\eb42";
$icon-typescript: "\eb43";
$icon-unity: "\eb44";
$icon-upload: "\eb45";
$icon-upstash: "\eb46";
$icon-user-add: "\eb47";
$icon-user-circle: "\eb48";
$icon-user-group: "\eb49";
$icon-user-remove: "\eb4a";
$icon-user: "\eb4b";
$icon-users: "\eb4c";
$icon-variable: "\eb4d";
$icon-video-camera: "\eb4e";
$icon-video: "\eb4f";
$icon-view-boards: "\eb50";
$icon-view-grid-add: "\eb51";
$icon-view-grid: "\eb52";
$icon-view-list: "\eb53";
$icon-vimeo: "\eb54";
$icon-vk: "\eb55";
$icon-volume-off: "\eb56";
$icon-volume-up: "\eb57";
$icon-vonage: "\eb58";
$icon-vs_code: "\eb59";
$icon-vue: "\eb5a";
$icon-whatsapp: "\eb5b";
$icon-wifi: "\eb5c";
$icon-wordpress: "\eb5d";
$icon-x-circle: "\eb5e";
$icon-x: "\eb5f";
$icon-yahoo: "\eb60";
$icon-yandex: "\eb61";
$icon-ycombinator: "\eb62";
$icon-youtube: "\eb63";
$icon-zoom-in: "\eb64";
$icon-zoom-out: "\eb65";
$icon-zoom: "\eb66";
