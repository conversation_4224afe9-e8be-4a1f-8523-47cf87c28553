{"name": "sxe", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-database.js"}, "dependencies": {"@appwrite.io/pink-icons": "^1.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tailwindcss/postcss": "^4.1.11", "appwrite": "^18.1.1", "dotenv": "^17.2.1", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "15.4.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"prettier": "3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}}