import { databases } from "./appwrite";
import { ID, Permission, Role, Query } from "appwrite";

// Database and Collection IDs
export const DATABASE_ID = "sxe_vision_board";
export const COLLECTIONS = {
  CATEGORIES: "categories",
  GOALS: "goals",
  USER_PREFERENCES: "user_preferences"
};

// Database schema setup
export const setupDatabase = async () => {
  try {
    // Create database
    await databases.create(DATABASE_ID, "SXE Vision Board");
    console.log("Database created successfully");

    // Create Categories collection
    await databases.createCollection(
      DATABASE_ID,
      COLLECTIONS.CATEGORIES,
      "Categories",
      [
        Permission.read(Role.user("user")),
        Permission.create(Role.user("user")),
        Permission.update(Role.user("user")),
        Permission.delete(Role.user("user"))
      ]
    );

    // Create attributes for Categories collection
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "name", 100, true);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "color", 50, true);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "userId", 50, true);
    await databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "order", true);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "createdAt", true);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "updatedAt", true);

    // Create Goals collection
    await databases.createCollection(
      DATABASE_ID,
      COLLECTIONS.GOALS,
      "Goals",
      [
        Permission.read(Role.user("user")),
        Permission.create(Role.user("user")),
        Permission.update(Role.user("user")),
        Permission.delete(Role.user("user"))
      ]
    );

    // Create attributes for Goals collection
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "title", 200, true);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "description", 1000, false);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "icon", 10, true);
    await databases.createEnumAttribute(
      DATABASE_ID, 
      COLLECTIONS.GOALS, 
      "status", 
      ["not-started", "in-progress", "done", "not-good"], 
      true
    );
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "categoryId", 50, true);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "userId", 50, true);
    await databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.GOALS, "order", true);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, "createdAt", true);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, "updatedAt", true);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, "targetDate", false);

    // Create User Preferences collection
    await databases.createCollection(
      DATABASE_ID,
      COLLECTIONS.USER_PREFERENCES,
      "User Preferences",
      [
        Permission.read(Role.user("user")),
        Permission.create(Role.user("user")),
        Permission.update(Role.user("user")),
        Permission.delete(Role.user("user"))
      ]
    );

    // Create attributes for User Preferences collection
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "userId", 50, true);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "theme", 50, false);
    await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "aiSuggestions", false);
    await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "preferences", 2000, false);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "createdAt", true);
    await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "updatedAt", true);

    console.log("Database schema created successfully");
  } catch (error) {
    console.error("Error setting up database:", error);
    throw error;
  }
};

// CRUD operations for Categories
export const categoryService = {
  async create(userId, categoryData) {
    return await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.CATEGORIES,
      ID.unique(),
      {
        ...categoryData,
        userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    );
  },

  async list(userId) {
    return await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.CATEGORIES,
      [
        Query.equal("userId", userId),
        Query.orderAsc("order")
      ]
    );
  },

  async update(categoryId, updates) {
    return await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.CATEGORIES,
      categoryId,
      {
        ...updates,
        updatedAt: new Date().toISOString()
      }
    );
  },

  async delete(categoryId) {
    return await databases.deleteDocument(
      DATABASE_ID,
      COLLECTIONS.CATEGORIES,
      categoryId
    );
  }
};

// CRUD operations for Goals
export const goalService = {
  async create(userId, goalData) {
    return await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.GOALS,
      ID.unique(),
      {
        ...goalData,
        userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    );
  },

  async list(userId, categoryId = null) {
    const queries = [Query.equal("userId", userId)];
    if (categoryId) {
      queries.push(Query.equal("categoryId", categoryId));
    }
    queries.push(Query.orderAsc("order"));

    return await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.GOALS,
      queries
    );
  },

  async update(goalId, updates) {
    return await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.GOALS,
      goalId,
      {
        ...updates,
        updatedAt: new Date().toISOString()
      }
    );
  },

  async delete(goalId) {
    return await databases.deleteDocument(
      DATABASE_ID,
      COLLECTIONS.GOALS,
      goalId
    );
  }
};

// CRUD operations for User Preferences
export const userPreferencesService = {
  async create(userId, preferences) {
    return await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.USER_PREFERENCES,
      ID.unique(),
      {
        userId,
        ...preferences,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    );
  },

  async get(userId) {
    const result = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.USER_PREFERENCES,
      [Query.equal("userId", userId)]
    );
    return result.documents[0] || null;
  },

  async update(preferencesId, updates) {
    return await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.USER_PREFERENCES,
      preferencesId,
      {
        ...updates,
        updatedAt: new Date().toISOString()
      }
    );
  }
};
