// AI Service for SXE Vision Board
// This service provides AI-powered features like goal suggestions and progress analysis

class AIService {
  constructor() {
    this.goalSuggestions = {
      personal: [
        { title: "Read 12 books this year", icon: "📚", description: "Expand your knowledge and imagination" },
        { title: "Learn a new language", icon: "🗣️", description: "Broaden your communication skills" },
        { title: "Practice meditation daily", icon: "🧘", description: "Improve mental health and focus" },
        { title: "Organize digital photos", icon: "📸", description: "Preserve precious memories" },
        { title: "Write in a journal", icon: "✍️", description: "Reflect on your thoughts and experiences" },
        { title: "Learn to cook 5 new recipes", icon: "👨‍🍳", description: "Expand your culinary skills" },
        { title: "Declutter living space", icon: "🏠", description: "Create a more peaceful environment" },
        { title: "Practice gratitude daily", icon: "🙏", description: "Cultivate a positive mindset" }
      ],
      learn: [
        { title: "Complete an online course", icon: "🎓", description: "Gain new skills and knowledge" },
        { title: "Learn data analysis", icon: "📊", description: "Understand data-driven insights" },
        { title: "Master a programming language", icon: "💻", description: "Enhance your technical abilities" },
        { title: "Study UX/UI design", icon: "🎨", description: "Create better user experiences" },
        { title: "Learn digital marketing", icon: "📱", description: "Understand modern marketing strategies" },
        { title: "Study machine learning", icon: "🤖", description: "Explore AI and automation" },
        { title: "Learn video editing", icon: "🎬", description: "Create engaging visual content" },
        { title: "Study blockchain technology", icon: "⛓️", description: "Understand decentralized systems" }
      ],
      work: [
        { title: "Launch a side project", icon: "🚀", description: "Turn your ideas into reality" },
        { title: "Build a professional network", icon: "🤝", description: "Connect with industry professionals" },
        { title: "Improve public speaking", icon: "🎤", description: "Enhance your communication skills" },
        { title: "Get a professional certification", icon: "🏆", description: "Validate your expertise" },
        { title: "Mentor someone junior", icon: "👥", description: "Share your knowledge and experience" },
        { title: "Optimize workflow processes", icon: "⚡", description: "Increase productivity and efficiency" },
        { title: "Create a personal brand", icon: "⭐", description: "Establish your professional identity" },
        { title: "Learn project management", icon: "📋", description: "Lead teams more effectively" }
      ],
      finance: [
        { title: "Create an emergency fund", icon: "🛡️", description: "Build financial security" },
        { title: "Start investing in stocks", icon: "📈", description: "Grow your wealth over time" },
        { title: "Track monthly expenses", icon: "💰", description: "Understand your spending patterns" },
        { title: "Pay off credit card debt", icon: "💳", description: "Reduce financial burden" },
        { title: "Learn about cryptocurrency", icon: "₿", description: "Explore digital currencies" },
        { title: "Set up retirement savings", icon: "🏦", description: "Plan for your future" },
        { title: "Create multiple income streams", icon: "💼", description: "Diversify your earnings" },
        { title: "Learn about real estate", icon: "🏠", description: "Explore property investment" }
      ],
      weekend: [
        { title: "Plan a weekend getaway", icon: "🏖️", description: "Relax and recharge" },
        { title: "Try a new hobby", icon: "🎯", description: "Discover new interests" },
        { title: "Visit local museums", icon: "🏛️", description: "Explore culture and history" },
        { title: "Host a dinner party", icon: "🍽️", description: "Connect with friends and family" },
        { title: "Go hiking or camping", icon: "🏔️", description: "Connect with nature" },
        { title: "Learn a musical instrument", icon: "🎸", description: "Express yourself creatively" },
        { title: "Volunteer for a cause", icon: "❤️", description: "Give back to your community" },
        { title: "Start a garden", icon: "🌱", description: "Grow your own plants" }
      ]
    };

    this.motivationalQuotes = [
      "The future belongs to those who believe in the beauty of their dreams.",
      "Success is not final, failure is not fatal: it is the courage to continue that counts.",
      "The only way to do great work is to love what you do.",
      "Your limitation—it's only your imagination.",
      "Push yourself, because no one else is going to do it for you.",
      "Great things never come from comfort zones.",
      "Dream it. Wish it. Do it.",
      "Success doesn't just find you. You have to go out and get it."
    ];
  }

  // Get AI-powered goal suggestions based on category
  getSuggestions(categoryName, existingGoals = []) {
    const categoryKey = categoryName.toLowerCase();
    const suggestions = this.goalSuggestions[categoryKey] || [];
    
    // Filter out goals that already exist
    const existingTitles = existingGoals.map(goal => goal.title.toLowerCase());
    const filteredSuggestions = suggestions.filter(
      suggestion => !existingTitles.includes(suggestion.title.toLowerCase())
    );

    // Return random 3-5 suggestions
    const shuffled = filteredSuggestions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(5, shuffled.length));
  }

  // Analyze progress and provide insights
  analyzeProgress(goals) {
    const total = goals.length;
    if (total === 0) {
      return {
        message: "Start by adding some goals to track your progress!",
        suggestion: "Break down your big dreams into smaller, actionable goals.",
        motivation: this.getRandomQuote()
      };
    }

    const completed = goals.filter(g => g.status === 'done').length;
    const inProgress = goals.filter(g => g.status === 'in-progress').length;
    const notGood = goals.filter(g => g.status === 'not-good').length;
    const completionRate = (completed / total) * 100;

    let message, suggestion;

    if (completionRate >= 80) {
      message = "🎉 Excellent progress! You're crushing your goals!";
      suggestion = "Consider setting more ambitious goals to keep challenging yourself.";
    } else if (completionRate >= 60) {
      message = "👍 Great work! You're making solid progress.";
      suggestion = "Focus on the goals that need attention to boost your completion rate.";
    } else if (completionRate >= 40) {
      message = "📈 You're on the right track, keep pushing forward!";
      suggestion = "Try breaking down larger goals into smaller, manageable tasks.";
    } else if (completionRate >= 20) {
      message = "💪 Every journey starts with a single step.";
      suggestion = "Focus on 1-2 goals at a time to avoid feeling overwhelmed.";
    } else {
      message = "🌱 It's time to take action on your goals!";
      suggestion = "Start with the easiest goal to build momentum.";
    }

    if (notGood > 0) {
      suggestion += ` You have ${notGood} goal(s) that need attention - consider revising or breaking them down.`;
    }

    return {
      message,
      suggestion,
      motivation: this.getRandomQuote(),
      stats: {
        total,
        completed,
        inProgress,
        notGood,
        completionRate: Math.round(completionRate)
      }
    };
  }

  // Smart categorization suggestions
  suggestCategory(goalTitle) {
    const title = goalTitle.toLowerCase();
    
    // Work-related keywords
    if (title.includes('work') || title.includes('job') || title.includes('career') || 
        title.includes('business') || title.includes('project') || title.includes('meeting') ||
        title.includes('client') || title.includes('professional')) {
      return 'work';
    }
    
    // Learning keywords
    if (title.includes('learn') || title.includes('study') || title.includes('course') ||
        title.includes('skill') || title.includes('education') || title.includes('training') ||
        title.includes('read') || title.includes('book')) {
      return 'learn';
    }
    
    // Finance keywords
    if (title.includes('money') || title.includes('save') || title.includes('invest') ||
        title.includes('budget') || title.includes('financial') || title.includes('bank') ||
        title.includes('debt') || title.includes('income')) {
      return 'finance';
    }
    
    // Weekend/leisure keywords
    if (title.includes('fun') || title.includes('hobby') || title.includes('travel') ||
        title.includes('vacation') || title.includes('weekend') || title.includes('relax') ||
        title.includes('entertainment') || title.includes('game')) {
      return 'weekend';
    }
    
    // Default to personal
    return 'personal';
  }

  // Get a random motivational quote
  getRandomQuote() {
    return this.motivationalQuotes[Math.floor(Math.random() * this.motivationalQuotes.length)];
  }

  // Generate goal recommendations based on user's current goals
  getPersonalizedRecommendations(allGoals, categoryName) {
    const analysis = this.analyzeProgress(allGoals);
    const suggestions = this.getSuggestions(categoryName, allGoals);
    
    return {
      analysis,
      suggestions: suggestions.slice(0, 3), // Limit to 3 suggestions
      quote: this.getRandomQuote()
    };
  }
}

export const aiService = new AIService();
export default aiService;
