"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Sparkles } from "lucide-react";
import GoalCard from "./GoalCard";
import AddGoalModal from "./AddGoalModal";
import AISuggestions from "./AISuggestions";
import { goalService } from "@/lib/database";

const Category = ({ category, userId }) => {
  const [goals, setGoals] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [loading, setLoading] = useState(true);

  // Sample goals based on the vision board image
  const sampleGoals = {
    personal: [
      { id: "1", title: "Documents", status: "in-progress", icon: "📄" },
      { id: "2", title: "Family", status: "not-good", icon: "👨‍👩‍👧‍👦" },
      { id: "3", title: "Exercise", status: "not-good", icon: "💪" },
      { id: "4", title: "Diet", status: "not-good", icon: "🥗" },
      { id: "5", title: "Prayers", status: "not-good", icon: "🙏" },
    ],
    learn: [
      { id: "6", title: "Coding", status: "done", icon: "💻" },
      { id: "7", title: "Design", status: "done", icon: "🎨" },
      { id: "8", title: "Business", status: "done", icon: "💼" },
      { id: "9", title: "Marketing", status: "done", icon: "📈" },
      { id: "10", title: "Finance", status: "done", icon: "💰" },
    ],
    work: [
      { id: "11", title: "Trading", status: "in-progress", icon: "📊" },
      { id: "12", title: "Real Estate", status: "in-progress", icon: "🏠" },
      { id: "13", title: "Digital Marketing", status: "in-progress", icon: "📱" },
      { id: "14", title: "Hubcv", status: "not-good", icon: "🔗" },
      { id: "15", title: "Codelude", status: "not-good", icon: "⚡" },
    ],
    finance: [
      { id: "16", title: "Net Worth", status: "in-progress", icon: "🏛️" },
      { id: "17", title: "Loans & Credits", status: "not-good", icon: "💳" },
      { id: "18", title: "Assets", status: "not-started", icon: "💎" },
      { id: "19", title: "Charity", status: "not-started", icon: "❤️" },
      { id: "20", title: "Investments", status: "not-started", icon: "💹" },
    ],
    weekend: [
      { id: "21", title: "Memories", status: "in-progress", icon: "📸" },
      { id: "22", title: "Travels", status: "in-progress", icon: "✈️" },
      { id: "23", title: "Shopping", status: "not-good", icon: "🛍️" },
      { id: "24", title: "Branding", status: "not-started", icon: "⭐" },
      { id: "25", title: "Events", status: "not-started", icon: "🎉" },
    ],
  };

  useEffect(() => {
    loadGoals();
  }, [category.id]);



  const loadGoals = async () => {
    try {
      setLoading(true);

      // Try to load goals from database
      try {
        const result = await goalService.list(userId, category.$id || category.id);
        if (result.documents.length > 0) {
          setGoals(result.documents);
        } else {
          // If no goals exist, create sample goals for this category
          await createSampleGoals();
        }
      } catch (dbError) {
        console.log("Database not ready, using sample data:", dbError);
        const categoryGoals = sampleGoals[category.id] || [];
        setGoals(categoryGoals);
      }
    } catch (error) {
      console.error("Error loading goals:", error);
    } finally {
      setLoading(false);
    }
  };

  const createSampleGoals = async () => {
    try {
      const categoryGoals = sampleGoals[category.id] || sampleGoals[category.name?.toLowerCase()] || [];
      const createdGoals = [];

      for (let i = 0; i < categoryGoals.length; i++) {
        const goal = categoryGoals[i];
        try {
          const created = await goalService.create(userId, {
            title: goal.title,
            icon: goal.icon,
            status: goal.status,
            categoryId: category.$id || category.id,
            order: i
          });
          createdGoals.push(created);
        } catch (error) {
          console.error("Error creating sample goal:", error);
          // If database creation fails, use sample data
          createdGoals.push(goal);
        }
      }
      setGoals(createdGoals);
    } catch (error) {
      console.error("Error creating sample goals:", error);
      const categoryGoals = sampleGoals[category.id] || [];
      setGoals(categoryGoals);
    }
  };

  const addGoal = async (newGoal) => {
    try {
      const created = await goalService.create(userId, {
        ...newGoal,
        status: "not-started",
        categoryId: category.$id || category.id,
        order: goals.length
      });
      setGoals([...goals, created]);
    } catch (error) {
      console.error("Error adding goal:", error);
      // Fallback to local state
      const goal = {
        id: Date.now().toString(),
        ...newGoal,
        status: "not-started",
      };
      setGoals([...goals, goal]);
    }
  };

  const updateGoal = async (goalId, updates) => {
    try {
      await goalService.update(goalId, updates);
      setGoals(goals.map(goal =>
        (goal.$id || goal.id) === goalId ? { ...goal, ...updates } : goal
      ));
    } catch (error) {
      console.error("Error updating goal:", error);
      // Fallback to local state update
      setGoals(goals.map(goal =>
        (goal.$id || goal.id) === goalId ? { ...goal, ...updates } : goal
      ));
    }
  };

  const deleteGoal = async (goalId) => {
    try {
      await goalService.delete(goalId);
      setGoals(goals.filter(goal => (goal.$id || goal.id) !== goalId));
    } catch (error) {
      console.error("Error deleting goal:", error);
      // Fallback to local state update
      setGoals(goals.filter(goal => (goal.$id || goal.id) !== goalId));
    }
  };

  return (
    <div className={`${category.color} rounded-lg p-4 min-h-[400px] shadow-lg hover:shadow-xl transition-shadow duration-300`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-white">{category.name}</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowAISuggestions(true)}
            className="text-white hover:bg-white/20 rounded-full p-1 transition-colors"
            title="AI Suggestions"
          >
            <Sparkles size={18} />
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="text-white hover:bg-white/20 rounded-full p-1 transition-colors"
            title="Add Goal"
          >
            <Plus size={20} />
          </button>
        </div>
      </div>

      <div className="space-y-3">
        <AnimatePresence>
          {goals.map((goal, index) => (
            <motion.div
              key={goal.$id || goal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <GoalCard
                goal={goal}
                onUpdate={updateGoal}
                onDelete={deleteGoal}
              />
            </motion.div>
          ))}
        </AnimatePresence>

        {goals.length === 0 && !loading && (
          <div className="text-white/70 text-center py-8">
            <p>No goals yet</p>
            <button
              onClick={() => setShowAddModal(true)}
              className="mt-2 text-white/90 hover:text-white underline"
            >
              Add your first goal
            </button>
          </div>
        )}
      </div>

      {/* Add New Goal Button at bottom */}
      <div className="mt-4 pt-2 border-t border-white/20">
        <button
          onClick={() => setShowAddModal(true)}
          className="w-full flex items-center justify-center space-x-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg py-2 transition-colors"
        >
          <Plus size={16} />
          <span className="text-sm">New goal</span>
        </button>
      </div>

      <AddGoalModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={addGoal}
        categoryName={category.name}
      />

      <AISuggestions
        categoryName={category.name}
        existingGoals={goals}
        onAddGoal={addGoal}
        isOpen={showAISuggestions}
        onClose={() => setShowAISuggestions(false)}
      />
    </div>
  );
};

export default Category;
