"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MoreVertical, Edit2, Trash2 } from "lucide-react";

const GoalCard = ({ goal, onUpdate, onDelete, isDragging = false }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(goal.title);

  const getStatusColor = (status) => {
    switch (status) {
      case "done":
        return "bg-green-500";
      case "in-progress":
        return "bg-blue-500";
      case "not-good":
        return "bg-red-500";
      case "not-started":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "done":
        return "Done";
      case "in-progress":
        return "In progress";
      case "not-good":
        return "Not Good";
      case "not-started":
        return "Not started";
      default:
        return "Not started";
    }
  };

  const statusOptions = [
    { value: "not-started", label: "Not started", color: "bg-gray-500" },
    { value: "in-progress", label: "In progress", color: "bg-blue-500" },
    { value: "done", label: "Done", color: "bg-green-500" },
    { value: "not-good", label: "Not Good", color: "bg-red-500" },
  ];

  const handleStatusChange = (newStatus) => {
    onUpdate(goal.$id || goal.id, { status: newStatus });
    setShowMenu(false);
  };

  const handleSaveEdit = () => {
    if (editTitle.trim()) {
      onUpdate(goal.$id || goal.id, { title: editTitle.trim() });
      setIsEditing(false);
    }
  };

  const handleCancelEdit = () => {
    setEditTitle(goal.title);
    setIsEditing(false);
  };

  return (
    <motion.div
      className={`bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 relative group cursor-grab active:cursor-grabbing ${
        isDragging ? 'shadow-lg ring-2 ring-blue-400' : ''
      }`}
      whileHover={!isDragging ? { scale: 1.02 } : {}}
      whileTap={!isDragging ? { scale: 0.98 } : {}}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1">
          <span className="text-2xl">{goal.icon}</span>
          
          {isEditing ? (
            <div className="flex-1">
              <input
                type="text"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="w-full px-2 py-1 border rounded text-sm"
                onKeyPress={(e) => {
                  if (e.key === "Enter") handleSaveEdit();
                  if (e.key === "Escape") handleCancelEdit();
                }}
                onBlur={handleSaveEdit}
                autoFocus
              />
            </div>
          ) : (
            <div className="flex-1">
              <h3 className="font-medium text-gray-800 text-sm">{goal.title}</h3>
              <p className="text-xs text-gray-500 mt-1">{getStatusText(goal.status)}</p>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <div
              className={`w-3 h-3 rounded-full ${getStatusColor(goal.status)} cursor-pointer`}
              onClick={() => setShowMenu(!showMenu)}
              title={getStatusText(goal.status)}
            />
            
            {showMenu && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="absolute right-0 top-6 bg-white rounded-lg shadow-lg border z-10 py-1 min-w-[120px]"
              >
                {statusOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleStatusChange(option.value)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center space-x-2 text-sm"
                  >
                    <div className={`w-3 h-3 rounded-full ${option.color}`} />
                    <span>{option.label}</span>
                  </button>
                ))}
              </motion.div>
            )}
          </div>

          <div className="relative">
            <button
              onClick={() => setShowMenu(false)}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <MoreVertical size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* Quick action buttons */}
      <div className="flex justify-end space-x-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <button
          onClick={() => setIsEditing(true)}
          className="text-gray-400 hover:text-blue-600 p-1 rounded hover:bg-blue-50 transition-colors"
          title="Edit"
        >
          <Edit2 size={12} />
        </button>
        <button
          onClick={() => onDelete(goal.$id || goal.id)}
          className="text-gray-400 hover:text-red-600 p-1 rounded hover:bg-red-50 transition-colors"
          title="Delete"
        >
          <Trash2 size={12} />
        </button>
      </div>
    </motion.div>
  );
};

export default GoalCard;
