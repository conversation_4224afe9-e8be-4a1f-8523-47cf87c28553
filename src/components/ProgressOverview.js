"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { goalService } from "@/lib/database";

const ProgressOverview = ({ userId }) => {
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    notStarted: 0,
    notGood: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userId) {
      loadStats();
    }
  }, [userId]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const result = await goalService.list(userId);
      const goals = result.documents;

      const newStats = {
        total: goals.length,
        completed: goals.filter(g => g.status === 'done').length,
        inProgress: goals.filter(g => g.status === 'in-progress').length,
        notStarted: goals.filter(g => g.status === 'not-started').length,
        notGood: goals.filter(g => g.status === 'not-good').length
      };

      setStats(newStats);
    } catch (error) {
      console.error("Error loading stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const completionPercentage = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

  if (loading || stats.total === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/10 backdrop-blur-md rounded-lg p-6 mb-8 border border-white/20"
    >
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white">Progress Overview</h2>
        <div className="text-2xl font-bold text-white">{completionPercentage}%</div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-white/20 rounded-full h-3 mb-4">
        <motion.div
          className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${completionPercentage}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
        />
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">{stats.completed}</div>
          <div className="text-sm text-white/70">Completed</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{stats.inProgress}</div>
          <div className="text-sm text-white/70">In Progress</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-400">{stats.notStarted}</div>
          <div className="text-sm text-white/70">Not Started</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-400">{stats.notGood}</div>
          <div className="text-sm text-white/70">Needs Attention</div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProgressOverview;
