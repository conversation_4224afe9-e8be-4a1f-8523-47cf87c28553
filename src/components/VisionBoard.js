"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import Category from "./Category";
import ProgressOverview from "./ProgressOverview";
import AIInsights from "./AIInsights";
import { categoryService, goalService, DATABASE_ID, COLLECTIONS } from "@/lib/database";
import useRealtime from "@/hooks/useRealtime";

const VisionBoard = ({ userId }) => {
  const [categories, setCategories] = useState([]);
  const [allGoals, setAllGoals] = useState([]);
  const [loading, setLoading] = useState(true);

  // Default categories based on the vision board image
  const defaultCategories = [
    { id: "personal", name: "Personal", color: "bg-purple-600" },
    { id: "learn", name: "Learn", color: "bg-orange-600" },
    { id: "work", name: "Work", color: "bg-yellow-600" },
    { id: "finance", name: "Finance", color: "bg-blue-600" },
    { id: "weekend", name: "Weekend", color: "bg-pink-600" },
  ];

  // Real-time update handlers
  const handleCategoryUpdate = useCallback((payload, events) => {
    const isCreate = events.some(e => e.includes('.create'));
    const isUpdate = events.some(e => e.includes('.update'));
    const isDelete = events.some(e => e.includes('.delete'));

    if (isCreate) {
      setCategories(prev => [...prev, payload]);
    } else if (isUpdate) {
      setCategories(prev => prev.map(cat =>
        cat.$id === payload.$id ? payload : cat
      ));
    } else if (isDelete) {
      setCategories(prev => prev.filter(cat => cat.$id !== payload.$id));
    }
  }, []);

  // Set up real-time subscriptions
  useRealtime(userId, handleCategoryUpdate, null);

  useEffect(() => {
    if (userId) {
      loadCategories();
      loadAllGoals();
    }
  }, [userId]);

  const loadAllGoals = async () => {
    try {
      const result = await goalService.list(userId);
      setAllGoals(result.documents);
    } catch (error) {
      console.error("Error loading all goals:", error);
    }
  };



  const loadCategories = async () => {
    try {
      setLoading(true);

      // Try to load categories from database
      try {
        const result = await categoryService.list(userId);
        if (result.documents.length > 0) {
          setCategories(result.documents);
        } else {
          // If no categories exist, create default ones
          await createDefaultCategories();
        }
      } catch (dbError) {
        console.log("Database not ready, using default categories:", dbError);
        setCategories(defaultCategories);
      }
    } catch (error) {
      console.error("Error loading categories:", error);
      setCategories(defaultCategories);
    } finally {
      setLoading(false);
    }
  };

  const createDefaultCategories = async () => {
    try {
      const createdCategories = [];
      for (let i = 0; i < defaultCategories.length; i++) {
        const category = defaultCategories[i];
        const created = await categoryService.create(userId, {
          name: category.name,
          color: category.color,
          order: i
        });
        createdCategories.push(created);
      }
      setCategories(createdCategories);
    } catch (error) {
      console.error("Error creating default categories:", error);
      setCategories(defaultCategories);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-7xl mx-auto"
      >
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold text-white mb-2">
            SXE Vision Board
          </h1>
          <p className="text-white/70 text-lg">
            Visualize your goals, track your progress, achieve your dreams
          </p>
        </div>

        <ProgressOverview userId={userId} />

        <AIInsights goals={allGoals} userId={userId} />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {categories.map((category, index) => (
            <motion.div
              key={category.$id || category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Category
                category={category}
                userId={userId}
              />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default VisionBoard;
