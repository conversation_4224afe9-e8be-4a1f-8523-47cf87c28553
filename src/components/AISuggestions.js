"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Lightbulb, Plus, X, Sparkles } from "lucide-react";
import { aiService } from "@/lib/aiService";

const AISuggestions = ({ categoryName, existingGoals, onAddGoal, isOpen, onClose }) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);

  useEffect(() => {
    if (isOpen && categoryName) {
      loadSuggestions();
    }
  }, [isOpen, categoryName, existingGoals]);

  const loadSuggestions = async () => {
    setLoading(true);
    try {
      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const aiSuggestions = aiService.getSuggestions(categoryName, existingGoals);
      setSuggestions(aiSuggestions);
    } catch (error) {
      console.error("Error loading AI suggestions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSuggestion = (suggestion) => {
    onAddGoal({
      title: suggestion.title,
      icon: suggestion.icon,
      description: suggestion.description
    });
    onClose();
  };

  const handleCustomizeAndAdd = () => {
    if (selectedSuggestion) {
      handleAddSuggestion(selectedSuggestion);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Sparkles className="text-purple-600" size={24} />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">
                    AI Goal Suggestions
                  </h2>
                  <p className="text-gray-600">
                    Smart recommendations for your {categoryName} category
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <X size={24} />
              </button>
            </div>

            {loading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
                <p className="text-gray-600">AI is analyzing your goals...</p>
              </div>
            ) : suggestions.length > 0 ? (
              <div className="space-y-4">
                <div className="grid gap-4">
                  {suggestions.map((suggestion, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        selectedSuggestion === suggestion
                          ? "border-purple-500 bg-purple-50"
                          : "border-gray-200 hover:border-purple-300 hover:bg-gray-50"
                      }`}
                      onClick={() => setSelectedSuggestion(suggestion)}
                    >
                      <div className="flex items-start space-x-3">
                        <span className="text-2xl">{suggestion.icon}</span>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-800 mb-1">
                            {suggestion.title}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {suggestion.description}
                          </p>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddSuggestion(suggestion);
                          }}
                          className="p-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors"
                          title="Add this goal"
                        >
                          <Plus size={18} />
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {selectedSuggestion && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-purple-50 border border-purple-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{selectedSuggestion.icon}</span>
                        <div>
                          <h4 className="font-semibold text-purple-800">
                            {selectedSuggestion.title}
                          </h4>
                          <p className="text-purple-600 text-sm">
                            Selected for addition
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={handleCustomizeAndAdd}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                      >
                        Add Goal
                      </button>
                    </div>
                  </motion.div>
                )}

                <div className="flex justify-between items-center pt-4 border-t">
                  <div className="flex items-center space-x-2 text-gray-500">
                    <Lightbulb size={16} />
                    <span className="text-sm">
                      Powered by AI • {suggestions.length} suggestions found
                    </span>
                  </div>
                  <button
                    onClick={loadSuggestions}
                    className="text-purple-600 hover:text-purple-700 text-sm font-medium"
                  >
                    Refresh Suggestions
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <Lightbulb className="mx-auto text-gray-400 mb-4" size={48} />
                <p className="text-gray-600">
                  No new suggestions available for this category.
                </p>
                <button
                  onClick={loadSuggestions}
                  className="mt-4 text-purple-600 hover:text-purple-700 font-medium"
                >
                  Try Again
                </button>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AISuggestions;
