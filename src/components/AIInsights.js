"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Brain, TrendingUp, Target, Quote } from "lucide-react";
import { aiService } from "@/lib/aiService";

const AIInsights = ({ goals, userId }) => {
  const [insights, setInsights] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (goals && goals.length >= 0) {
      generateInsights();
    }
  }, [goals]);

  const generateInsights = async () => {
    setLoading(true);
    try {
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const analysis = aiService.analyzeProgress(goals);
      setInsights(analysis);
    } catch (error) {
      console.error("Error generating insights:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white"
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="animate-spin">
            <Brain size={24} />
          </div>
          <h3 className="text-lg font-semibold">AI is analyzing your progress...</h3>
        </div>
        <div className="animate-pulse">
          <div className="h-4 bg-white/20 rounded mb-2"></div>
          <div className="h-4 bg-white/20 rounded w-3/4"></div>
        </div>
      </motion.div>
    );
  }

  if (!insights) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white mb-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <Brain size={24} />
        <h3 className="text-lg font-semibold">AI Insights</h3>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Progress Analysis */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <TrendingUp size={18} />
            <h4 className="font-medium">Progress Analysis</h4>
          </div>
          <p className="text-white/90 mb-3">{insights.message}</p>
          
          {insights.stats && (
            <div className="bg-white/10 rounded-lg p-3">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-white/70">Completion Rate:</span>
                  <div className="font-bold text-lg">{insights.stats.completionRate}%</div>
                </div>
                <div>
                  <span className="text-white/70">Total Goals:</span>
                  <div className="font-bold text-lg">{insights.stats.total}</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Suggestions */}
        <div>
          <div className="flex items-center space-x-2 mb-3">
            <Target size={18} />
            <h4 className="font-medium">Smart Suggestions</h4>
          </div>
          <p className="text-white/90 text-sm mb-3">{insights.suggestion}</p>
          
          {/* Motivational Quote */}
          <div className="bg-white/10 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <Quote size={16} className="mt-1 text-white/70" />
              <p className="text-sm italic text-white/90">
                "{insights.motivation}"
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Recommendations */}
      {insights.stats && insights.stats.notGood > 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-4 p-3 bg-yellow-500/20 border border-yellow-400/30 rounded-lg"
        >
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-yellow-300">⚠️</span>
            <span className="font-medium text-yellow-100">Attention Needed</span>
          </div>
          <p className="text-yellow-100 text-sm">
            You have {insights.stats.notGood} goal(s) marked as "Not Good". 
            Consider breaking them down into smaller tasks or adjusting your approach.
          </p>
        </motion.div>
      )}

      {/* Quick Actions */}
      <div className="mt-4 flex flex-wrap gap-2">
        <button
          onClick={generateInsights}
          className="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full text-sm transition-colors"
        >
          Refresh Analysis
        </button>
        {insights.stats && insights.stats.completionRate >= 80 && (
          <span className="bg-green-500/30 px-3 py-1 rounded-full text-sm">
            🎉 Goal Crusher!
          </span>
        )}
        {insights.stats && insights.stats.inProgress > 0 && (
          <span className="bg-blue-500/30 px-3 py-1 rounded-full text-sm">
            💪 {insights.stats.inProgress} In Progress
          </span>
        )}
      </div>
    </motion.div>
  );
};

export default AIInsights;
