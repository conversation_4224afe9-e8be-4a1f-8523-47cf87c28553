"use client";

import { useEffect, useCallback } from "react";
import { client } from "@/lib/appwrite";
import { DATABASE_ID, COLLECTIONS } from "@/lib/database";

export const useRealtime = (userId, onCategoryUpdate, onGoalUpdate) => {
  const handleRealtimeUpdate = useCallback((response) => {
    const { events, payload } = response;
    
    // Handle category updates
    if (events.some(event => event.includes(`databases.${DATABASE_ID}.collections.${COLLECTIONS.CATEGORIES}`))) {
      if (payload.userId === userId) {
        onCategoryUpdate && onCategoryUpdate(payload, events);
      }
    }
    
    // Handle goal updates
    if (events.some(event => event.includes(`databases.${DATABASE_ID}.collections.${COLLECTIONS.GOALS}`))) {
      if (payload.userId === userId) {
        onGoalUpdate && onGoalUpdate(payload, events);
      }
    }
  }, [userId, onCategoryUpdate, onGoalUpdate]);

  useEffect(() => {
    if (!userId) return;

    // Subscribe to category changes
    const categorySubscription = client.subscribe(
      `databases.${DATABASE_ID}.collections.${COLLECTIONS.CATEGORIES}.documents`,
      handleRealtimeUpdate
    );

    // Subscribe to goal changes
    const goalSubscription = client.subscribe(
      `databases.${DATABASE_ID}.collections.${COLLECTIONS.GOALS}.documents`,
      handleRealtimeUpdate
    );

    return () => {
      categorySubscription();
      goalSubscription();
    };
  }, [userId, handleRealtimeUpdate]);
};

export default useRealtime;
