#!/usr/bin/env node

/**
 * Database Setup Script for SXE Vision Board
 * 
 * This script sets up the Appwrite database schema for the SXE vision board app.
 * Run this script once to initialize the database structure.
 * 
 * Usage: node scripts/setup-database.js
 */

import { Client, Databases, ID, Permission, Role } from "appwrite";
import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: ".env.local" });

const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY); // You'll need to add this to .env.local

const databases = new Databases(client);

// Database and Collection IDs
const DATABASE_ID = "sxe_vision_board";
const COLLECTIONS = {
  CATEGORIES: "categories",
  GOALS: "goals",
  USER_PREFERENCES: "user_preferences"
};

async function setupDatabase() {
  try {
    console.log("🚀 Setting up SXE Vision Board database...");

    // Create database
    try {
      await databases.create(DATABASE_ID, "SXE Vision Board");
      console.log("✅ Database created successfully");
    } catch (error) {
      if (error.code === 409) {
        console.log("ℹ️  Database already exists");
      } else {
        throw error;
      }
    }

    // Create Categories collection
    try {
      await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        "Categories",
        [
          Permission.read(Role.any()),
          Permission.create(Role.users()),
          Permission.update(Role.users()),
          Permission.delete(Role.users())
        ]
      );
      console.log("✅ Categories collection created");

      // Create attributes for Categories collection
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "name", 100, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "color", 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "userId", 50, true);
      await databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "order", true);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "createdAt", true);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.CATEGORIES, "updatedAt", true);
      
      console.log("✅ Categories attributes created");
    } catch (error) {
      if (error.code === 409) {
        console.log("ℹ️  Categories collection already exists");
      } else {
        throw error;
      }
    }

    // Create Goals collection
    try {
      await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.GOALS,
        "Goals",
        [
          Permission.read(Role.any()),
          Permission.create(Role.users()),
          Permission.update(Role.users()),
          Permission.delete(Role.users())
        ]
      );
      console.log("✅ Goals collection created");

      // Create attributes for Goals collection
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "title", 200, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "description", 1000, false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "icon", 10, true);
      await databases.createEnumAttribute(
        DATABASE_ID, 
        COLLECTIONS.GOALS, 
        "status", 
        ["not-started", "in-progress", "done", "not-good"], 
        true
      );
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "categoryId", 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.GOALS, "userId", 50, true);
      await databases.createIntegerAttribute(DATABASE_ID, COLLECTIONS.GOALS, "order", true);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, "createdAt", true);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, "updatedAt", true);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.GOALS, "targetDate", false);
      
      console.log("✅ Goals attributes created");
    } catch (error) {
      if (error.code === 409) {
        console.log("ℹ️  Goals collection already exists");
      } else {
        throw error;
      }
    }

    // Create User Preferences collection
    try {
      await databases.createCollection(
        DATABASE_ID,
        COLLECTIONS.USER_PREFERENCES,
        "User Preferences",
        [
          Permission.read(Role.any()),
          Permission.create(Role.users()),
          Permission.update(Role.users()),
          Permission.delete(Role.users())
        ]
      );
      console.log("✅ User Preferences collection created");

      // Create attributes for User Preferences collection
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "userId", 50, true);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "theme", 50, false);
      await databases.createBooleanAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "aiSuggestions", false);
      await databases.createStringAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "preferences", 2000, false);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "createdAt", true);
      await databases.createDatetimeAttribute(DATABASE_ID, COLLECTIONS.USER_PREFERENCES, "updatedAt", true);
      
      console.log("✅ User Preferences attributes created");
    } catch (error) {
      if (error.code === 409) {
        console.log("ℹ️  User Preferences collection already exists");
      } else {
        throw error;
      }
    }

    console.log("🎉 Database setup completed successfully!");
    console.log("\nNext steps:");
    console.log("1. Update your components to use the database");
    console.log("2. Test the application");
    console.log("3. Add sample data if needed");

  } catch (error) {
    console.error("❌ Error setting up database:", error);
    process.exit(1);
  }
}

// Run the setup
setupDatabase();
